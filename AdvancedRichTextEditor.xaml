<UserControl x:Class="像素喵笔记.AdvancedRichTextEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:像素喵笔记"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- 引入Figma样式 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid x:Name="mainGrid" Background="White">
        <Grid.RowDefinitions>
            <!-- 内容编辑区域 -->
            <RowDefinition Height="*"/>
            <!-- 状态栏区域 -->
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>



        <!-- 主要内容区域 -->
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <!-- 主编辑区域 -->
                <ColumnDefinition Width="*"/>
                <!-- 媒体管理侧边栏 -->
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 内容编辑区域 -->
            <Border Grid.Column="0" Background="White" Margin="0">
                <Grid x:Name="contentGrid">
                    <!-- 文档编辑区域 -->
                    <ScrollViewer x:Name="documentScrollViewer" 
                                  VerticalScrollBarVisibility="Auto" 
                                  HorizontalScrollBarVisibility="Auto"
                                  Padding="40,20">
                        <Grid x:Name="documentContainer" 
                              Background="White" 
                              MinHeight="600"
                              MaxWidth="800">
                            <!-- 文档内容将在这里动态添加 -->
                            <RichTextBox x:Name="richTextEditor"
                                         BorderThickness="0"
                                         Background="Transparent"
                                         FontFamily="Segoe UI, Segoe UI Emoji, Segoe UI Symbol, Arial Unicode MS"
                                         FontSize="14"
                                         AcceptsReturn="True"
                                         AcceptsTab="True"
                                         VerticalScrollBarVisibility="Disabled"
                                         HorizontalScrollBarVisibility="Disabled"
                                         SelectionChanged="RichTextEditor_SelectionChanged"
                                         PreviewMouseRightButtonDown="RichTextEditor_PreviewMouseRightButtonDown"
                                         ContextMenuOpening="RichTextEditor_ContextMenuOpening"
                                         TextOptions.TextFormattingMode="Display"
                                         TextOptions.TextRenderingMode="Auto">

                                <!-- 自定义右键菜单 -->
                                <RichTextBox.ContextMenu>
                                    <ContextMenu x:Name="richTextContextMenu" Style="{StaticResource FigmaContextMenuStyle}">
                                        <MenuItem Header="撤销" Click="Undo_Click" Style="{StaticResource FigmaMenuItemStyle}">
                                            <MenuItem.Icon>
                                                <TextBlock Text="↶" FontFamily="Segoe UI Symbol" FontSize="14"/>
                                            </MenuItem.Icon>
                                        </MenuItem>
                                        <MenuItem Header="重做" Click="Redo_Click" Style="{StaticResource FigmaMenuItemStyle}">
                                            <MenuItem.Icon>
                                                <TextBlock Text="↷" FontFamily="Segoe UI Symbol" FontSize="14"/>
                                            </MenuItem.Icon>
                                        </MenuItem>
                                        <Separator Style="{StaticResource FigmaSeparatorStyle}"/>
                                        <MenuItem Header="剪切" Click="Cut_Click" Style="{StaticResource FigmaMenuItemStyle}">
                                            <MenuItem.Icon>
                                                <TextBlock Text="✂" FontFamily="Segoe UI Emoji" FontSize="14"/>
                                            </MenuItem.Icon>
                                        </MenuItem>
                                        <MenuItem Header="复制" Click="Copy_Click" Style="{StaticResource FigmaMenuItemStyle}">
                                            <MenuItem.Icon>
                                                <TextBlock Text="📋" FontFamily="Segoe UI Emoji" FontSize="14"/>
                                            </MenuItem.Icon>
                                        </MenuItem>
                                        <MenuItem Header="粘贴" Click="Paste_Click" Style="{StaticResource FigmaMenuItemStyle}">
                                            <MenuItem.Icon>
                                                <TextBlock Text="📄" FontFamily="Segoe UI Emoji" FontSize="14"/>
                                            </MenuItem.Icon>
                                        </MenuItem>
                                        <Separator Style="{StaticResource FigmaSeparatorStyle}"/>
                                        <MenuItem Header="全选" Click="SelectAll_Click" Style="{StaticResource FigmaMenuItemStyle}">
                                            <MenuItem.Icon>
                                                <TextBlock Text="🔘" FontFamily="Segoe UI Emoji" FontSize="14"/>
                                            </MenuItem.Icon>
                                        </MenuItem>
                                        <Separator Style="{StaticResource FigmaSeparatorStyle}"/>
                                        <MenuItem Header="字体设置" Click="FontSettings_Click" Style="{StaticResource FigmaMenuItemStyle}">
                                            <MenuItem.Icon>
                                                <TextBlock Text="🔤" FontFamily="Segoe UI Emoji" FontSize="14"/>
                                            </MenuItem.Icon>
                                        </MenuItem>
                                        <MenuItem Header="插入表格" Click="InsertTable_Click" Style="{StaticResource FigmaMenuItemStyle}">
                                            <MenuItem.Icon>
                                                <TextBlock Text="📊" FontFamily="Segoe UI Emoji" FontSize="14"/>
                                            </MenuItem.Icon>
                                        </MenuItem>
                                    </ContextMenu>
                                </RichTextBox.ContextMenu>
                                <RichTextBox.Document>
                                    <FlowDocument PageWidth="760">
                                        <Paragraph>
                                            <Run Text="开始编写您的内容..." Foreground="#999"/>
                                        </Paragraph>
                                    </FlowDocument>
                                </RichTextBox.Document>
                            </RichTextBox>
                        </Grid>
                    </ScrollViewer>

                    <!-- 格式化菜单容器 -->
                    <Canvas x:Name="formattingMenuCanvas" IsHitTestVisible="False"/>

                    <!-- 媒体编辑覆盖层 -->
                    <Canvas x:Name="mediaEditingOverlay"
                            IsHitTestVisible="True"
                            Visibility="Collapsed"
                            Background="Transparent"/>

                </Grid>
            </Border>

            <!-- 媒体管理侧边栏 -->
            <Border Grid.Column="1" 
                    x:Name="mediaSidebar"
                    Width="280" 
                    Background="#F8F9FA" 
                    BorderBrush="#E0E0E0" 
                    BorderThickness="1,0,0,0"
                    Visibility="Collapsed">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 侧边栏标题 -->
                    <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="16,12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="媒体管理器" FontWeight="SemiBold" FontSize="14"/>
                            <Button Grid.Column="1" x:Name="btnCloseSidebar" Style="{StaticResource FigmaButtonStyle}" 
                                    Width="24" Height="24" Click="BtnCloseSidebar_Click">
                                <Path Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" 
                                      Fill="#666" Width="12" Height="12"/>
                            </Button>
                        </Grid>
                    </Border>

                    <!-- 媒体文件列表 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
                        <StackPanel x:Name="mediaListPanel">
                            <!-- 媒体文件项目将在这里动态添加 -->
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="1"
                Background="#F8F9FA"
                BorderBrush="#E0E0E0"
                BorderThickness="0,1,0,0"
                Padding="16,8">
            <TextBlock x:Name="statusText" Text="就绪" FontSize="12" Foreground="#666"/>
        </Border>

        <!-- 表格操作工具栏 - 浮动显示在编辑器中间下方 -->
        <Border x:Name="tableToolbar"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="1"
                CornerRadius="20"
                Padding="12,8"
                Visibility="Collapsed"
                HorizontalAlignment="Center"
                VerticalAlignment="Bottom"
                Margin="0,0,0,80"
                Effect="{StaticResource StandardFigmaShadow}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="insertRowButton"
                        Width="36" Height="36"
                        ToolTip="插入行"
                        Click="InsertRowButton_Click"
                        Margin="0,0,8,0"
                        Background="#0099FF"
                        BorderThickness="0"
                        Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="#0099FF"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="8"
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#007ACC"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#005A99"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <TextBlock Text="+" FontSize="16" FontWeight="Bold" Foreground="White"/>
                </Button>

                <Button x:Name="insertColumnButton"
                        Width="36" Height="36"
                        ToolTip="插入列"
                        Click="InsertColumnButton_Click"
                        Margin="0,0,8,0"
                        Background="#0099FF"
                        BorderThickness="0"
                        Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="#0099FF"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="8"
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#007ACC"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#005A99"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <TextBlock Text="||" FontSize="12" FontWeight="Bold" Foreground="White"/>
                </Button>

                <Button x:Name="deleteRowButton"
                        Width="36" Height="36"
                        ToolTip="删除行"
                        Click="DeleteRowButton_Click"
                        Margin="0,0,8,0"
                        Background="#FF4444"
                        BorderThickness="0"
                        Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="#FF4444"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="8"
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#FF2222"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#CC0000"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <TextBlock Text="−" FontSize="16" FontWeight="Bold" Foreground="White"/>
                </Button>

                <Button x:Name="deleteColumnButton"
                        Width="36" Height="36"
                        ToolTip="删除列"
                        Click="DeleteColumnButton_Click"
                        Background="#FF4444"
                        BorderThickness="0"
                        Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Background" Value="#FF4444"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="8"
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#FF2222"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#CC0000"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <TextBlock Text="⫿" FontSize="12" FontWeight="Bold" Foreground="White"/>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
