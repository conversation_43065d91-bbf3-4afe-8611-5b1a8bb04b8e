using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Windows.Threading;
using Microsoft.Win32;

namespace 像素喵笔记
{
    /// <summary>
    /// AdvancedRichTextEditor.xaml 的交互逻辑
    /// 高级富文本编辑器，支持媒体文件、表格、多栏布局等功能
    /// </summary>
    public partial class AdvancedRichTextEditor : UserControl
    {
        #region 私有字段

        private MediaManager _mediaManager = null!;
        private PageNode? _currentPageNode;
        private TextFormattingMenu? _currentFormattingMenu;

        private List<MediaClipboardData> _mediaClipboard = new List<MediaClipboardData>();

        // 媒体编辑模式相关字段
        private FrameworkElement? _selectedMediaElement;
        private bool _isInMediaEditMode = false;
        private MediaEditingOverlay? _currentEditingOverlay;

        #endregion

        #region 公共事件

        /// <summary>
        /// 内容变化事件
        /// </summary>
        public event EventHandler? ContentChanged;

        /// <summary>
        /// 媒体插入事件 - 触发实时保存
        /// </summary>
        public event EventHandler? MediaInserted;

        /// <summary>
        /// 表格操作事件 - 触发实时保存
        /// </summary>
        public event EventHandler? TableModified;

        /// <summary>
        /// 格式化操作事件 - 触发实时保存
        /// </summary>
        public event EventHandler? FormattingChanged;

        /// <summary>
        /// 统计信息更新事件
        /// </summary>
        public event EventHandler? StatisticsUpdated;

        /// <summary>
        /// 当前节点数据 - 用于媒体文件复制
        /// </summary>
        public TreeNodeData? CurrentNodeData { get; set; }

        /// <summary>
        /// 文档图片管理器 - 新的简单实现
        /// </summary>
        private DocumentImageManager? _documentImageManager;

        /// <summary>
        /// 媒体文件添加事件
        /// </summary>
        public event EventHandler<MediaFileEventArgs>? MediaFileAdded;

        /// <summary>
        /// 媒体文件删除事件
        /// </summary>
        public event EventHandler<MediaFileEventArgs>? MediaFileDeleted;

        #endregion

        #region 构造函数

        public AdvancedRichTextEditor()
        {
            InitializeComponent();
            InitializeEditor();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化编辑器
        /// </summary>
        private void InitializeEditor()
        {
            try
            {
                // 初始化媒体管理器
                _mediaManager = new MediaManager();

                // 绑定工具栏事件
                BindToolbarEvents();

                // 绑定编辑器事件
                BindEditorEvents();

                // 初始化键盘快捷键
                InitializeKeyboardShortcuts();

                System.Diagnostics.Debug.WriteLine("AdvancedRichTextEditor 初始化完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化编辑器失败: {ex.Message}");
                MessageBox.Show($"初始化编辑器失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        /// <summary>
        /// 绑定工具栏事件 - 已禁用，工具栏已移至MainWindow
        /// </summary>
        private void BindToolbarEvents()
        {
            // 工具栏已移至MainWindow，这里不再需要绑定事件
        }

        /// <summary>
        /// 绑定编辑器事件
        /// </summary>
        private void BindEditorEvents()
        {
            richTextEditor.TextChanged += RichTextEditor_TextChanged;
            richTextEditor.SelectionChanged += RichTextEditor_SelectionChanged;
            richTextEditor.PreviewKeyDown += RichTextEditor_PreviewKeyDown;

            // 拖拽支持
            richTextEditor.AllowDrop = true;
            richTextEditor.Drop += RichTextEditor_Drop;
            richTextEditor.DragOver += RichTextEditor_DragOver;

            // 媒体元素拖拽保护
            richTextEditor.PreviewDragEnter += RichTextEditor_PreviewDragEnter;
            richTextEditor.PreviewDragLeave += RichTextEditor_PreviewDragLeave;
            richTextEditor.PreviewDrop += RichTextEditor_PreviewDrop;
        }

        /// <summary>
        /// 初始化键盘快捷键
        /// </summary>
        private void InitializeKeyboardShortcuts()
        {
            // 添加键盘快捷键支持
            this.KeyDown += AdvancedRichTextEditor_KeyDown;
        }

        #endregion

        #region 工具栏事件处理

        private void BtnBold_Click(object sender, RoutedEventArgs e)
        {
            ApplyTextFormat(TextFormatType.Bold);
        }

        private void BtnItalic_Click(object sender, RoutedEventArgs e)
        {
            ApplyTextFormat(TextFormatType.Italic);
        }

        private void BtnUnderline_Click(object sender, RoutedEventArgs e)
        {
            ApplyTextFormat(TextFormatType.Underline);
        }

        private void BtnImportFiles_Click(object sender, RoutedEventArgs e)
        {
            ImportFiles();
        }

        private void BtnInsertImage_Click(object sender, RoutedEventArgs e)
        {
            InsertImage();
        }

        // 🔧 删除音频和视频插入按钮事件处理器

        private void BtnInsertTable_Click(object sender, RoutedEventArgs e)
        {
            InsertTable();
        }



        private void BtnSingleColumn_Click(object sender, RoutedEventArgs e)
        {
            SetColumnLayout(1);
        }

        private void BtnTwoColumns_Click(object sender, RoutedEventArgs e)
        {
            SetColumnLayout(2);
        }

        private void BtnMediaManager_Click(object sender, RoutedEventArgs e)
        {
            ToggleMediaSidebar();
        }

        private void BtnCloseSidebar_Click(object sender, RoutedEventArgs e)
        {
            mediaSidebar.Visibility = Visibility.Collapsed;
        }

        #endregion

        #region 编辑器事件处理

        private void RichTextEditor_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 更新PageNode统计信息
            if (_currentPageNode != null)
            {
                UpdatePageNodeStatistics();
            }

            // 触发内容变化事件
            ContentChanged?.Invoke(this, EventArgs.Empty);
        }

        private void RichTextEditor_SelectionChanged(object sender, RoutedEventArgs e)
        {
            // 在媒体编辑模式下，限制选择操作
            if (_isInMediaEditMode)
            {
                // 清除文本选择，保持焦点在当前媒体元素
                richTextEditor.Selection.Select(richTextEditor.Selection.Start, richTextEditor.Selection.Start);
                return;
            }

            try
            {
                // 更新格式化按钮
                UpdateFormattingButtons();

                // 优化：只在选择状态真正改变时更新格式化菜单
                var hasSelection = !richTextEditor.Selection.IsEmpty;
                var menuVisible = _currentFormattingMenu != null && _currentFormattingMenu.Visibility == Visibility.Visible;

                if (hasSelection && !menuVisible)
                {
                    ShowFormattingMenu();
                }
                else if (!hasSelection && menuVisible)
                {
                    HideFormattingMenu();
                }

                // 检测表格选择
                CheckTableSelection();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"选择变化处理失败: {ex.Message}");
            }
        }

        private void RichTextEditor_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            // 处理特殊键盘输入
            HandleSpecialKeys(e);
        }

        private void AdvancedRichTextEditor_KeyDown(object sender, KeyEventArgs e)
        {
            // 处理ESC键退出媒体编辑模式
            if (e.Key == Key.Escape && _isInMediaEditMode)
            {
                ExitMediaEditMode();
                e.Handled = true;
                return;
            }

            // 在媒体编辑模式下，禁用其他快捷键
            if (_isInMediaEditMode)
            {
                e.Handled = true;
                return;
            }

            // 处理快捷键
            if (Keyboard.Modifiers == ModifierKeys.Control)
            {
                switch (e.Key)
                {
                    case Key.B:
                        ApplyTextFormat(TextFormatType.Bold);
                        e.Handled = true;
                        break;
                    case Key.I:
                        ApplyTextFormat(TextFormatType.Italic);
                        e.Handled = true;
                        break;
                    case Key.U:
                        ApplyTextFormat(TextFormatType.Underline);
                        e.Handled = true;
                        break;
                }
            }
        }

        #endregion

        #region 拖拽支持

        private void RichTextEditor_DragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                e.Effects = DragDropEffects.Copy;
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
            e.Handled = true;
        }

        private void RichTextEditor_Drop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                foreach (string file in files)
                {
                    InsertMediaFile(file);
                }
            }
            e.Handled = true;
        }

        /// <summary>
        /// 媒体元素拖拽保护 - 预览拖拽进入
        /// </summary>
        private void RichTextEditor_PreviewDragEnter(object sender, DragEventArgs e)
        {
            try
            {
                // 检查是否拖拽的是媒体元素
                if (IsMediaElementBeingDragged(e))
                {
                    e.Effects = DragDropEffects.None;
                    e.Handled = true;
                    System.Diagnostics.Debug.WriteLine("阻止媒体元素拖拽进入");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"预览拖拽进入处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 媒体元素拖拽保护 - 预览拖拽离开
        /// </summary>
        private void RichTextEditor_PreviewDragLeave(object sender, DragEventArgs e)
        {
            try
            {
                // 检查是否拖拽的是媒体元素
                if (IsMediaElementBeingDragged(e))
                {
                    e.Effects = DragDropEffects.None;
                    e.Handled = true;
                    System.Diagnostics.Debug.WriteLine("阻止媒体元素拖拽离开");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"预览拖拽离开处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 媒体元素拖拽保护 - 预览放置
        /// </summary>
        private void RichTextEditor_PreviewDrop(object sender, DragEventArgs e)
        {
            try
            {
                // 检查是否拖拽的是媒体元素
                if (IsMediaElementBeingDragged(e))
                {
                    e.Effects = DragDropEffects.None;
                    e.Handled = true;
                    System.Diagnostics.Debug.WriteLine("阻止媒体元素拖拽放置");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"预览拖拽放置处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否正在拖拽媒体元素
        /// </summary>
        private bool IsMediaElementBeingDragged(DragEventArgs e)
        {
            try
            {
                // 检查拖拽数据中是否包含媒体元素标识
                if (e.Data.GetDataPresent("MediaElement"))
                {
                    return true;
                }

                // 检查是否从RichTextBox内部拖拽
                var source = e.Source as FrameworkElement;
                while (source != null)
                {
                    if (source.Tag?.ToString() == "MediaElement")
                    {
                        return true;
                    }
                    source = source.Parent as FrameworkElement;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查媒体元素拖拽失败: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region 右键菜单事件处理

        /// <summary>
        /// 右键鼠标按下预览事件处理
        /// </summary>
        private void RichTextEditor_PreviewMouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 简化处理，不再检测媒体元素
            // 表格现在有自己的工具栏，不需要特殊的右键菜单处理
        }

        /// <summary>
        /// 右键菜单打开事件处理
        /// </summary>
        private void RichTextEditor_ContextMenuOpening(object sender, ContextMenuEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 右键菜单打开 ===");

                // 获取鼠标位置
                var mousePosition = Mouse.GetPosition(richTextEditor);
                System.Diagnostics.Debug.WriteLine($"鼠标位置: {mousePosition}");

                // 检查是否点击了媒体元素
                var mediaElement = GetElementAtPosition(mousePosition);

                if (mediaElement != null)
                {
                    System.Diagnostics.Debug.WriteLine($"检测到媒体元素: {mediaElement.GetType().Name}");

                    // 取消默认菜单
                    e.Handled = true;

                    // 直接显示简单的媒体菜单
                    ShowSimpleMediaMenu(mediaElement as FrameworkElement, mousePosition);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("未检测到媒体元素，显示默认菜单");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"右键菜单处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示统一Figma风格的媒体菜单
        /// </summary>
        private void ShowSimpleMediaMenu(FrameworkElement? mediaElement, Point position)
        {
            if (mediaElement == null) return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"显示统一Figma风格媒体菜单: {mediaElement.GetType().Name}");

                // 创建右键菜单并应用统一样式
                var contextMenu = new ContextMenu();

                // 应用项目中定义的Figma风格样式
                try
                {
                    if (Application.Current.TryFindResource("FigmaContextMenuStyle") is Style contextMenuStyle)
                    {
                        contextMenu.Style = contextMenuStyle;
                        System.Diagnostics.Debug.WriteLine("成功应用FigmaContextMenuStyle");
                    }
                }
                catch (Exception styleEx)
                {
                    System.Diagnostics.Debug.WriteLine($"无法应用FigmaContextMenuStyle: {styleEx.Message}");
                }

                // 创建编辑模式菜单项
                var editItem = CreateFigmaMenuItem("编辑模式", "✏️", () =>
                {
                    System.Diagnostics.Debug.WriteLine("编辑模式被点击");
                    try
                    {
                        EnterMediaEditMode(mediaElement);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"进入编辑模式失败: {ex.Message}");
                    }
                });
                contextMenu.Items.Add(editItem);

                // 添加分隔符
                contextMenu.Items.Add(new Separator());

                // 创建删除媒体菜单项（危险操作，使用红色）
                var deleteItem = CreateFigmaMenuItem("删除媒体", "🗑️", () =>
                {
                    System.Diagnostics.Debug.WriteLine("删除媒体被点击");
                    try
                    {
                        // 强制在UI线程上执行删除操作
                        Dispatcher.BeginInvoke(new Action(() =>
                        {
                            DeleteMediaElement(mediaElement);
                        }), System.Windows.Threading.DispatcherPriority.Normal);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"删除媒体失败: {ex.Message}");
                    }
                }, true); // 标记为危险操作
                contextMenu.Items.Add(deleteItem);

                // 显示菜单
                contextMenu.PlacementTarget = richTextEditor;
                contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint;
                contextMenu.IsOpen = true;

                System.Diagnostics.Debug.WriteLine($"统一Figma风格媒体菜单已显示，菜单项数量: {contextMenu.Items.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示统一Figma风格媒体菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建Figma风格的菜单项（与MainWindow中的方法保持一致）
        /// </summary>
        private MenuItem CreateFigmaMenuItem(string text, string icon, Action action, bool isDangerous = false)
        {
            var menuItem = new MenuItem();

            // 应用Figma样式
            try
            {
                if (Application.Current.TryFindResource("FigmaMenuItemStyle") is Style menuItemStyle)
                {
                    menuItem.Style = menuItemStyle;
                }
            }
            catch (Exception styleEx)
            {
                System.Diagnostics.Debug.WriteLine($"无法应用FigmaMenuItemStyle: {styleEx.Message}");
            }

            // 创建内容容器
            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 图标
            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 14,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center,
                Width = 20
            };

            // 文本
            var textBlock = new TextBlock
            {
                Text = text,
                FontSize = 13,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 如果是危险操作，使用红色
            if (isDangerous)
            {
                iconText.Foreground = new SolidColorBrush(Color.FromRgb(239, 68, 68));
                textBlock.Foreground = new SolidColorBrush(Color.FromRgb(239, 68, 68));
            }

            stackPanel.Children.Add(iconText);
            stackPanel.Children.Add(textBlock);

            menuItem.Header = stackPanel;
            menuItem.Click += (s, e) => action?.Invoke();

            return menuItem;
        }

        /// <summary>
        /// 重新扫描并修复文档中的媒体元素关联
        /// </summary>
        public void RepairMediaElementAssociations()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始修复媒体元素关联 ===");

                var document = richTextEditor.Document;
                if (document == null)
                {
                    System.Diagnostics.Debug.WriteLine("文档为空，跳过修复");
                    return;
                }

                RepairBlockMediaElements(document.Blocks);

                System.Diagnostics.Debug.WriteLine("=== 媒体元素关联修复完成 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"修复媒体元素关联失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归修复Block中的媒体元素
        /// </summary>
        private void RepairBlockMediaElements(BlockCollection blocks)
        {
            foreach (var block in blocks)
            {
                if (block is Paragraph paragraph)
                {
                    RepairInlineMediaElements(paragraph.Inlines);
                }
                else if (block is List list)
                {
                    foreach (var listItem in list.ListItems)
                    {
                        RepairBlockMediaElements(listItem.Blocks);
                    }
                }
                else if (block is Table table)
                {
                    foreach (var rowGroup in table.RowGroups)
                    {
                        foreach (var row in rowGroup.Rows)
                        {
                            foreach (var cell in row.Cells)
                            {
                                RepairBlockMediaElements(cell.Blocks);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 修复Inline中的媒体元素
        /// </summary>
        private void RepairInlineMediaElements(InlineCollection inlines)
        {
            foreach (var inline in inlines)
            {
                if (inline is InlineUIContainer container && container.Child != null)
                {
                    var child = container.Child;
                    System.Diagnostics.Debug.WriteLine($"发现InlineUIContainer，子元素类型: {child.GetType().Name}");

                    if (IsMediaElement(child))
                    {
                        System.Diagnostics.Debug.WriteLine($"修复媒体元素关联: {child.GetType().Name}");

                        // 重新建立关联关系
                        child.SetValue(FrameworkElement.DataContextProperty, container);
                        child.SetValue(FrameworkElement.TagProperty, "MediaElement");

                        // 特别处理ResizableImageControl，确保图片路径正确
                        if (child is ResizableImageControl imageControl)
                        {
                            var imagePath = imageControl.ImageFilePath;
                            if (!string.IsNullOrEmpty(imagePath) && File.Exists(imagePath))
                            {
                                System.Diagnostics.Debug.WriteLine($"恢复图片路径: {imagePath}");
                                // 重新加载图片以确保显示正确
                                imageControl.LoadImageFile(imagePath);
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"警告：图片路径无效或文件不存在: {imagePath}");
                            }
                        }

                        System.Diagnostics.Debug.WriteLine("媒体元素关联已修复");
                    }
                }
                else if (inline is Span span)
                {
                    RepairInlineMediaElements(span.Inlines);
                }
            }
        }

        private Table? _currentSelectedTable;

        /// <summary>
        /// 检测光标位置是否在表格中
        /// </summary>
        private void CheckTableSelection()
        {
            try
            {
                // 延迟检测，避免频繁触发
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    var caretPosition = richTextEditor.CaretPosition;
                    var table = FindTableAtPosition(caretPosition);

                    if (table != null && table != _currentSelectedTable)
                    {
                        _currentSelectedTable = table;
                        ShowTableToolbar(table);
                        System.Diagnostics.Debug.WriteLine("检测到表格，显示工具栏");
                    }
                    else if (table == null && _currentSelectedTable != null)
                    {
                        _currentSelectedTable = null;
                        HideTableToolbar();
                        System.Diagnostics.Debug.WriteLine("离开表格，隐藏工具栏");
                    }
                }), System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检测表格选择失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 在指定位置查找表格
        /// </summary>
        private Table? FindTableAtPosition(TextPointer position)
        {
            try
            {
                // 检查光标是否在表格单元格内
                var element = position.Parent;
                int maxDepth = 15; // 增加查找深度
                int currentDepth = 0;

                bool isInTableCell = false;
                Table? foundTable = null;

                while (element != null && currentDepth < maxDepth)
                {
                    // 检查是否在TableCell内
                    if (element is TableCell)
                    {
                        isInTableCell = true;
                        System.Diagnostics.Debug.WriteLine($"光标在表格单元格内，深度: {currentDepth}");
                    }

                    // 查找Table元素
                    if (element is Table table)
                    {
                        foundTable = table;
                        System.Diagnostics.Debug.WriteLine($"找到表格，深度: {currentDepth}");
                        break;
                    }

                    // 获取父元素
                    if (element is FrameworkContentElement contentElement)
                    {
                        element = contentElement.Parent;
                    }
                    else if (element is FrameworkElement frameworkElement)
                    {
                        element = frameworkElement.Parent;
                    }
                    else if (element is DependencyObject depObj)
                    {
                        element = LogicalTreeHelper.GetParent(depObj);
                    }
                    else
                    {
                        break;
                    }

                    currentDepth++;
                }

                // 只有当光标在表格单元格内时才返回表格
                if (isInTableCell && foundTable != null)
                {
                    System.Diagnostics.Debug.WriteLine("确认光标在表格单元格内");
                    return foundTable;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找表格失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 显示表格工具栏
        /// </summary>
        private void ShowTableToolbar(Table table)
        {
            try
            {
                if (tableToolbar != null)
                {
                    // 工具栏位置已在XAML中设置为居中底部，直接显示即可
                    tableToolbar.Visibility = Visibility.Visible;
                    System.Diagnostics.Debug.WriteLine("表格工具栏已显示");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示表格工具栏失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 隐藏表格工具栏
        /// </summary>
        private void HideTableToolbar()
        {
            try
            {
                if (tableToolbar != null)
                {
                    tableToolbar.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"隐藏表格工具栏失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 插入行按钮点击事件
        /// </summary>
        private void InsertRowButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentSelectedTable != null)
                {
                    InsertTableRow(_currentSelectedTable);
                    statusText.Text = "已插入新行";

                    // 触发表格修改事件，用于实时保存
                    TableModified?.Invoke(this, EventArgs.Empty);
                    System.Diagnostics.Debug.WriteLine("表格行插入完成，触发实时保存");
                }
            }
            catch (Exception ex)
            {
                statusText.Text = $"插入行失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"插入行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 插入列按钮点击事件
        /// </summary>
        private void InsertColumnButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentSelectedTable != null)
                {
                    InsertTableColumn(_currentSelectedTable);
                    statusText.Text = "已插入新列";

                    // 触发表格修改事件，用于实时保存
                    TableModified?.Invoke(this, EventArgs.Empty);
                    System.Diagnostics.Debug.WriteLine("表格列插入完成，触发实时保存");
                }
            }
            catch (Exception ex)
            {
                statusText.Text = $"插入列失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"插入列失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除行按钮点击事件
        /// </summary>
        private void DeleteRowButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentSelectedTable != null)
                {
                    DeleteTableRow(_currentSelectedTable);
                    statusText.Text = "已删除行";

                    // 触发表格修改事件，用于实时保存
                    TableModified?.Invoke(this, EventArgs.Empty);
                    System.Diagnostics.Debug.WriteLine("表格行删除完成，触发实时保存");
                }
            }
            catch (Exception ex)
            {
                statusText.Text = $"删除行失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"删除行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除列按钮点击事件
        /// </summary>
        private void DeleteColumnButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentSelectedTable != null)
                {
                    DeleteTableColumn(_currentSelectedTable);
                    statusText.Text = "已删除列";

                    // 触发表格修改事件，用于实时保存
                    TableModified?.Invoke(this, EventArgs.Empty);
                    System.Diagnostics.Debug.WriteLine("表格列删除完成，触发实时保存");
                }
            }
            catch (Exception ex)
            {
                statusText.Text = $"删除列失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"删除列失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 显示媒体元素的右键菜单
        /// </summary>
        private void ShowMediaElementContextMenu(FrameworkElement mediaElement, Point position)
        {
            try
            {
                if (mediaElement.ContextMenu != null)
                {
                    mediaElement.ContextMenu.PlacementTarget = richTextEditor;
                    mediaElement.ContextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint;
                    mediaElement.ContextMenu.IsOpen = true;
                    System.Diagnostics.Debug.WriteLine($"显示媒体元素右键菜单: {mediaElement.GetType().Name}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"媒体元素没有右键菜单: {mediaElement.GetType().Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示媒体元素右键菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 撤销操作
        /// </summary>
        private void Undo_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (richTextEditor.CanUndo)
                {
                    richTextEditor.Undo();
                    statusText.Text = "已撤销操作";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"撤销操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 重做操作
        /// </summary>
        private void Redo_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (richTextEditor.CanRedo)
                {
                    richTextEditor.Redo();
                    statusText.Text = "已重做操作";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重做操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 剪切操作
        /// </summary>
        private void Cut_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (CutSelectedContent())
                {
                    statusText.Text = "已剪切到剪贴板";
                }
                else
                {
                    richTextEditor.Cut();
                    statusText.Text = "已剪切到剪贴板";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"剪切操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制操作
        /// </summary>
        private void Copy_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (CopySelectedContent())
                {
                    statusText.Text = "已复制到剪贴板";
                }
                else
                {
                    richTextEditor.Copy();
                    statusText.Text = "已复制到剪贴板";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"复制操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 粘贴操作
        /// </summary>
        private void Paste_Click(object sender, RoutedEventArgs e)
        {
            HandlePasteCommand();
        }

        /// <summary>
        /// 处理粘贴命令 - 供外部调用
        /// </summary>
        public void HandlePasteCommand()
        {
            try
            {
                // 首先检查系统剪贴板是否包含图片
                if (System.Windows.Clipboard.ContainsImage())
                {
                    System.Diagnostics.Debug.WriteLine("检测到剪贴板中有图片，开始处理截图粘贴");
                    HandleClipboardImagePaste();
                    return;
                }

                // 然后尝试粘贴内部媒体内容
                if (PasteContent())
                {
                    // 如果成功粘贴了媒体内容，就不再调用系统粘贴
                    if (statusText != null)
                    {
                        statusText.Text = "已粘贴媒体内容";
                    }
                    System.Diagnostics.Debug.WriteLine("成功粘贴媒体内容");
                }
                else
                {
                    // 如果没有媒体内容可粘贴，则使用系统粘贴
                    richTextEditor.Paste();
                    if (statusText != null)
                    {
                        statusText.Text = "已粘贴文本内容";
                    }
                    System.Diagnostics.Debug.WriteLine("使用系统粘贴功能");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"粘贴操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理剪贴板图片粘贴 - 使用与正常插入图片相同的流程
        /// </summary>
        private async void HandleClipboardImagePaste()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始处理剪贴板图片粘贴 ===");

                if (!System.Windows.Clipboard.ContainsImage())
                {
                    System.Diagnostics.Debug.WriteLine("剪贴板中没有图片数据");
                    return;
                }

                // 获取剪贴板中的图片
                var clipboardImage = System.Windows.Clipboard.GetImage();
                if (clipboardImage == null)
                {
                    System.Diagnostics.Debug.WriteLine("无法从剪贴板获取图片数据");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"剪贴板图片尺寸: {clipboardImage.PixelWidth}x{clipboardImage.PixelHeight}");

                // 保存图片到临时文件
                var tempImagePath = await SaveClipboardImageToTempFile(clipboardImage);
                if (string.IsNullOrEmpty(tempImagePath))
                {
                    System.Diagnostics.Debug.WriteLine("保存剪贴板图片到临时文件失败");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"临时文件路径: {tempImagePath}");

                // 使用与正常插入图片完全相同的流程
                System.Diagnostics.Debug.WriteLine("调用InsertImageFile方法");
                InsertImageFile(tempImagePath);

                System.Diagnostics.Debug.WriteLine($"=== 剪贴板图片粘贴完成: {tempImagePath} ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理剪贴板图片粘贴失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 将剪贴板图片保存为临时文件
        /// </summary>
        private async Task<string> SaveClipboardImageToTempFile(BitmapSource clipboardImage)
        {
            try
            {
                // 保存到临时文件夹
                var tempPath = System.IO.Path.GetTempPath();
                var tempFileName = $"clipboard_image_{DateTime.Now:yyyyMMdd_HHmmss_fff}.png";
                var tempFilePath = System.IO.Path.Combine(tempPath, tempFileName);

                // 使用PNG编码器保存图片
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(clipboardImage));

                await Task.Run(() =>
                {
                    using (var fileStream = new FileStream(tempFilePath, FileMode.Create, FileAccess.Write))
                    {
                        encoder.Save(fileStream);
                    }
                });

                System.Diagnostics.Debug.WriteLine($"剪贴板图片已保存到临时文件: {tempFilePath}");
                return tempFilePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存剪贴板图片到临时文件失败: {ex.Message}");
                return string.Empty;
            }
        }





        /// <summary>
        /// 触发瀑布流刷新
        /// </summary>
        private void TriggerWaterfallRefresh()
        {
            try
            {
                // 触发媒体插入事件，这会通知MainWindow刷新瀑布流
                MediaInserted?.Invoke(this, EventArgs.Empty);
                System.Diagnostics.Debug.WriteLine("已触发瀑布流刷新事件");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"触发瀑布流刷新失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 全选操作
        /// </summary>
        private void SelectAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                richTextEditor.SelectAll();
                statusText.Text = "已全选内容";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"全选操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 字体设置操作
        /// </summary>
        private void FontSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowFontSettingsDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"字体设置操作失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示字体设置对话框
        /// </summary>
        private void ShowFontSettingsDialog()
        {
            try
            {
                // 获取当前选中文本的字体设置
                var currentSettings = GetCurrentFontSettings();

                // 打开字体设置对话框
                var fontDialog = new FontSettingsDialog(currentSettings);

                // 设置父窗口
                var parentWindow = Window.GetWindow(this);
                if (parentWindow != null)
                {
                    fontDialog.Owner = parentWindow;
                }

                if (fontDialog.ShowDialog() == true && fontDialog.SelectedFontSettings != null)
                {
                    // 应用字体设置到选中的文本或光标位置
                    ApplyFontSettings(fontDialog.SelectedFontSettings);
                    statusText.Text = "字体设置已应用";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示字体设置对话框失败: {ex.Message}");
                MessageBox.Show($"打开字体设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 插入表格操作
        /// </summary>
        private void InsertTable_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InsertTable();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入表格操作失败: {ex.Message}");
            }
        }

        #endregion

        #region 媒体剪贴板功能

        /// <summary>
        /// 复制选中的内容（包括媒体元素）
        /// </summary>
        private bool CopySelectedContent()
        {
            try
            {
                var selection = richTextEditor.Selection;
                if (selection.IsEmpty) return false;

                // 查找选中区域中的媒体元素
                var mediaElements = FindMediaElementsInSelection(selection);
                if (mediaElements.Count == 0) return false;

                // 清空媒体剪贴板
                _mediaClipboard.Clear();

                // 复制媒体元素到剪贴板
                foreach (var element in mediaElements)
                {
                    var clipboardData = CreateMediaClipboardData(element);
                    if (clipboardData != null)
                    {
                        _mediaClipboard.Add(clipboardData);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"已复制 {_mediaClipboard.Count} 个媒体元素");
                return _mediaClipboard.Count > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"复制媒体内容失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 剪切选中的内容（包括媒体元素）
        /// </summary>
        private bool CutSelectedContent()
        {
            try
            {
                var selection = richTextEditor.Selection;
                if (selection.IsEmpty) return false;

                // 先复制
                if (!CopySelectedContent()) return false;

                // 然后删除选中的媒体元素
                var mediaElements = FindMediaElementsInSelection(selection);
                foreach (var element in mediaElements)
                {
                    RemoveMediaElement(element);
                }

                System.Diagnostics.Debug.WriteLine($"已剪切 {mediaElements.Count} 个媒体元素");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"剪切媒体内容失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 粘贴内容（包括媒体元素）
        /// </summary>
        private bool PasteContent()
        {
            try
            {
                if (_mediaClipboard.Count == 0) return false;

                // 在当前光标位置粘贴媒体元素
                foreach (var clipboardData in _mediaClipboard)
                {
                    CreateAndInsertMediaElement(clipboardData);
                }

                System.Diagnostics.Debug.WriteLine($"已粘贴 {_mediaClipboard.Count} 个媒体元素");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"粘贴媒体内容失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 在选中区域中查找媒体元素
        /// </summary>
        private List<FrameworkElement> FindMediaElementsInSelection(TextSelection selection)
        {
            var mediaElements = new List<FrameworkElement>();

            try
            {
                var start = selection.Start;
                var end = selection.End;

                // 遍历选中区域的所有内容
                var current = start;
                while (current.CompareTo(end) < 0)
                {
                    var nextContext = current.GetNextContextPosition(LogicalDirection.Forward);
                    if (nextContext == null) break;

                    // 检查是否为InlineUIContainer
                    var element = current.GetAdjacentElement(LogicalDirection.Forward);
                    if (element is InlineUIContainer container && container.Child != null)
                    {
                        // 检查是否为媒体元素
                        if (IsMediaElement(container.Child))
                        {
                            mediaElements.Add((FrameworkElement)container.Child);
                        }
                    }

                    current = nextContext;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找媒体元素失败: {ex.Message}");
            }

            return mediaElements;
        }

        /// <summary>
        /// 判断是否为媒体元素
        /// </summary>
        private bool IsMediaElement(UIElement element)
        {
            return element is ResizableImageControl;
        }


        /// <summary>
        /// 创建媒体剪贴板数据
        /// </summary>
        private MediaClipboardData? CreateMediaClipboardData(FrameworkElement element)
        {
            try
            {
                var clipboardData = new MediaClipboardData
                {
                    Width = element.Width,
                    Height = element.Height
                };

                if (element is ResizableImageControl imageControl)
                {
                    clipboardData.MediaType = "Image";
                    clipboardData.FilePath = GetImageFilePath(imageControl);
                }
                else
                {
                    return null;
                }

                return clipboardData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建媒体剪贴板数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 移除媒体元素
        /// </summary>
        private void RemoveMediaElement(FrameworkElement element)
        {
            try
            {
                // 获取容器引用
                var container = element.DataContext as InlineUIContainer;
                if (container != null)
                {
                    // 从文档中移除容器
                    var paragraph = container.Parent as Paragraph;
                    if (paragraph != null)
                    {
                        paragraph.Inlines.Remove(container);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"移除媒体元素失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建并插入媒体元素
        /// </summary>
        private void CreateAndInsertMediaElement(MediaClipboardData clipboardData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始创建媒体元素: {clipboardData.MediaType}, 文件路径: {clipboardData.FilePath}");
                FrameworkElement? element = null;

                switch (clipboardData.MediaType)
                {
                    case "Image":
                        if (!string.IsNullOrEmpty(clipboardData.FilePath))
                        {
                            System.Diagnostics.Debug.WriteLine($"创建图片控件，路径: {clipboardData.FilePath}");
                            element = new ResizableImageControl();
                            ((ResizableImageControl)element).LoadImageFile(clipboardData.FilePath);
                            System.Diagnostics.Debug.WriteLine("图片控件创建完成");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("图片文件路径为空，跳过创建");
                        }
                        break;

                    // 🔧 删除音频和视频剪贴板恢复

                    case "Table":
                        System.Diagnostics.Debug.WriteLine("表格功能已重构，暂不支持剪贴板操作");
                        return;
                        break;
                }

                if (element != null)
                {
                    System.Diagnostics.Debug.WriteLine($"媒体元素创建成功，开始设置尺寸和插入");

                    // 恢复尺寸
                    if (!double.IsNaN(clipboardData.Width) && clipboardData.Width > 0)
                    {
                        element.Width = clipboardData.Width;
                        System.Diagnostics.Debug.WriteLine($"设置宽度: {clipboardData.Width}");
                    }
                    if (!double.IsNaN(clipboardData.Height) && clipboardData.Height > 0)
                    {
                        element.Height = clipboardData.Height;
                        System.Diagnostics.Debug.WriteLine($"设置高度: {clipboardData.Height}");
                    }

                    // 插入到文档
                    InsertUIElementAtCursor(element);
                    System.Diagnostics.Debug.WriteLine("媒体元素已成功插入到文档");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("媒体元素创建失败，element为null");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建并插入媒体元素失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 获取图片文件路径
        /// </summary>
        private string GetImageFilePath(ResizableImageControl imageControl)
        {
            try
            {
                // 通过反射或公共属性获取文件路径
                var field = imageControl.GetType().GetField("_imageFilePath",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                return field?.GetValue(imageControl)?.ToString() ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        // 🔧 删除音频和视频文件路径获取方法



        /// <summary>
        /// 创建原生WPF表格
        /// </summary>
        private Table CreateNativeTable(int rows, int columns)
        {
            var table = new Table();

            // 设置表格样式
            table.CellSpacing = 0;
            table.BorderBrush = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED));
            table.BorderThickness = new Thickness(1);
            table.Background = Brushes.White;

            // 创建列定义
            for (int col = 0; col < columns; col++)
            {
                table.Columns.Add(new TableColumn { Width = new GridLength(120) });
            }

            // 创建行组
            var rowGroup = new TableRowGroup();
            table.RowGroups.Add(rowGroup);

            // 创建行和单元格
            for (int row = 0; row < rows; row++)
            {
                var tableRow = new TableRow();

                for (int col = 0; col < columns; col++)
                {
                    var cell = new TableCell();

                    // 设置单元格样式
                    cell.BorderBrush = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED));
                    cell.BorderThickness = new Thickness(1);
                    cell.Padding = new Thickness(8);
                    cell.Background = Brushes.White;

                    // 添加可编辑的段落
                    var paragraph = new Paragraph();
                    paragraph.Margin = new Thickness(0);
                    cell.Blocks.Add(paragraph);

                    tableRow.Cells.Add(cell);
                }

                rowGroup.Rows.Add(tableRow);
            }

            return table;
        }

        /// <summary>
        /// 插入表格行
        /// </summary>
        private void InsertTableRow(Table table)
        {
            try
            {
                if (table.RowGroups.Count > 0)
                {
                    var rowGroup = table.RowGroups[0];
                    var columnCount = table.Columns.Count;

                    var newRow = new TableRow();

                    for (int col = 0; col < columnCount; col++)
                    {
                        var cell = new TableCell();
                        cell.BorderBrush = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED));
                        cell.BorderThickness = new Thickness(1);
                        cell.Padding = new Thickness(8);
                        cell.Background = Brushes.White;

                        var paragraph = new Paragraph();
                        paragraph.Margin = new Thickness(0);
                        cell.Blocks.Add(paragraph);

                        newRow.Cells.Add(cell);
                    }

                    rowGroup.Rows.Add(newRow);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入表格行失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 插入表格列
        /// </summary>
        private void InsertTableColumn(Table table)
        {
            try
            {
                // 添加新列定义
                table.Columns.Add(new TableColumn { Width = new GridLength(120) });

                // 为每一行添加新单元格
                if (table.RowGroups.Count > 0)
                {
                    var rowGroup = table.RowGroups[0];

                    foreach (var row in rowGroup.Rows)
                    {
                        var cell = new TableCell();
                        cell.BorderBrush = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED));
                        cell.BorderThickness = new Thickness(1);
                        cell.Padding = new Thickness(8);
                        cell.Background = Brushes.White;

                        var paragraph = new Paragraph();
                        paragraph.Margin = new Thickness(0);
                        cell.Blocks.Add(paragraph);

                        row.Cells.Add(cell);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入表格列失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 删除表格行
        /// </summary>
        private void DeleteTableRow(Table table)
        {
            try
            {
                if (table.RowGroups.Count > 0)
                {
                    var rowGroup = table.RowGroups[0];

                    if (rowGroup.Rows.Count > 1) // 至少保留一行
                    {
                        rowGroup.Rows.RemoveAt(rowGroup.Rows.Count - 1);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除表格行失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 删除表格列
        /// </summary>
        private void DeleteTableColumn(Table table)
        {
            try
            {
                if (table.Columns.Count > 1) // 至少保留一列
                {
                    // 删除列定义
                    table.Columns.RemoveAt(table.Columns.Count - 1);

                    // 删除每一行的最后一个单元格
                    if (table.RowGroups.Count > 0)
                    {
                        var rowGroup = table.RowGroups[0];

                        foreach (var row in rowGroup.Rows)
                        {
                            if (row.Cells.Count > 0)
                            {
                                row.Cells.RemoveAt(row.Cells.Count - 1);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除表格列失败: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 加载页面内容
        /// </summary>
        public void LoadPage(PageNode pageNode)
        {
            try
            {
                _currentPageNode = pageNode;
                
                if (!string.IsNullOrEmpty(pageNode.Content))
                {
                    // 加载富文本内容
                    LoadRichTextContent(pageNode.Content);
                }
                else
                {
                    // 设置默认内容
                    SetDefaultContent();
                }

                // 加载媒体文件
                LoadMediaFiles(pageNode);

                System.Diagnostics.Debug.WriteLine($"页面 '{pageNode.Name}' 加载完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载页面失败: {ex.Message}");
                MessageBox.Show($"加载页面失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 保存页面内容
        /// </summary>
        public void SavePage()
        {
            try
            {
                if (_currentPageNode != null)
                {
                    // 保存到PageNode
                    _currentPageNode.Content = GetRichTextContent();

                    // 更新统计信息
                    UpdatePageNodeStatistics();

                    // 使用DocumentSerializer保存完整文档
                    DocumentSerializer.SaveDocument(_currentPageNode, richTextEditor.Document, _mediaManager.GetAllMediaFiles());

                    statusText.Text = "文档已保存";
                    System.Diagnostics.Debug.WriteLine($"页面 '{_currentPageNode.Name}' 保存完成");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存页面失败: {ex.Message}");
                MessageBox.Show($"保存页面失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取当前内容
        /// </summary>
        public string GetContent()
        {
            return GetRichTextContent();
        }

        /// <summary>
        /// 更新PageNode的统计信息
        /// </summary>
        private void UpdatePageNodeStatistics()
        {
            if (_currentPageNode == null) return;

            try
            {
                // 获取纯文本内容并计算字符数
                string plainText = GetPlainTextFromDocument();
                int charCount = string.IsNullOrWhiteSpace(plainText) ? 0 : plainText.Trim().Length;

                // 直接设置WordCount字段，避免触发Content的setter
                var wordCountField = typeof(PageNode).GetField("_wordCount",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (wordCountField != null)
                {
                    wordCountField.SetValue(_currentPageNode, charCount);
                }

                // 统计图片数量
                int imageCount = CountImagesInDocument();
                _currentPageNode.ImageCount = imageCount;

                // 统计表格数量
                int tableCount = CountTablesInDocument();
                _currentPageNode.TableCount = tableCount;

                System.Diagnostics.Debug.WriteLine($"更新统计信息 - 字符数: {charCount}, 图片: {imageCount}, 表格: {tableCount}");

                // 触发统计信息更新事件
                StatisticsUpdated?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新PageNode统计信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从RichTextBox文档中获取纯文本
        /// </summary>
        private string GetPlainTextFromDocument()
        {
            try
            {
                var textRange = new TextRange(richTextEditor.Document.ContentStart, richTextEditor.Document.ContentEnd);
                return textRange.Text ?? string.Empty;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取纯文本失败: {ex.Message}");
                return string.Empty;
            }
        }



        /// <summary>
        /// 统计文档中的图片数量
        /// </summary>
        private int CountImagesInDocument()
        {
            int count = 0;
            try
            {
                // 遍历文档中的所有InlineUIContainer和BlockUIContainer
                foreach (var block in richTextEditor.Document.Blocks)
                {
                    count += CountImagesInBlock(block);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"统计图片数量失败: {ex.Message}");
            }
            return count;
        }

        /// <summary>
        /// 统计Block中的图片数量
        /// </summary>
        private int CountImagesInBlock(Block block)
        {
            int count = 0;

            if (block is Paragraph paragraph)
            {
                foreach (var inline in paragraph.Inlines)
                {
                    if (inline is InlineUIContainer uiContainer)
                    {
                        if (uiContainer.Child is ResizableImageControl)
                        {
                            count++;
                        }
                    }
                }
            }
            else if (block is BlockUIContainer blockContainer)
            {
                if (blockContainer.Child is ResizableImageControl)
                {
                    count++;
                }
            }

            return count;
        }

        /// <summary>
        /// 统计文档中的表格数量
        /// </summary>
        private int CountTablesInDocument()
        {
            int count = 0;
            try
            {
                foreach (var block in richTextEditor.Document.Blocks)
                {
                    if (block is Table)
                    {
                        count++;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"统计表格数量失败: {ex.Message}");
            }
            return count;
        }

        /// <summary>
        /// 设置内容
        /// </summary>
        public void SetContent(string content)
        {
            LoadRichTextContent(content);
        }

        /// <summary>
        /// 应用字体设置 - 供外部调用
        /// </summary>
        public void ApplyFontSettings(FontSettings settings)
        {
            if (settings == null) return;

            try
            {
                var selection = richTextEditor.Selection;

                if (!selection.IsEmpty)
                {
                    // 应用到选中文本
                    selection.ApplyPropertyValue(TextElement.FontFamilyProperty, new FontFamily(settings.FontFamily));
                    selection.ApplyPropertyValue(TextElement.FontSizeProperty, settings.FontSize);
                    selection.ApplyPropertyValue(TextElement.FontWeightProperty, settings.IsBold ? FontWeights.Bold : FontWeights.Normal);
                    selection.ApplyPropertyValue(TextElement.FontStyleProperty, settings.IsItalic ? FontStyles.Italic : FontStyles.Normal);
                    selection.ApplyPropertyValue(TextElement.ForegroundProperty, new SolidColorBrush(settings.TextColor));

                    // 应用背景颜色
                    selection.ApplyPropertyValue(TextElement.BackgroundProperty, new SolidColorBrush(settings.BackgroundColor));

                    // 应用文本装饰
                    var decorations = new TextDecorationCollection();
                    if (settings.IsUnderline)
                    {
                        decorations.Add(TextDecorations.Underline);
                    }
                    if (settings.IsStrikethrough)
                    {
                        decorations.Add(TextDecorations.Strikethrough);
                    }
                    selection.ApplyPropertyValue(Inline.TextDecorationsProperty, decorations.Count > 0 ? decorations : null);
                }
                else
                {
                    // 应用到光标位置（影响后续输入的文本）
                    richTextEditor.FontFamily = new FontFamily(settings.FontFamily);
                    richTextEditor.FontSize = settings.FontSize;
                    richTextEditor.FontWeight = settings.IsBold ? FontWeights.Bold : FontWeights.Normal;
                    richTextEditor.FontStyle = settings.IsItalic ? FontStyles.Italic : FontStyles.Normal;
                    richTextEditor.Foreground = new SolidColorBrush(settings.TextColor);
                    richTextEditor.Background = new SolidColorBrush(settings.BackgroundColor);
                }

                statusText.Text = "字体设置已应用";
                System.Diagnostics.Debug.WriteLine($"字体设置已应用: {settings.FontFamily}, {settings.FontSize}pt");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用字体设置失败: {ex.Message}");
                MessageBox.Show($"应用字体设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }













        /// <summary>
        /// 插入符号 - 供外部调用
        /// </summary>
        public void InsertSymbol(string symbol)
        {
            if (string.IsNullOrEmpty(symbol)) return;

            try
            {
                // 在光标处插入符号，使用TextRange确保正确插入
                var caretPosition = richTextEditor.CaretPosition;
                var insertionPosition = caretPosition.GetInsertionPosition(LogicalDirection.Forward) ?? caretPosition;

                // 创建一个Run元素来包装符号，确保字体支持
                var run = new Run(symbol);

                // 设置字体以确保特殊符号能够正确显示
                run.FontFamily = GetBestFontForSymbol(symbol);
                run.FontSize = richTextEditor.FontSize;

                // 获取当前段落，如果不存在则创建新段落
                var paragraph = insertionPosition.Paragraph;
                if (paragraph == null)
                {
                    paragraph = new Paragraph();
                    richTextEditor.Document.Blocks.Add(paragraph);
                    insertionPosition = paragraph.ContentStart;
                }

                // 在光标位置插入Run元素
                var range = new TextRange(insertionPosition, insertionPosition);
                range.Text = ""; // 清空选择

                // 插入符号
                insertionPosition.InsertTextInRun(symbol);

                // 应用字体格式到刚插入的文本
                var symbolStart = insertionPosition;
                var symbolEnd = symbolStart.GetPositionAtOffset(symbol.Length) ?? symbolStart;
                var symbolRange = new TextRange(symbolStart, symbolEnd);
                symbolRange.ApplyPropertyValue(TextElement.FontFamilyProperty, GetBestFontForSymbol(symbol));

                // 移动光标到新插入的符号后面
                var newPosition = symbolEnd;
                if (newPosition != null)
                {
                    richTextEditor.CaretPosition = newPosition;
                }

                richTextEditor.Focus();
                statusText.Text = $"已插入符号: {symbol}";

                System.Diagnostics.Debug.WriteLine($"符号已插入: {symbol} (Unicode: U+{((int)symbol[0]):X4})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入符号失败: {ex.Message}");
                MessageBox.Show($"插入符号时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 为符号选择最佳字体
        /// </summary>
        private FontFamily GetBestFontForSymbol(string symbol)
        {
            if (string.IsNullOrEmpty(symbol)) return new FontFamily("Segoe UI");

            var codePoint = char.ConvertToUtf32(symbol, 0);

            // 根据Unicode范围选择合适的字体
            if (codePoint >= 0x1F300 && codePoint <= 0x1F9FF) // Emoji范围
            {
                return new FontFamily("Segoe UI Emoji");
            }
            else if (codePoint >= 0x2000 && codePoint <= 0x2BFF) // 符号和标点
            {
                return new FontFamily("Segoe UI Symbol");
            }
            else if (codePoint >= 0x0370 && codePoint <= 0x03FF) // 希腊字母
            {
                return new FontFamily("Times New Roman");
            }
            else if (codePoint >= 0x2100 && codePoint <= 0x214F) // 字母式符号
            {
                return new FontFamily("Cambria Math");
            }
            else if (codePoint >= 0x2200 && codePoint <= 0x22FF) // 数学运算符
            {
                return new FontFamily("Cambria Math");
            }
            else
            {
                return new FontFamily("Segoe UI");
            }
        }

        /// <summary>
        /// 插入编号 - 供外部调用
        /// </summary>
        public void InsertNumbering(string selectedStyle, string styleType)
        {
            if (string.IsNullOrEmpty(selectedStyle)) return;

            try
            {
                var selection = richTextEditor.Selection;

                // 检查是否有选中文本
                if (!selection.IsEmpty)
                {
                    // 有选中文本：为每行添加编号
                    ApplyNumberingToSelectedText(selectedStyle, styleType);
                }
                else
                {
                    // 没有选中文本：保持原有功能，在光标位置插入编号
                    InsertNumberingAtCursor(selectedStyle, styleType);
                }

                richTextEditor.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"插入编号时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 为选中的文本应用编号
        /// </summary>
        private void ApplyNumberingToSelectedText(string selectedStyle, string styleType)
        {
            var selection = richTextEditor.Selection;
            if (selection.IsEmpty) return;

            // 获取选中的文本
            string selectedText = selection.Text;
            if (string.IsNullOrEmpty(selectedText)) return;

            // 按行分割文本，移除空的分割结果以避免多余的空行
            string[] lines = selectedText.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            // 生成带编号的文本
            var numberedLines = new List<string>();
            int lineNumber = 1;

            foreach (string line in lines)
            {
                string numberingText = GetNumberingTextForLine(selectedStyle, styleType, lineNumber);
                numberedLines.Add($"{numberingText} {line}");
                lineNumber++;
            }

            // 替换选中的文本
            string numberedText = string.Join(Environment.NewLine, numberedLines);
            selection.Text = numberedText;

            statusText.Text = $"已为 {lineNumber - 1} 行文本添加编号";
        }

        /// <summary>
        /// 在光标位置插入编号（原有功能）
        /// </summary>
        private void InsertNumberingAtCursor(string selectedStyle, string styleType)
        {
            string numberingText = GetNumberingText(selectedStyle, styleType);
            var caretPosition = richTextEditor.CaretPosition;
            var range = new TextRange(caretPosition, caretPosition);
            range.Text = numberingText + " ";

            // 移动光标
            var newPosition = caretPosition.GetPositionAtOffset(numberingText.Length + 1);
            if (newPosition != null)
            {
                richTextEditor.CaretPosition = newPosition;
            }

            statusText.Text = "已插入编号";
        }

        /// <summary>
        /// 获取指定行号的编号文本
        /// </summary>
        private string GetNumberingTextForLine(string style, string styleType, int lineNumber)
        {
            switch (styleType)
            {
                case "numbering":
                    // 根据样式名称返回对应的编号格式
                    switch (style)
                    {
                        case "数字":
                            return $"{lineNumber}.";
                        case "数字括号":
                            return $"{lineNumber})";
                        case "括号数字":
                            return $"({lineNumber})";
                        case "圆圈数字":
                            return NumberingHelper.GetCircledNumber(lineNumber);
                        case "大写字母":
                            return $"{NumberingHelper.GetUppercaseLetter(lineNumber)}.";
                        case "小写字母":
                            return $"{NumberingHelper.GetLowercaseLetter(lineNumber)}.";
                        case "大写罗马":
                            return $"{NumberingHelper.GetUppercaseRoman(lineNumber)}.";
                        case "小写罗马":
                            return $"{NumberingHelper.GetLowercaseRoman(lineNumber)}.";
                        case "中文数字":
                            return $"{NumberingHelper.GetChineseNumber(lineNumber)}.";
                        default:
                            return $"{lineNumber}.";
                    }

                case "bullet":
                    // 根据样式名称返回对应的项目符号
                    switch (style)
                    {
                        case "实心圆":
                            return "●";
                        case "空心圆":
                            return "○";
                        case "实心方":
                            return "■";
                        case "空心方":
                            return "□";
                        case "菱形":
                            return "♦";
                        case "三角":
                            return "►";
                        case "对勾":
                            return "✓";
                        case "星号":
                            return "★";
                        default:
                            return "●";
                    }

                case "multilevel":
                    return $"{lineNumber}.";

                default:
                    return $"{lineNumber}.";
            }
        }

        /// <summary>
        /// 获取编号文本
        /// </summary>
        private string GetNumberingText(string style, string styleType)
        {
            switch (styleType)
            {
                case "numbering":
                    return style.Contains("1.") ? "1." : style.Contains("1)") ? "1)" : "1.";
                case "bullet":
                    return style.Contains("●") ? "●" : style.Contains("○") ? "○" : "●";
                case "multilevel":
                    return "1.";
                default:
                    return "1.";
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 应用文本格式
        /// </summary>
        private void ApplyTextFormat(TextFormatType format)
        {
            try
            {
                var selection = richTextEditor.Selection;
                if (selection.IsEmpty) return;

                switch (format)
                {
                    case TextFormatType.Bold:
                        var currentWeight = selection.GetPropertyValue(TextElement.FontWeightProperty);
                        var newWeight = currentWeight.Equals(FontWeights.Bold) ? FontWeights.Normal : FontWeights.Bold;
                        selection.ApplyPropertyValue(TextElement.FontWeightProperty, newWeight);
                        break;

                    case TextFormatType.Italic:
                        var currentStyle = selection.GetPropertyValue(TextElement.FontStyleProperty);
                        var newStyle = currentStyle.Equals(FontStyles.Italic) ? FontStyles.Normal : FontStyles.Italic;
                        selection.ApplyPropertyValue(TextElement.FontStyleProperty, newStyle);
                        break;

                    case TextFormatType.Underline:
                        var currentDecoration = selection.GetPropertyValue(Inline.TextDecorationsProperty);
                        var newDecoration = currentDecoration.Equals(TextDecorations.Underline) ? null : TextDecorations.Underline;
                        selection.ApplyPropertyValue(Inline.TextDecorationsProperty, newDecoration);
                        break;
                }

                richTextEditor.Focus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用文本格式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 插入图片 - 供外部调用
        /// </summary>
        public void InsertImage()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择图片文件",
                    Filter = "图片文件|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff|所有文件|*.*",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    InsertMediaFile(openFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入图片失败: {ex.Message}");
                MessageBox.Show($"插入图片失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 🔧 删除音频和视频插入方法

        /// <summary>
        /// 导入文件 - 供外部调用
        /// </summary>
        public void ImportFiles()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择要导入的文件",
                    Filter = "所有支持的文件|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff;*.webp;*.mp3;*.wav;*.wma;*.aac;*.flac;*.ogg;*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.flv;*.webm;*.pdf;*.doc;*.docx;*.txt;*.rtf|" +
                            "图片文件|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff;*.webp|" +
                            "音频文件|*.mp3;*.wav;*.wma;*.aac;*.flac;*.ogg|" +
                            "视频文件|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.flv;*.webm|" +
                            "文档文件|*.pdf;*.doc;*.docx;*.txt;*.rtf|" +
                            "所有文件|*.*",
                    Multiselect = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var addedFiles = _mediaManager.AddMediaFiles(openFileDialog.FileNames);

                    foreach (var mediaFile in addedFiles)
                    {
                        InsertMediaFile(mediaFile.FilePath);
                    }

                    statusText.Text = $"成功导入 {addedFiles.Count} 个文件";
                    RefreshMediaList();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导入文件失败: {ex.Message}");
                MessageBox.Show($"导入文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 插入表格 - 供外部调用
        /// </summary>
        public void InsertTable()
        {
            try
            {
                // 创建原生WPF Table
                var table = CreateNativeTable(3, 3);

                // 获取当前光标位置
                var caretPosition = richTextEditor.CaretPosition;

                // 确保在段落中插入
                if (caretPosition.Paragraph == null)
                {
                    // 如果没有段落，创建一个新段落
                    var newParagraph = new Paragraph();
                    richTextEditor.Document.Blocks.Add(newParagraph);
                    caretPosition = newParagraph.ContentStart;
                }

                // 在当前段落后插入表格
                var currentParagraph = caretPosition.Paragraph;
                var blockContainer = currentParagraph.Parent as FlowDocument;

                if (blockContainer != null)
                {
                    // 在当前段落后插入表格
                    var blocks = blockContainer.Blocks.ToList();
                    var insertIndex = blocks.IndexOf(currentParagraph) + 1;

                    // 先添加表格
                    blockContainer.Blocks.Add(table);

                    // 再添加一个新段落，方便继续编辑
                    var nextParagraph = new Paragraph();
                    blockContainer.Blocks.Add(nextParagraph);

                    // 设置光标到新段落
                    richTextEditor.CaretPosition = nextParagraph.ContentStart;
                }

                statusText.Text = "表格已插入";

                // 触发表格修改事件，用于实时保存
                TableModified?.Invoke(this, EventArgs.Empty);
                System.Diagnostics.Debug.WriteLine("原生表格插入完成，触发实时保存");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入表格失败: {ex.Message}");
                MessageBox.Show($"插入表格失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 插入图片文件 - 供外部调用
        /// </summary>
        public async void InsertImageFile(string filePath)
        {
            try
            {
                // 先复制文件到当前节点的附件文件夹
                var copiedFilePath = await CopyMediaToCurrentNode(filePath);
                var finalFilePath = !string.IsNullOrEmpty(copiedFilePath) ? copiedFilePath : filePath;

                // 添加到媒体管理器
                var fileExtension = System.IO.Path.GetExtension(finalFilePath).ToLower();
                var mediaType = GetMediaFileType(fileExtension);
                var mediaFile = _mediaManager.AddMediaFile(finalFilePath, mediaType);

                // 插入图片到文档
                InsertImageToDocument(finalFilePath);

                // 刷新媒体列表以在瀑布流中显示
                RefreshMediaList();

                // 触发媒体文件添加事件
                MediaFileAdded?.Invoke(this, new MediaFileEventArgs
                {
                    FilePath = finalFilePath,
                    FileType = mediaType,
                    FileName = System.IO.Path.GetFileName(finalFilePath)
                });

                // 触发媒体插入事件，用于保存
                MediaInserted?.Invoke(this, EventArgs.Empty);

                System.Diagnostics.Debug.WriteLine($"图片插入完成，已添加到媒体管理器: {finalFilePath}");
                statusText.Text = $"已插入图片: {System.IO.Path.GetFileName(finalFilePath)}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入图片文件失败: {ex.Message}");
                MessageBox.Show($"插入图片文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 🔧 删除音频和视频插入方法

        /// <summary>
        /// 应用粗体格式 - 供外部调用
        /// </summary>
        public void ApplyBoldFormat()
        {
            try
            {
                ApplyTextFormat(TextFormatType.Bold);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用粗体格式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用斜体格式 - 供外部调用
        /// </summary>
        public void ApplyItalicFormat()
        {
            try
            {
                ApplyTextFormat(TextFormatType.Italic);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用斜体格式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用下划线格式 - 供外部调用
        /// </summary>
        public void ApplyUnderlineFormat()
        {
            try
            {
                ApplyTextFormat(TextFormatType.Underline);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用下划线格式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前字体设置 - 供外部调用
        /// </summary>
        public FontSettings GetCurrentFontSettings()
        {
            try
            {
                var selection = richTextEditor.Selection;
                var settings = new FontSettings();

                if (!selection.IsEmpty)
                {
                    // 获取选中文本的字体设置
                    var fontFamily = selection.GetPropertyValue(TextElement.FontFamilyProperty) as FontFamily;
                    var fontSize = selection.GetPropertyValue(TextElement.FontSizeProperty);
                    var fontWeight = selection.GetPropertyValue(TextElement.FontWeightProperty);
                    var fontStyle = selection.GetPropertyValue(TextElement.FontStyleProperty);
                    var foreground = selection.GetPropertyValue(TextElement.ForegroundProperty) as Brush;
                    var textDecorations = selection.GetPropertyValue(Inline.TextDecorationsProperty) as TextDecorationCollection;

                    settings.FontFamily = fontFamily?.Source ?? "Segoe UI";
                    settings.FontSize = fontSize is double size ? size : 14.0;
                    settings.IsBold = fontWeight.Equals(FontWeights.Bold);
                    settings.IsItalic = fontStyle.Equals(FontStyles.Italic);
                    settings.IsUnderline = textDecorations?.Any(td => td.Location == TextDecorationLocation.Underline) ?? false;
                    var colorBrush = foreground as SolidColorBrush ?? new SolidColorBrush(Colors.Black);
                    settings.TextColor = colorBrush.Color;
                }
                else
                {
                    // 获取光标位置的字体设置
                    settings.FontFamily = richTextEditor.FontFamily?.Source ?? "Segoe UI";
                    settings.FontSize = richTextEditor.FontSize;
                    settings.IsBold = false;
                    settings.IsItalic = false;
                    settings.IsUnderline = false;
                    settings.TextColor = Colors.Black;
                }

                return settings;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取字体设置失败: {ex.Message}");
                return new FontSettings(); // 返回默认设置
            }
        }





        /// <summary>
        /// 设置栏布局
        /// </summary>
        private void SetColumnLayout(int columnCount)
        {
            try
            {
                var document = richTextEditor.Document;
                document.ColumnWidth = columnCount == 1 ? double.NaN : 350;
                document.ColumnGap = columnCount == 1 ? 0 : 20;

                statusText.Text = $"已设置为 {columnCount} 栏布局";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置栏布局失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换媒体侧边栏
        /// </summary>
        private void ToggleMediaSidebar()
        {
            mediaSidebar.Visibility = mediaSidebar.Visibility == Visibility.Visible
                ? Visibility.Collapsed
                : Visibility.Visible;

            if (mediaSidebar.Visibility == Visibility.Visible)
            {
                RefreshMediaList();
            }
        }

        /// <summary>
        /// 刷新媒体列表
        /// </summary>
        private void RefreshMediaList()
        {
            try
            {
                // 优化：使用虚拟化或延迟加载大量媒体文件
                var mediaFiles = _mediaManager.GetAllMediaFiles();

                // 如果媒体文件数量很大，考虑分批加载
                if (mediaFiles.Count > 50)
                {
                    RefreshMediaListBatched(mediaFiles);
                }
                else
                {
                    RefreshMediaListImmediate(mediaFiles);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新媒体列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 立即刷新媒体列表
        /// </summary>
        private void RefreshMediaListImmediate(IList<MediaFileInfo> mediaFiles)
        {
            mediaListPanel.Children.Clear();
            foreach (var mediaFile in mediaFiles)
            {
                var mediaItem = CreateMediaListItem(mediaFile);
                mediaListPanel.Children.Add(mediaItem);
            }
        }

        /// <summary>
        /// 分批刷新媒体列表
        /// </summary>
        private void RefreshMediaListBatched(IList<MediaFileInfo> mediaFiles)
        {
            mediaListPanel.Children.Clear();

            // 分批加载，每批10个
            const int batchSize = 10;
            int currentIndex = 0;

            var timer = new DispatcherTimer();
            timer.Interval = TimeSpan.FromMilliseconds(50);
            timer.Tick += (s, e) =>
            {
                int endIndex = Math.Min(currentIndex + batchSize, mediaFiles.Count);

                for (int i = currentIndex; i < endIndex; i++)
                {
                    var mediaItem = CreateMediaListItem(mediaFiles[i]);
                    mediaListPanel.Children.Add(mediaItem);
                }

                currentIndex = endIndex;

                if (currentIndex >= mediaFiles.Count)
                {
                    timer.Stop();
                }
            };

            timer.Start();
        }

        /// <summary>
        /// 创建媒体列表项
        /// </summary>
        private UIElement CreateMediaListItem(MediaFileInfo mediaFile)
        {
            var border = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4),
                Padding = new Thickness(8),
                Margin = new Thickness(0, 0, 0, 8)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(40) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 图标
            var icon = new TextBlock
            {
                Text = GetMediaTypeIcon(mediaFile.FileType),
                FontSize = 20,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(icon, 0);
            grid.Children.Add(icon);

            // 文件信息
            var infoPanel = new StackPanel();
            infoPanel.Children.Add(new TextBlock
            {
                Text = mediaFile.FileName,
                FontWeight = FontWeights.SemiBold,
                TextTrimming = TextTrimming.CharacterEllipsis
            });
            infoPanel.Children.Add(new TextBlock
            {
                Text = mediaFile.FileSize,
                FontSize = 10,
                Foreground = Brushes.Gray
            });
            Grid.SetColumn(infoPanel, 1);
            grid.Children.Add(infoPanel);

            border.Child = grid;
            return border;
        }

        /// <summary>
        /// 获取媒体类型图标
        /// </summary>
        private string GetMediaTypeIcon(MediaFileType fileType)
        {
            return fileType switch
            {
                MediaFileType.Image => "🖼️",
                MediaFileType.Document => "📄",
                _ => "📁"
            };
        }

        /// <summary>
        /// 插入媒体文件 - 修复版，确保文件被复制到附件文件夹
        /// </summary>
        private async void InsertMediaFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    MessageBox.Show("文件不存在", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 先复制文件到当前节点的附件文件夹
                var copiedFilePath = await CopyMediaToCurrentNode(filePath);
                var finalFilePath = !string.IsNullOrEmpty(copiedFilePath) ? copiedFilePath : filePath;

                var fileExtension = System.IO.Path.GetExtension(finalFilePath).ToLower();
                var mediaType = GetMediaFileType(fileExtension);

                // 添加到媒体管理器
                var mediaFile = _mediaManager.AddMediaFile(finalFilePath, mediaType);

                // 根据类型插入到文档
                switch (mediaType)
                {
                    case MediaFileType.Image:
                        InsertImageToDocument(finalFilePath);
                        break;
                    default:
                        System.Diagnostics.Debug.WriteLine($"不支持的文件类型: {mediaType}");
                        break;
                }

                // 触发事件
                MediaFileAdded?.Invoke(this, new MediaFileEventArgs
                {
                    FilePath = finalFilePath,
                    FileType = mediaType,
                    FileName = System.IO.Path.GetFileName(finalFilePath)
                });

                // 触发媒体插入事件，用于保存
                MediaInserted?.Invoke(this, EventArgs.Empty);

                statusText.Text = $"已插入 {System.IO.Path.GetFileName(finalFilePath)}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入媒体文件失败: {ex.Message}");
                MessageBox.Show($"插入媒体文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取媒体文件类型
        /// </summary>
        private MediaFileType GetMediaFileType(string extension)
        {
            return extension switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".tiff" or ".webp" => MediaFileType.Image,
                _ => MediaFileType.Document
            };
        }

        /// <summary>
        /// 插入图片到文档
        /// </summary>
        private void InsertImageToDocument(string imagePath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== InsertImageToDocument开始: {imagePath} ===");

                // 使用新的可缩放图片控件
                var resizableImage = new ResizableImageControl();
                System.Diagnostics.Debug.WriteLine($"创建ResizableImageControl: {resizableImage.GetType().Name}");

                resizableImage.LoadImageFile(imagePath);

                // 设置标识信息，便于序列化和恢复
                resizableImage.Tag = $"ImagePath:{imagePath}";
                resizableImage.Name = $"Image_{Guid.NewGuid():N}";

                System.Diagnostics.Debug.WriteLine("图片文件已加载到控件");

                // 在当前光标位置插入图片
                System.Diagnostics.Debug.WriteLine("调用InsertUIElementAtCursor");
                InsertUIElementAtCursor(resizableImage);

                System.Diagnostics.Debug.WriteLine($"=== 可缩放图片已插入到文档: {imagePath} ===");
                statusText.Text = $"图片已插入: {System.IO.Path.GetFileName(imagePath)}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入图片到文档失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                MessageBox.Show($"插入图片失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 🔧 删除音频和视频插入到文档的方法

        /// <summary>
        /// 在光标位置插入UI元素
        /// </summary>
        private void InsertUIElementAtCursor(UIElement element)
        {
            try
            {
                if (richTextEditor == null)
                {
                    System.Diagnostics.Debug.WriteLine("richTextEditor为null，无法插入UI元素");
                    throw new InvalidOperationException("富文本编辑器未初始化");
                }

                var caretPosition = richTextEditor.CaretPosition;

                // 获取当前段落
                var paragraph = caretPosition.Paragraph;

                if (paragraph == null)
                {
                    // 如果没有当前段落，创建新段落
                    paragraph = new Paragraph();
                    richTextEditor.Document.Blocks.Add(paragraph);
                    caretPosition = paragraph.ContentStart;
                }

                // 创建InlineUIContainer来包装UI元素
                var container = new InlineUIContainer(element);
                container.Tag = "MediaContainer";

                // 为媒体元素添加拖拽保护
                SetupMediaElementDragProtection(element, container);

                // 在光标位置插入容器
                if (caretPosition.IsAtInsertionPosition)
                {
                    // 在光标位置插入新行，然后添加容器
                    var newParagraph = new Paragraph();
                    newParagraph.Inlines.Add(container);

                    // 在当前位置插入新段落
                    var insertPosition = caretPosition.GetInsertionPosition(LogicalDirection.Forward) ?? caretPosition;
                    insertPosition.InsertParagraphBreak();

                    // 获取新创建的段落并添加容器
                    var nextParagraph = insertPosition.Paragraph?.NextBlock as Paragraph;
                    if (nextParagraph != null)
                    {
                        nextParagraph.Inlines.Clear();
                        nextParagraph.Inlines.Add(container);
                    }
                    else
                    {
                        // 如果无法获取下一段落，直接在当前段落添加
                        paragraph.Inlines.Add(container);
                    }
                }
                else
                {
                    // 如果不在插入位置，在段落末尾添加
                    paragraph.Inlines.Add(container);
                }

                // 移动光标到插入元素之后
                var newPosition = container.ElementEnd.GetNextInsertionPosition(LogicalDirection.Forward);
                if (newPosition != null)
                {
                    richTextEditor.CaretPosition = newPosition;
                }

                richTextEditor.Focus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"在光标位置插入UI元素失败: {ex.Message}");

                // 如果光标位置插入失败，回退到文档末尾插入
                try
                {
                    var paragraph = new Paragraph();
                    paragraph.Inlines.Add(new InlineUIContainer(element));
                    richTextEditor.Document.Blocks.Add(paragraph);
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"回退插入也失败: {fallbackEx.Message}");
                    throw;
                }
            }
        }

        /// <summary>
        /// 为媒体元素设置拖拽保护和右键菜单
        /// </summary>
        private void SetupMediaElementDragProtection(UIElement element, InlineUIContainer container)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== SetupMediaElementDragProtection开始 ===");
                System.Diagnostics.Debug.WriteLine($"元素类型: {element.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"是否为ResizableImageControl: {element is ResizableImageControl}");

                // 标记为媒体元素
                element.SetValue(FrameworkElement.TagProperty, "MediaElement");
                System.Diagnostics.Debug.WriteLine("已设置Tag为MediaElement");

                // 禁用拖拽相关属性
                element.AllowDrop = false;

                // 设置为不可拖拽
                if (element is FrameworkElement frameworkElement)
                {
                    // 禁用拖拽启动
                    frameworkElement.PreviewMouseLeftButtonDown += MediaElement_PreviewMouseLeftButtonDown;
                    frameworkElement.PreviewMouseMove += MediaElement_PreviewMouseMove;
                    frameworkElement.PreviewMouseLeftButtonUp += MediaElement_PreviewMouseLeftButtonUp;

                    // 添加双击进入编辑模式
                    if (frameworkElement is Control mediaControl)
                    {
                        // 对于Control类型，使用MouseDoubleClick事件
                        mediaControl.MouseDoubleClick += (s, e) =>
                        {
                            System.Diagnostics.Debug.WriteLine("媒体元素双击事件触发（Control）");
                            EnterMediaEditMode(frameworkElement);
                            e.Handled = true;
                        };
                    }

                    // 对于所有FrameworkElement，使用MouseLeftButtonDown的双击检测
                    frameworkElement.MouseLeftButtonDown += (s, e) =>
                    {
                        if (e.ClickCount == 2)
                        {
                            System.Diagnostics.Debug.WriteLine("媒体元素双击检测（FrameworkElement）");
                            EnterMediaEditMode(frameworkElement);
                            e.Handled = true;
                        }
                    };

                    // 禁用拖拽操作
                    frameworkElement.PreviewDragEnter += MediaElement_PreviewDragEnter;
                    frameworkElement.PreviewDragOver += MediaElement_PreviewDragOver;
                    frameworkElement.PreviewDrop += MediaElement_PreviewDrop;

                    // 设置焦点属性
                    frameworkElement.Focusable = false;

                    // 如果是Control类型，设置IsTabStop
                    if (frameworkElement is Control control)
                    {
                        control.IsTabStop = false;
                    }

                    // 添加右键菜单
                    SetupMediaElementContextMenu(frameworkElement);
                }

                // 存储容器引用
                element.SetValue(FrameworkElement.DataContextProperty, container);

                System.Diagnostics.Debug.WriteLine("=== 媒体元素拖拽保护和右键菜单已设置完成 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置媒体元素拖拽保护失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为媒体元素设置右键菜单
        /// </summary>
        private void SetupMediaElementContextMenu(FrameworkElement element)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始为媒体元素设置右键菜单: {element.GetType().Name}");



                var contextMenu = new ContextMenu();

                // 尝试获取样式，如果失败则使用默认样式
                try
                {
                    contextMenu.Style = Application.Current.FindResource("FigmaContextMenuStyle") as Style;
                    System.Diagnostics.Debug.WriteLine("成功应用FigmaContextMenuStyle");
                }
                catch (Exception styleEx)
                {
                    System.Diagnostics.Debug.WriteLine($"无法找到FigmaContextMenuStyle: {styleEx.Message}");
                }

                // 编辑模式菜单项
                var editModeItem = new MenuItem
                {
                    Header = "编辑模式"
                };

                try
                {
                    editModeItem.Style = Application.Current.FindResource("FigmaMenuItemStyle") as Style;
                }
                catch (Exception styleEx)
                {
                    System.Diagnostics.Debug.WriteLine($"无法找到FigmaMenuItemStyle: {styleEx.Message}");
                }

                editModeItem.Icon = new TextBlock { Text = "✏️", FontFamily = new FontFamily("Segoe UI Emoji"), FontSize = 14 };
                editModeItem.Click += (s, e) =>
                {
                    System.Diagnostics.Debug.WriteLine($"预设菜单：编辑模式被点击，媒体元素: {element.GetType().Name}");
                    try
                    {
                        EnterMediaEditMode(element);
                        System.Diagnostics.Debug.WriteLine("预设菜单：成功调用EnterMediaEditMode");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"预设菜单：调用EnterMediaEditMode失败: {ex.Message}");
                    }
                };
                contextMenu.Items.Add(editModeItem);
                System.Diagnostics.Debug.WriteLine("添加了编辑模式菜单项");

                // 分隔符
                var separator1 = new Separator();
                try
                {
                    separator1.Style = Application.Current.FindResource("FigmaSeparatorStyle") as Style;
                }
                catch (Exception styleEx)
                {
                    System.Diagnostics.Debug.WriteLine($"无法找到FigmaSeparatorStyle: {styleEx.Message}");
                }
                contextMenu.Items.Add(separator1);

                // 删除媒体菜单项
                var deleteItem = new MenuItem
                {
                    Header = "删除媒体"
                };
                try
                {
                    deleteItem.Style = Application.Current.FindResource("FigmaMenuItemStyle") as Style;
                }
                catch (Exception styleEx)
                {
                    System.Diagnostics.Debug.WriteLine($"无法找到FigmaMenuItemStyle: {styleEx.Message}");
                }
                deleteItem.Icon = new TextBlock { Text = "🗑️", FontFamily = new FontFamily("Segoe UI Emoji"), FontSize = 14 };
                deleteItem.Click += (s, e) => DeleteMediaElement(element);
                contextMenu.Items.Add(deleteItem);

                // 设置右键菜单
                element.ContextMenu = contextMenu;
                System.Diagnostics.Debug.WriteLine($"成功为媒体元素设置右键菜单，菜单项数量: {contextMenu.Items.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置媒体元素右键菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 媒体元素预览鼠标左键按下事件
        /// </summary>
        private void MediaElement_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"媒体元素预览鼠标按下: ClickCount={e.ClickCount}, Sender={sender.GetType().Name}");

                // 🔧 删除视频控件交互检测，因为已删除视频功能

                // 在媒体编辑模式下，检查是否点击了当前编辑的元素
                if (_isInMediaEditMode)
                {
                    if (sender != _selectedMediaElement)
                    {
                        // 点击了其他元素，阻止操作
                        e.Handled = true;
                        System.Diagnostics.Debug.WriteLine("编辑模式下点击了其他媒体元素，已阻止");
                        return;
                    }
                }

                // 检查是否是双击
                if (e.ClickCount == 2)
                {
                    // 双击进入编辑模式，不阻止事件，让后续的双击处理器也能接收到
                    System.Diagnostics.Debug.WriteLine("PreviewMouseLeftButtonDown检测到双击，不阻止事件传播");
                    return;
                }

                // 单击时不阻止事件，让双击检测能正常工作
                System.Diagnostics.Debug.WriteLine("媒体元素单击，允许事件传播");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"媒体元素预览鼠标按下处理失败: {ex.Message}");
            }
        }

        // 🔧 删除视频控件交互元素检测方法

        /// <summary>
        /// 媒体元素预览鼠标移动事件
        /// </summary>
        private void MediaElement_PreviewMouseMove(object sender, MouseEventArgs e)
        {
            try
            {
                if (e.LeftButton == MouseButtonState.Pressed)
                {
                    // 阻止拖拽移动
                    e.Handled = true;
                    System.Diagnostics.Debug.WriteLine("媒体元素预览鼠标移动，已阻止拖拽");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"媒体元素预览鼠标移动处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 媒体元素预览鼠标左键释放事件
        /// </summary>
        private void MediaElement_PreviewMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            try
            {
                e.Handled = true;
                System.Diagnostics.Debug.WriteLine("媒体元素预览鼠标释放");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"媒体元素预览鼠标释放处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 媒体元素预览拖拽进入事件
        /// </summary>
        private void MediaElement_PreviewDragEnter(object sender, DragEventArgs e)
        {
            e.Effects = DragDropEffects.None;
            e.Handled = true;
        }

        /// <summary>
        /// 媒体元素预览拖拽悬停事件
        /// </summary>
        private void MediaElement_PreviewDragOver(object sender, DragEventArgs e)
        {
            e.Effects = DragDropEffects.None;
            e.Handled = true;
        }

        /// <summary>
        /// 媒体元素预览拖拽放置事件
        /// </summary>
        private void MediaElement_PreviewDrop(object sender, DragEventArgs e)
        {
            e.Effects = DragDropEffects.None;
            e.Handled = true;
        }

        /// <summary>
        /// 进入媒体编辑模式
        /// </summary>
        private void EnterMediaEditMode(FrameworkElement element)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== 开始进入媒体编辑模式 ===");
                System.Diagnostics.Debug.WriteLine($"媒体元素类型: {element?.GetType().Name ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"当前编辑模式状态: {_isInMediaEditMode}");

                // 🔧 添加null检查
                if (element == null)
                {
                    System.Diagnostics.Debug.WriteLine("错误：传入的媒体元素为null");
                    return;
                }

                // 退出之前的编辑模式
                ExitMediaEditMode();

                // 设置当前选中的媒体元素
                _selectedMediaElement = element;
                _isInMediaEditMode = true;

                // 禁用文本编辑
                richTextEditor.IsReadOnly = true;

                // 显示媒体编辑覆盖层
                mediaEditingOverlay.Visibility = Visibility.Visible;

                // 确保覆盖层在正确的位置
                Canvas.SetLeft(mediaEditingOverlay, 0);
                Canvas.SetTop(mediaEditingOverlay, 0);
                mediaEditingOverlay.Width = contentGrid.ActualWidth;
                mediaEditingOverlay.Height = contentGrid.ActualHeight;

                // 创建编辑覆盖层
                _currentEditingOverlay = new MediaEditingOverlay(mediaEditingOverlay, element, this);

                // 高亮显示选中的媒体元素
                HighlightMediaElement(element, true);

                statusText.Text = "媒体编辑模式 - 可拖拽角落调整尺寸，点击按钮退出";

                System.Diagnostics.Debug.WriteLine("=== 媒体编辑模式激活成功 ===");
                System.Diagnostics.Debug.WriteLine($"选中的媒体元素: {_selectedMediaElement?.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"编辑模式状态: {_isInMediaEditMode}");
                System.Diagnostics.Debug.WriteLine($"覆盖层可见性: {mediaEditingOverlay.Visibility}");
                System.Diagnostics.Debug.WriteLine($"文本编辑器只读状态: {richTextEditor.IsReadOnly}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"=== 进入媒体编辑模式失败 ===");
                System.Diagnostics.Debug.WriteLine($"错误信息: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                statusText.Text = "进入编辑模式失败";
            }
        }

        /// <summary>
        /// 退出媒体编辑模式
        /// </summary>
        public void ExitMediaEditMode()
        {
            try
            {
                if (_isInMediaEditMode)
                {
                    // 移除高亮
                    if (_selectedMediaElement != null)
                    {
                        HighlightMediaElement(_selectedMediaElement, false);
                    }

                    // 移除编辑覆盖层
                    _currentEditingOverlay?.Remove();
                    _currentEditingOverlay = null;

                    // 隐藏覆盖层
                    mediaEditingOverlay.Visibility = Visibility.Collapsed;

                    // 恢复文本编辑
                    richTextEditor.IsReadOnly = false;

                    // 重置状态
                    _selectedMediaElement = null;
                    _isInMediaEditMode = false;

                    statusText.Text = "已退出媒体编辑模式";
                    System.Diagnostics.Debug.WriteLine("已退出媒体编辑模式");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"退出媒体编辑模式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 高亮显示媒体元素
        /// </summary>
        private void HighlightMediaElement(FrameworkElement element, bool highlight)
        {
            try
            {
                if (highlight)
                {
                    // 添加高亮效果
                    element.Effect = new System.Windows.Media.Effects.DropShadowEffect
                    {
                        Color = Colors.Blue,
                        BlurRadius = 10,
                        ShadowDepth = 0,
                        Opacity = 0.6
                    };
                }
                else
                {
                    // 移除高亮效果
                    element.Effect = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置媒体元素高亮失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否处于媒体编辑模式
        /// </summary>
        public bool IsInMediaEditMode => _isInMediaEditMode;

        /// <summary>
        /// 获取当前选中的媒体元素
        /// </summary>
        public FrameworkElement? SelectedMediaElement => _selectedMediaElement;

        /// <summary>
        /// 启用媒体元素的拖拽调整尺寸功能
        /// </summary>
        private void EnableMediaResizeHandles(FrameworkElement element)
        {
            try
            {
                // 为不同类型的媒体元素启用拖拽功能
                if (element is ResizableImageControl imageControl)
                {
                    EnableImageResizeHandles(imageControl);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"启用媒体拖拽功能失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启用图片控件的拖拽调整功能
        /// </summary>
        private void EnableImageResizeHandles(ResizableImageControl imageControl)
        {
            try
            {
                // 调用图片控件的内置拖拽功能
                var method = imageControl.GetType().GetMethod("EnableResizeMode",
                    System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                method?.Invoke(imageControl, null);

                System.Diagnostics.Debug.WriteLine("图片拖拽调整功能已启用");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"启用图片拖拽功能失败: {ex.Message}");
                // 如果没有内置方法，添加自定义拖拽功能
                AddCustomResizeHandles(imageControl);
            }
        }

        // 🔧 删除音频和视频拖拽调整功能



        /// <summary>
        /// 添加自定义拖拽调整手柄
        /// </summary>
        private void AddCustomResizeHandles(FrameworkElement element, bool widthOnly = false)
        {
            try
            {
                // 创建拖拽调整覆盖层
                var resizeOverlay = new Canvas
                {
                    Background = Brushes.Transparent,
                    Width = element.ActualWidth,
                    Height = element.ActualHeight
                };

                // 添加拖拽手柄
                if (!widthOnly)
                {
                    // 右下角手柄（同时调整宽高）
                    var bottomRightHandle = CreateResizeHandle();
                    Canvas.SetRight(bottomRightHandle, -5);
                    Canvas.SetBottom(bottomRightHandle, -5);
                    bottomRightHandle.MouseLeftButtonDown += (s, e) => StartResize(element, ResizeDirection.BottomRight, e);
                    resizeOverlay.Children.Add(bottomRightHandle);

                    // 右边手柄（调整宽度）
                    var rightHandle = CreateResizeHandle();
                    Canvas.SetRight(rightHandle, -5);
                    Canvas.SetTop(rightHandle, (element.ActualHeight - 10) / 2);
                    rightHandle.MouseLeftButtonDown += (s, e) => StartResize(element, ResizeDirection.Right, e);
                    resizeOverlay.Children.Add(rightHandle);

                    // 底边手柄（调整高度）
                    var bottomHandle = CreateResizeHandle();
                    Canvas.SetLeft(bottomHandle, (element.ActualWidth - 10) / 2);
                    Canvas.SetBottom(bottomHandle, -5);
                    bottomHandle.MouseLeftButtonDown += (s, e) => StartResize(element, ResizeDirection.Bottom, e);
                    resizeOverlay.Children.Add(bottomHandle);
                }
                else
                {
                    // 只添加右边手柄（仅调整宽度）
                    var rightHandle = CreateResizeHandle();
                    Canvas.SetRight(rightHandle, -5);
                    Canvas.SetTop(rightHandle, (element.ActualHeight - 10) / 2);
                    rightHandle.MouseLeftButtonDown += (s, e) => StartResize(element, ResizeDirection.Right, e);
                    resizeOverlay.Children.Add(rightHandle);
                }

                // 将覆盖层添加到元素上
                if (element.Parent is Panel parentPanel)
                {
                    var elementIndex = parentPanel.Children.IndexOf(element);
                    parentPanel.Children.Insert(elementIndex + 1, resizeOverlay);
                }

                // 存储覆盖层引用以便后续移除
                element.Tag = resizeOverlay;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加自定义拖拽手柄失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建拖拽调整手柄
        /// </summary>
        private Rectangle CreateResizeHandle()
        {
            return new Rectangle
            {
                Width = 10,
                Height = 10,
                Fill = new SolidColorBrush(Color.FromRgb(66, 133, 244)), // Figma蓝色
                Stroke = Brushes.White,
                StrokeThickness = 2,
                Cursor = Cursors.SizeNWSE
            };
        }

        /// <summary>
        /// 开始拖拽调整尺寸
        /// </summary>
        private void StartResize(FrameworkElement element, ResizeDirection direction, MouseButtonEventArgs e)
        {
            try
            {
                var handle = e.Source as Rectangle;
                if (handle == null) return;

                handle.CaptureMouse();

                var startPoint = e.GetPosition(richTextEditor);
                var startWidth = element.ActualWidth;
                var startHeight = element.ActualHeight;

                MouseEventHandler mouseMoveHandler = null;
                MouseButtonEventHandler mouseUpHandler = null;

                mouseMoveHandler = (s, args) =>
                {
                    var currentPoint = args.GetPosition(richTextEditor);
                    var deltaX = currentPoint.X - startPoint.X;
                    var deltaY = currentPoint.Y - startPoint.Y;

                    switch (direction)
                    {
                        case ResizeDirection.Right:
                            var newWidth = Math.Max(50, startWidth + deltaX);
                            element.Width = newWidth;
                            break;

                        case ResizeDirection.Bottom:
                            var newHeight = Math.Max(30, startHeight + deltaY);
                            element.Height = newHeight;
                            break;

                        case ResizeDirection.BottomRight:
                            element.Width = Math.Max(50, startWidth + deltaX);
                            element.Height = Math.Max(30, startHeight + deltaY);
                            break;
                    }

                    // 更新覆盖层尺寸
                    if (element.Tag is Canvas overlay)
                    {
                        overlay.Width = element.ActualWidth;
                        overlay.Height = element.ActualHeight;
                    }
                };

                mouseUpHandler = (s, args) =>
                {
                    handle.ReleaseMouseCapture();
                    richTextEditor.MouseMove -= mouseMoveHandler;
                    richTextEditor.MouseLeftButtonUp -= mouseUpHandler;

                    statusText.Text = $"媒体尺寸已调整为 {element.ActualWidth:F0} x {element.ActualHeight:F0}";
                };

                richTextEditor.MouseMove += mouseMoveHandler;
                richTextEditor.MouseLeftButtonUp += mouseUpHandler;

                e.Handled = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"开始拖拽调整失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示尺寸调整对话框
        /// </summary>
        private void ShowResizeDialog(FrameworkElement element)
        {
            try
            {
                var dialog = new MediaResizeDialog(element.Width, element.Height);
                var parentWindow = Window.GetWindow(this);
                if (parentWindow != null)
                {
                    dialog.Owner = parentWindow;
                }

                if (dialog.ShowDialog() == true)
                {
                    element.Width = dialog.NewWidth;
                    element.Height = dialog.NewHeight;
                    statusText.Text = $"媒体尺寸已调整为 {dialog.NewWidth:F0} x {dialog.NewHeight:F0}";
                    System.Diagnostics.Debug.WriteLine($"媒体尺寸已调整: {dialog.NewWidth} x {dialog.NewHeight}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示尺寸调整对话框失败: {ex.Message}");
                MessageBox.Show($"调整尺寸失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 重置媒体尺寸
        /// </summary>
        private void ResetMediaSize(FrameworkElement element)
        {
            try
            {
                // 根据媒体类型设置默认尺寸
                if (element is ResizableImageControl)
                {
                    element.Width = 400;
                    element.Height = 300;
                }
                else
                {
                    element.Width = 400;
                    element.Height = 300;
                }

                statusText.Text = "媒体尺寸已重置";
                System.Diagnostics.Debug.WriteLine("媒体尺寸已重置");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重置媒体尺寸失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除媒体元素 - 新设计：直接删除附件文件夹中的图片，刷新所有相关瀑布流
        /// </summary>
        private void DeleteMediaElement(FrameworkElement element)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== 开始删除媒体元素: {element.GetType().Name} ===");

                // 获取图片文件路径
                string? filePath = null;
                if (element is ResizableImageControl imageControl)
                {
                    filePath = imageControl.GetImageFilePath();
                    System.Diagnostics.Debug.WriteLine($"获取图片文件路径: {filePath}");

                    // 验证文件路径是否有效
                    if (string.IsNullOrEmpty(filePath))
                    {
                        System.Diagnostics.Debug.WriteLine("警告：图片文件路径为空！");
                    }
                    else if (!File.Exists(filePath))
                    {
                        System.Diagnostics.Debug.WriteLine($"警告：图片文件不存在: {filePath}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"确认：图片文件存在: {filePath}");
                    }
                }

                // 从文档中移除元素
                var container = element.DataContext as InlineUIContainer;
                if (container != null)
                {
                    var paragraph = container.Parent as Paragraph;
                    if (paragraph != null)
                    {
                        paragraph.Inlines.Remove(container);
                        System.Diagnostics.Debug.WriteLine("已从文档中移除媒体元素");
                    }
                }

                // 如果有文件路径，执行删除操作
                if (!string.IsNullOrEmpty(filePath))
                {
                    System.Diagnostics.Debug.WriteLine($"开始执行删除操作: {filePath}");

                    // 1. 直接删除附件文件夹中的物理文件
                    bool deleteSuccess = DeletePhysicalImageFile(filePath);

                    // 2. 从媒体管理器中移除记录
                    RemoveFromMediaManager(filePath);

                    // 3. 触发删除事件，通知刷新所有相关的瀑布流（父节点、子节点、孙节点）
                    NotifyImageDeletedToAllNodes(filePath);

                    if (deleteSuccess)
                    {
                        statusText.Text = "图片已删除，正在刷新瀑布流...";
                        System.Diagnostics.Debug.WriteLine("图片删除成功，已触发瀑布流刷新");
                    }
                    else
                    {
                        statusText.Text = "图片删除可能失败，请检查文件权限";
                        System.Diagnostics.Debug.WriteLine("图片删除可能失败");
                    }
                }
                else
                {
                    statusText.Text = "删除失败：无法获取文件路径";
                    System.Diagnostics.Debug.WriteLine("删除失败：文件路径为空");
                }

                System.Diagnostics.Debug.WriteLine("=== 媒体元素删除完成 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除媒体元素失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                statusText.Text = "删除媒体元素失败";
            }
        }

        /// <summary>
        /// 删除物理图片文件 - 改进版，支持引用计数检查
        /// </summary>
        private bool DeletePhysicalImageFile(string filePath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"尝试删除物理文件: {filePath}");

                if (!File.Exists(filePath))
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 物理文件不存在: {filePath}");
                    return true; // 文件不存在也算删除成功
                }

                // 🔧 新增：检查文件是否被其他节点引用
                if (IsFileReferencedByOtherNodes(filePath))
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ 文件被其他节点引用，跳过物理删除: {filePath}");
                    return true; // 被其他节点引用时，不删除物理文件，但返回成功
                }

                // 确保文件不是只读的
                File.SetAttributes(filePath, FileAttributes.Normal);
                File.Delete(filePath);

                // 验证删除是否成功
                if (!File.Exists(filePath))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ 物理文件删除成功: {filePath}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ 物理文件删除失败，文件仍然存在: {filePath}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ 删除物理文件失败: {filePath}, {ex.Message}");

                // 如果直接删除失败，尝试延迟删除
                Task.Run(async () =>
                {
                    await Task.Delay(200);
                    try
                    {
                        if (File.Exists(filePath) && !IsFileReferencedByOtherNodes(filePath))
                        {
                            File.SetAttributes(filePath, FileAttributes.Normal);
                            File.Delete(filePath);

                            if (!File.Exists(filePath))
                            {
                                System.Diagnostics.Debug.WriteLine($"✅ 延迟删除物理文件成功: {filePath}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"❌ 延迟删除物理文件也失败: {filePath}");
                            }
                        }
                    }
                    catch (Exception delayEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ 延迟删除物理文件异常: {filePath}, {delayEx.Message}");
                    }
                });

                return false;
            }
        }

        /// <summary>
        /// 检查文件是否被其他节点引用 - 使用文件引用管理器
        /// </summary>
        private bool IsFileReferencedByOtherNodes(string filePath)
        {
            try
            {
                // 获取当前节点路径
                string currentNodePath = "";
                if (CurrentNodeData != null)
                {
                    var saveManager = SaveManager.Instance;
                    currentNodePath = saveManager.GetNodePath(CurrentNodeData);
                }

                return FileReferenceManager.Instance.IsFileReferencedByOtherNodes(filePath, currentNodePath);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查文件引用失败: {filePath}, {ex.Message}");
                return true; // 出错时保守处理，假设被引用
            }
        }

        /// <summary>
        /// 从媒体管理器中移除文件记录
        /// </summary>
        private void RemoveFromMediaManager(string filePath)
        {
            try
            {
                var mediaFile = _mediaManager.GetAllMediaFiles()
                    .FirstOrDefault(m => m.FilePath.Equals(filePath, StringComparison.OrdinalIgnoreCase));

                if (mediaFile != null)
                {
                    _mediaManager.RemoveMediaFile(mediaFile.Id);
                    System.Diagnostics.Debug.WriteLine($"已从媒体管理器中移除文件: {filePath}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"在媒体管理器中未找到文件记录: {filePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从媒体管理器移除文件失败: {filePath}, {ex.Message}");
            }
        }

        /// <summary>
        /// 通知图片已删除，触发所有相关节点的瀑布流刷新
        /// </summary>
        private void NotifyImageDeletedToAllNodes(string filePath)
        {
            try
            {
                // 触发媒体文件删除事件，这会通知MainWindow刷新所有相关的瀑布流
                MediaFileDeleted?.Invoke(this, new MediaFileEventArgs
                {
                    FilePath = filePath,
                    FileType = MediaFileType.Image,
                    FileName = System.IO.Path.GetFileName(filePath)
                });
                System.Diagnostics.Debug.WriteLine($"已触发MediaFileDeleted事件，将刷新所有相关节点: {filePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"触发删除事件失败: {filePath}, {ex.Message}");
            }
        }

        /// <summary>
        /// 更新格式化按钮状态
        /// </summary>
        private void UpdateFormattingButtons()
        {
            try
            {
                var selection = richTextEditor.Selection;
                if (selection.IsEmpty) return;

                // 格式化按钮已移至MainWindow，这里不再更新按钮状态
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新格式化按钮状态失败: {ex.Message}");
            }
        }





        /// <summary>
        /// 处理特殊按键
        /// </summary>
        private void HandleSpecialKeys(KeyEventArgs e)
        {
            try
            {
                // 只处理Delete键删除媒体元素，保持简洁
                if (e.Key == Key.Delete)
                {
                    HandleDeleteKeyForMediaElements();
                }

                // 可以在这里添加其他特殊按键处理逻辑
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理特殊按键失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理Delete键删除媒体元素
        /// </summary>
        private void HandleDeleteKeyForMediaElements()
        {
            try
            {
                var selection = richTextEditor.Selection;
                if (selection.IsEmpty)
                {
                    return;
                }

                System.Diagnostics.Debug.WriteLine("检测到Delete键，检查选中区域是否包含媒体元素");

                // 查找选中区域中的媒体元素
                var mediaElements = FindMediaElementsInSelection(selection);
                if (mediaElements.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"找到 {mediaElements.Count} 个媒体元素，准备删除");

                    // 删除所有选中的媒体元素
                    foreach (var mediaElement in mediaElements)
                    {
                        DeleteMediaElement(mediaElement);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理Delete键删除媒体元素失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 加载富文本内容
        /// </summary>
        private void LoadRichTextContent(string content)
        {
            try
            {
                if (string.IsNullOrEmpty(content))
                {
                    SetDefaultContent();
                    return;
                }

                // 尝试从DocumentSerializer加载完整文档
                if (_currentPageNode != null)
                {
                    var documentData = DocumentSerializer.LoadDocument(_currentPageNode.Id);
                    if (documentData != null)
                    {
                        var document = DocumentSerializer.DeserializeFlowDocument(documentData.Content);
                        if (document != null)
                        {
                            richTextEditor.Document = document;

                            // 修复媒体元素关联（解决重启后删除功能失效问题）
                            RepairMediaElementAssociations();

                            // 加载媒体文件
                            _mediaManager.ClearAllMediaFiles();
                            foreach (var mediaFile in documentData.MediaFiles)
                            {
                                if (File.Exists(mediaFile.FilePath))
                                {
                                    _mediaManager.AddMediaFile(mediaFile.FilePath, mediaFile.FileType);
                                }
                            }

                            System.Diagnostics.Debug.WriteLine("文档加载完成，媒体元素关联已修复");
                            return;
                        }
                    }
                }

                // 回退到简单文本加载
                var simpleDocument = new FlowDocument();
                var paragraph = new Paragraph(new Run(content));
                simpleDocument.Blocks.Add(paragraph);
                richTextEditor.Document = simpleDocument;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载富文本内容失败: {ex.Message}");
                SetDefaultContent();
            }
        }

        /// <summary>
        /// 获取富文本内容
        /// </summary>
        private string GetRichTextContent()
        {
            try
            {
                var textRange = new TextRange(richTextEditor.Document.ContentStart, richTextEditor.Document.ContentEnd);
                return textRange.Text;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取富文本内容失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 设置默认内容
        /// </summary>
        private void SetDefaultContent()
        {
            var document = new FlowDocument();
            var paragraph = new Paragraph(new Run("开始编写您的内容...") { Foreground = Brushes.Gray });
            document.Blocks.Add(paragraph);
            richTextEditor.Document = document;
        }

        /// <summary>
        /// 加载媒体文件
        /// </summary>
        private void LoadMediaFiles(PageNode pageNode)
        {
            try
            {
                // 这里可以加载与页面关联的媒体文件
                // 暂时为空实现，后续可以扩展
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载媒体文件失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 显示格式化菜单
        /// </summary>
        private void ShowFormattingMenu()
        {
            try
            {
                // 如果菜单已存在，先隐藏
                HideFormattingMenu();

                // 创建新菜单
                _currentFormattingMenu = new TextFormattingMenu();

                // 计算菜单位置
                var selection = richTextEditor.Selection;
                var selectionRect = selection.Start.GetCharacterRect(LogicalDirection.Forward);

                // 转换坐标到Canvas
                var canvasPosition = richTextEditor.TransformToAncestor(formattingMenuCanvas)
                    .Transform(new Point(selectionRect.Left, selectionRect.Bottom + 10));

                // 设置菜单位置
                Canvas.SetLeft(_currentFormattingMenu, canvasPosition.X);
                Canvas.SetTop(_currentFormattingMenu, canvasPosition.Y);

                // 绑定事件
                _currentFormattingMenu.OnFormatApplied += OnFormattingMenuFormatApplied;
                _currentFormattingMenu.OnMenuClosed += OnFormattingMenuClosed;

                // 添加到Canvas并显示
                formattingMenuCanvas.Children.Add(_currentFormattingMenu);
                _currentFormattingMenu.ShowWithAnimation();

                System.Diagnostics.Debug.WriteLine("格式化菜单已显示");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示格式化菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 隐藏格式化菜单
        /// </summary>
        private void HideFormattingMenu()
        {
            try
            {
                if (_currentFormattingMenu != null)
                {
                    // 解绑事件
                    _currentFormattingMenu.OnFormatApplied -= OnFormattingMenuFormatApplied;
                    _currentFormattingMenu.OnMenuClosed -= OnFormattingMenuClosed;

                    // 从Canvas移除
                    formattingMenuCanvas.Children.Remove(_currentFormattingMenu);
                    _currentFormattingMenu = null;

                    System.Diagnostics.Debug.WriteLine("格式化菜单已隐藏");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"隐藏格式化菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 格式化菜单格式应用事件处理
        /// </summary>
        private void OnFormattingMenuFormatApplied(TextFormat format)
        {
            try
            {
                ApplyAdvancedTextFormat(format);
                System.Diagnostics.Debug.WriteLine("高级文本格式已应用");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用高级文本格式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 格式化菜单关闭事件处理
        /// </summary>
        private void OnFormattingMenuClosed()
        {
            HideFormattingMenu();
        }

        /// <summary>
        /// 应用高级文本格式（来自TextFormattingMenu）
        /// </summary>
        private void ApplyAdvancedTextFormat(TextFormat format)
        {
            try
            {
                var selection = richTextEditor.Selection;
                if (selection.IsEmpty) return;

                // 应用字体系列
                if (!string.IsNullOrEmpty(format.FontFamily))
                {
                    selection.ApplyPropertyValue(TextElement.FontFamilyProperty, new FontFamily(format.FontFamily));
                }

                // 应用字体大小
                if (format.FontSize.HasValue)
                {
                    selection.ApplyPropertyValue(TextElement.FontSizeProperty, format.FontSize.Value);
                }

                // 应用字体颜色
                if (format.FontColor.HasValue)
                {
                    selection.ApplyPropertyValue(TextElement.ForegroundProperty, new SolidColorBrush(format.FontColor.Value));
                }

                // 应用粗体
                if (format.IsBold.HasValue)
                {
                    var weight = format.IsBold.Value ? FontWeights.Bold : FontWeights.Normal;
                    selection.ApplyPropertyValue(TextElement.FontWeightProperty, weight);
                }

                // 应用斜体
                if (format.IsItalic.HasValue)
                {
                    var style = format.IsItalic.Value ? FontStyles.Italic : FontStyles.Normal;
                    selection.ApplyPropertyValue(TextElement.FontStyleProperty, style);
                }

                // 应用下划线
                if (format.IsUnderline.HasValue)
                {
                    var decorations = format.IsUnderline.Value ? TextDecorations.Underline : null;
                    selection.ApplyPropertyValue(Inline.TextDecorationsProperty, decorations);
                }

                // 应用删除线
                if (format.IsStrikethrough.HasValue)
                {
                    var decorations = format.IsStrikethrough.Value ? TextDecorations.Strikethrough : null;
                    selection.ApplyPropertyValue(Inline.TextDecorationsProperty, decorations);
                }

                // 应用文本对齐
                if (format.TextAlignment.HasValue)
                {
                    var paragraph = selection.Start.Paragraph;
                    if (paragraph != null)
                    {
                        paragraph.TextAlignment = format.TextAlignment.Value;
                    }
                }

                richTextEditor.Focus();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用高级文本格式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制媒体文件到当前节点的附件文件夹
        /// </summary>
        private async Task<string> CopyMediaToCurrentNode(string sourceFilePath)
        {
            try
            {
                if (CurrentNodeData == null)
                {
                    System.Diagnostics.Debug.WriteLine("当前节点数据为空，无法复制媒体文件");
                    return string.Empty;
                }

                var saveManager = SaveManager.Instance;
                var copiedPath = await saveManager.SaveAttachmentAsync(CurrentNodeData, sourceFilePath);

                if (!string.IsNullOrEmpty(copiedPath))
                {
                    System.Diagnostics.Debug.WriteLine($"媒体文件已复制: {sourceFilePath} -> {copiedPath}");
                    return copiedPath;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"媒体文件复制失败: {sourceFilePath}");
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"复制媒体文件异常: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 显示临时媒体菜单
        /// </summary>
        private void ShowTemporaryMediaMenu(FrameworkElement mediaElement, Point mousePosition)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"创建临时媒体菜单，媒体元素类型: {mediaElement.GetType().Name}");

                var contextMenu = new ContextMenu();

                // 尝试应用样式
                try
                {
                    contextMenu.Style = Application.Current.FindResource("FigmaContextMenuStyle") as Style;
                }
                catch (Exception styleEx)
                {
                    System.Diagnostics.Debug.WriteLine($"无法应用FigmaContextMenuStyle: {styleEx.Message}");
                }

                // 编辑模式菜单项
                var editModeItem = new MenuItem
                {
                    Header = "编辑模式",
                    Icon = new TextBlock { Text = "✏️", FontFamily = new FontFamily("Segoe UI Emoji"), FontSize = 14 }
                };

                // 重要：添加调试信息到点击事件
                editModeItem.Click += (s, e) =>
                {
                    System.Diagnostics.Debug.WriteLine($"临时菜单：编辑模式被点击，媒体元素: {mediaElement.GetType().Name}");
                    try
                    {
                        EnterMediaEditMode(mediaElement);
                        System.Diagnostics.Debug.WriteLine("临时菜单：成功调用EnterMediaEditMode");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"临时菜单：调用EnterMediaEditMode失败: {ex.Message}");
                    }
                };
                contextMenu.Items.Add(editModeItem);

                // 分隔符
                contextMenu.Items.Add(new Separator());

                // 删除媒体菜单项
                var deleteItem = new MenuItem
                {
                    Header = "删除媒体",
                    Icon = new TextBlock { Text = "🗑️", FontFamily = new FontFamily("Segoe UI Emoji"), FontSize = 14 }
                };
                deleteItem.Click += (s, e) =>
                {
                    System.Diagnostics.Debug.WriteLine($"临时菜单：删除媒体被点击");
                    DeleteMediaElement(mediaElement);
                };
                contextMenu.Items.Add(deleteItem);

                // 显示菜单
                contextMenu.PlacementTarget = richTextEditor;
                contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint;
                contextMenu.IsOpen = true;

                System.Diagnostics.Debug.WriteLine($"临时媒体菜单已显示，菜单项数量: {contextMenu.Items.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示临时媒体菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取指定位置的媒体元素 - 简化版本
        /// </summary>
        private UIElement? GetElementAtPosition(Point position)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"检测位置: {position}");

                // 获取鼠标位置对应的文本位置
                var textPosition = richTextEditor.GetPositionFromPoint(position, true);
                if (textPosition == null)
                {
                    System.Diagnostics.Debug.WriteLine("无法获取文本位置");
                    return null;
                }

                // 检查前后相邻的元素
                var forwardElement = textPosition.GetAdjacentElement(LogicalDirection.Forward);
                var backwardElement = textPosition.GetAdjacentElement(LogicalDirection.Backward);

                System.Diagnostics.Debug.WriteLine($"Forward元素: {forwardElement?.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"Backward元素: {backwardElement?.GetType().Name}");

                // 检查Forward方向的InlineUIContainer
                if (forwardElement is InlineUIContainer forwardContainer && forwardContainer.Child != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Forward容器子元素: {forwardContainer.Child.GetType().Name}");
                    if (IsMediaElement(forwardContainer.Child))
                    {
                        System.Diagnostics.Debug.WriteLine($"找到媒体元素(Forward): {forwardContainer.Child.GetType().Name}");
                        return forwardContainer.Child;
                    }
                }

                // 检查Backward方向的InlineUIContainer
                if (backwardElement is InlineUIContainer backwardContainer && backwardContainer.Child != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Backward容器子元素: {backwardContainer.Child.GetType().Name}");
                    if (IsMediaElement(backwardContainer.Child))
                    {
                        System.Diagnostics.Debug.WriteLine($"找到媒体元素(Backward): {backwardContainer.Child.GetType().Name}");
                        return backwardContainer.Child;
                    }
                }

                System.Diagnostics.Debug.WriteLine("未找到媒体元素");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取位置元素失败: {ex.Message}");
                return null;
            }
        }





        #endregion
    }

    #region 辅助类和枚举

    /// <summary>
    /// 文本格式枚举
    /// </summary>
    public enum TextFormatType
    {
        Bold,
        Italic,
        Underline,
        FontSize,
        FontColor,
        FontFamily
    }

    /// <summary>
    /// 媒体文件事件参数
    /// </summary>
    public class MediaFileEventArgs : EventArgs
    {
        public string FilePath { get; set; } = string.Empty;
        public MediaFileType FileType { get; set; }
        public string FileName { get; set; } = string.Empty;
    }

    /// <summary>
    /// 媒体文件类型
    /// </summary>
    public enum MediaFileType
    {
        Image,
        Document
    }

    #endregion
}

/// <summary>
/// 媒体剪贴板数据类
/// </summary>
public class MediaClipboardData
{
    public string MediaType { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public double Width { get; set; }
    public double Height { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
}



/// <summary>
/// 媒体编辑覆盖层类 - 适用于RichTextBox环境
/// </summary>
public class MediaEditingOverlay
{
    private readonly Canvas _overlayCanvas;
    private readonly FrameworkElement _targetElement;
    private readonly 像素喵笔记.AdvancedRichTextEditor _editor;
    private readonly RichTextBox _richTextBox;

    // 控制按钮
    private Button? _cancelButton;
    private Button? _confirmButton;

    // 缩放控制界面
    private StackPanel? _controlPanel;
    private Border? _borderContainer;
    private StackPanel? _mainContainer;
    private Slider? _sizeSlider;
    private TextBlock? _sizeLabel;

    // 原始尺寸
    private Size _originalSize;

    public MediaEditingOverlay(Canvas overlayCanvas, FrameworkElement targetElement, 像素喵笔记.AdvancedRichTextEditor editor)
    {
        _overlayCanvas = overlayCanvas ?? throw new ArgumentNullException(nameof(overlayCanvas));
        _targetElement = targetElement ?? throw new ArgumentNullException(nameof(targetElement));
        _editor = editor ?? throw new ArgumentNullException(nameof(editor));

        // 获取RichTextBox引用
        _richTextBox = editor.richTextEditor;

        // 记录原始尺寸
        _originalSize = new Size(_targetElement.ActualWidth, _targetElement.ActualHeight);

        CreateOverlayElements();

        // 延迟更新位置，确保布局完成
        _overlayCanvas.Dispatcher.BeginInvoke(new Action(() =>
        {
            PositionControlPanelAtBottom();
            UpdateOverlayPosition();
        }), System.Windows.Threading.DispatcherPriority.Loaded);

        // 监听覆盖层画布尺寸变化，重新定位控制面板
        _overlayCanvas.SizeChanged += (s, e) => PositionControlPanelAtBottom();

        // 监听目标元素变化
        _targetElement.SizeChanged += OnTargetElementChanged;
    }

    /// <summary>
    /// 创建覆盖层元素
    /// </summary>
    private void CreateOverlayElements()
    {
        // 创建控制面板（包含按钮）
        CreateControlPanel();
    }

    /// <summary>
    /// 创建控制面板
    /// </summary>
    private void CreateControlPanel()
    {
        // 创建主水平容器（包含控制面板和按钮）
        _mainContainer = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center
        };

        // 创建边框容器（只包含控制面板）
        _borderContainer = new Border
        {
            Background = new SolidColorBrush(Color.FromArgb(250, 255, 255, 255)), // 更不透明的白色背景
            CornerRadius = new CornerRadius(8), // 圆角
            BorderBrush = new SolidColorBrush(Color.FromArgb(100, 200, 200, 200)), // 淡灰色边框
            BorderThickness = new Thickness(1),
            Margin = new Thickness(10),
            Width = 220 // 稍微窄一点，为按钮留空间
        };

        // 创建控制面板容器
        _controlPanel = new StackPanel
        {
            Orientation = Orientation.Vertical,
            Margin = new Thickness(0)
        };

        // 将StackPanel放入Border中
        _borderContainer.Child = _controlPanel;

        // 添加阴影效果到边框容器
        _borderContainer.Effect = new System.Windows.Media.Effects.DropShadowEffect
        {
            Color = Colors.Gray,
            Direction = 315,
            ShadowDepth = 4,
            Opacity = 0.4,
            BlurRadius = 8
        };

        // 创建标题
        var titleLabel = new TextBlock
        {
            Text = "调整图片大小",
            FontSize = 12,
            FontWeight = FontWeights.Bold,
            Foreground = new SolidColorBrush(Color.FromRgb(51, 51, 51)),
            Margin = new Thickness(10, 8, 10, 5),
            HorizontalAlignment = HorizontalAlignment.Center
        };

        // 创建尺寸滑动条
        _sizeSlider = new Slider
        {
            Minimum = 50,
            Maximum = 800,
            Value = _targetElement.ActualWidth,
            Margin = new Thickness(10, 5, 10, 5),
            TickFrequency = 50,
            IsSnapToTickEnabled = false
        };

        // 创建按钮容器（垂直排列，在控制面板右侧）
        var buttonPanel = new StackPanel
        {
            Orientation = Orientation.Vertical,
            Margin = new Thickness(5, 10, 10, 10), // 左边距5px，与控制面板分开
            VerticalAlignment = VerticalAlignment.Center
        };

        // 创建尺寸显示标签
        _sizeLabel = new TextBlock
        {
            Text = $"{(int)_targetElement.ActualWidth} × {(int)_targetElement.ActualHeight} px",
            FontSize = 10,
            Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
            Margin = new Thickness(10, 0, 10, 5),
            HorizontalAlignment = HorizontalAlignment.Center
        };

        // 创建取消按钮
        _cancelButton = new Button
        {
            Content = "×",
            Width = 32,
            Height = 32,
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Background = Brushes.White, // 白色背景
            Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(218, 220, 224)), // Figma风格边框色
            BorderThickness = new Thickness(1),
            Margin = new Thickness(0, 0, 0, 8), // 下边距8px
            Cursor = Cursors.Hand,
            ToolTip = "取消编辑"
        };

        // 添加Figma风格阴影效果（参考滑动条样式）
        _cancelButton.Effect = new System.Windows.Media.Effects.DropShadowEffect
        {
            Color = Color.FromArgb(51, 0, 0, 0), // 20%透明度的黑色，更明显
            Direction = 315, // 左下方向
            ShadowDepth = 3,
            BlurRadius = 5,
            Opacity = 0.3
        };

        // 创建确认按钮
        _confirmButton = new Button
        {
            Content = "√",
            Width = 32,
            Height = 32,
            FontSize = 18,
            FontWeight = FontWeights.Bold,
            Background = Brushes.White, // 白色背景
            Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
            BorderBrush = new SolidColorBrush(Color.FromRgb(218, 220, 224)), // Figma风格边框色
            BorderThickness = new Thickness(1),
            Margin = new Thickness(0, 0, 0, 0),
            Cursor = Cursors.Hand,
            ToolTip = "确认修改"
        };

        // 添加Figma风格阴影效果（参考滑动条样式）
        _confirmButton.Effect = new System.Windows.Media.Effects.DropShadowEffect
        {
            Color = Color.FromArgb(51, 0, 0, 0), // 20%透明度的黑色，更明显
            Direction = 315, // 左下方向
            ShadowDepth = 3,
            BlurRadius = 5,
            Opacity = 0.3
        };

        // 为按钮添加圆角模板
        var buttonTemplate = CreateFigmaButtonTemplate();
        _cancelButton.Template = buttonTemplate;
        _confirmButton.Template = buttonTemplate;

        // 添加按钮悬停效果
        _cancelButton.MouseEnter += (s, e) =>
        {
            _cancelButton.Background = new SolidColorBrush(Color.FromRgb(220, 53, 69)); // 红色
            _cancelButton.Foreground = Brushes.White;
        };
        _cancelButton.MouseLeave += (s, e) =>
        {
            _cancelButton.Background = Brushes.White; // 恢复白色背景
            _cancelButton.Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102));
        };

        _confirmButton.MouseEnter += (s, e) =>
        {
            _confirmButton.Background = new SolidColorBrush(Color.FromRgb(0, 153, 255)); // 蓝色 #0099ff
            _confirmButton.Foreground = Brushes.White;
        };
        _confirmButton.MouseLeave += (s, e) =>
        {
            _confirmButton.Background = Brushes.White; // 恢复白色背景
            _confirmButton.Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102));
        };

        // 添加按钮事件
        _cancelButton.Click += CancelButton_Click;
        _confirmButton.Click += ConfirmButton_Click;

        // 将按钮添加到按钮容器
        buttonPanel.Children.Add(_cancelButton);
        buttonPanel.Children.Add(_confirmButton);

        // 添加滑动条事件
        _sizeSlider.ValueChanged += SizeSlider_ValueChanged;

        // 组装控制面板
        _controlPanel.Children.Add(titleLabel);
        _controlPanel.Children.Add(_sizeSlider);
        _controlPanel.Children.Add(_sizeLabel);

        // 组装主容器
        _mainContainer.Children.Add(_borderContainer);
        _mainContainer.Children.Add(buttonPanel);

        Panel.SetZIndex(_mainContainer, 1000);
        _overlayCanvas.Children.Add(_mainContainer);
    }

    /// <summary>
    /// 创建Figma风格按钮模板
    /// </summary>
    private ControlTemplate CreateFigmaButtonTemplate()
    {
        var template = new ControlTemplate(typeof(Button));

        // 创建边框元素
        var borderFactory = new FrameworkElementFactory(typeof(Border));
        borderFactory.Name = "border";

        // 绑定属性
        borderFactory.SetBinding(Border.BackgroundProperty,
            new Binding("Background") { RelativeSource = new RelativeSource(RelativeSourceMode.TemplatedParent) });
        borderFactory.SetBinding(Border.BorderBrushProperty,
            new Binding("BorderBrush") { RelativeSource = new RelativeSource(RelativeSourceMode.TemplatedParent) });
        borderFactory.SetBinding(Border.BorderThicknessProperty,
            new Binding("BorderThickness") { RelativeSource = new RelativeSource(RelativeSourceMode.TemplatedParent) });

        // 设置圆角
        borderFactory.SetValue(Border.CornerRadiusProperty, new CornerRadius(6));

        // 创建内容展示器
        var contentFactory = new FrameworkElementFactory(typeof(ContentPresenter));
        contentFactory.SetValue(ContentPresenter.HorizontalAlignmentProperty, HorizontalAlignment.Center);
        contentFactory.SetValue(ContentPresenter.VerticalAlignmentProperty, VerticalAlignment.Center);
        contentFactory.SetBinding(ContentPresenter.ContentProperty,
            new Binding("Content") { RelativeSource = new RelativeSource(RelativeSourceMode.TemplatedParent) });

        // 组装模板
        borderFactory.AppendChild(contentFactory);
        template.VisualTree = borderFactory;

        return template;
    }

    /// <summary>
    /// 取消按钮点击事件
    /// </summary>
    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 恢复原始尺寸
            _targetElement.Width = _originalSize.Width;
            _targetElement.Height = _originalSize.Height;

            // 更新滑动条和标签显示
            if (_sizeSlider != null)
            {
                _sizeSlider.Value = _originalSize.Width;
            }

            if (_sizeLabel != null)
            {
                _sizeLabel.Text = $"{(int)_originalSize.Width} × {(int)_originalSize.Height} px";
            }

            // 退出编辑模式
            _editor.ExitMediaEditMode();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"取消编辑失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 确认按钮点击事件
    /// </summary>
    private void ConfirmButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 直接退出编辑模式，保持当前尺寸
            _editor.ExitMediaEditMode();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"确认编辑失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 滑动条值变化事件处理
    /// </summary>
    private void SizeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (_sizeSlider == null || _sizeLabel == null) return;

        try
        {
            var newWidth = e.NewValue;

            // 保持宽高比
            var aspectRatio = _originalSize.Width / _originalSize.Height;
            var newHeight = newWidth / aspectRatio;

            // 应用新尺寸
            _targetElement.Width = newWidth;
            _targetElement.Height = newHeight;

            // 更新显示标签
            _sizeLabel.Text = $"{(int)newWidth} × {(int)newHeight} px";

            // 按钮现在在控制面板中，不需要单独更新位置
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"调整尺寸失败: {ex.Message}");
        }
    }









    /// <summary>
    /// 更新覆盖层位置
    /// </summary>
    private void UpdateOverlayPosition()
    {
        if (_targetElement == null || _controlPanel == null) return;

        try
        {
            // 将控制面板固定在编辑器底部中央
            PositionControlPanelAtBottom();

            // 按钮现在在控制面板中，不需要单独更新位置
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"更新覆盖层位置失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 将控制面板固定在编辑器底部中央
    /// </summary>
    private void PositionControlPanelAtBottom()
    {
        if (_mainContainer == null) return;

        try
        {
            // 获取覆盖层画布的尺寸
            var canvasWidth = _overlayCanvas.ActualWidth;
            var canvasHeight = _overlayCanvas.ActualHeight;

            // 计算主容器的估算宽度（控制面板220px + 按钮区域约50px + 间距）
            var estimatedWidth = 285;

            // 将主容器放置在画布底部中央
            var panelX = (canvasWidth - estimatedWidth) / 2;
            var panelY = canvasHeight - 100; // 距离底部100px，给控制面板留足空间

            Canvas.SetLeft(_mainContainer, panelX);
            Canvas.SetTop(_mainContainer, panelY);

            System.Diagnostics.Debug.WriteLine($"控制面板位置: X={panelX}, Y={panelY}, 画布尺寸: {canvasWidth}x{canvasHeight}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"定位控制面板失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取目标元素在覆盖层画布中的边界
    /// </summary>
    private Rect GetElementBounds()
    {
        try
        {
            // 使用屏幕坐标方法，更可靠
            var elementScreenPos = _targetElement.PointToScreen(new Point(0, 0));
            var canvasScreenPos = _overlayCanvas.PointToScreen(new Point(0, 0));

            var relativeX = elementScreenPos.X - canvasScreenPos.X;
            var relativeY = elementScreenPos.Y - canvasScreenPos.Y;

            return new Rect(relativeX, relativeY, _targetElement.ActualWidth, _targetElement.ActualHeight);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"获取元素边界失败: {ex.Message}");
            return Rect.Empty;
        }
    }





    /// <summary>
    /// 目标元素变化事件处理
    /// </summary>
    private void OnTargetElementChanged(object sender, EventArgs e)
    {
        UpdateOverlayPosition();
    }

    /// <summary>
    /// 调试坐标信息
    /// </summary>
    private void DebugCoordinates()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("=== 坐标调试信息 ===");

            // 目标元素信息
            System.Diagnostics.Debug.WriteLine($"目标元素尺寸: {_targetElement.ActualWidth} x {_targetElement.ActualHeight}");

            // RichTextBox信息
            var richTextBoxBounds = new Rect(0, 0, _richTextBox.ActualWidth, _richTextBox.ActualHeight);
            System.Diagnostics.Debug.WriteLine($"RichTextBox尺寸: {richTextBoxBounds}");

            // 覆盖层画布信息
            var overlayBounds = new Rect(0, 0, _overlayCanvas.ActualWidth, _overlayCanvas.ActualHeight);
            System.Diagnostics.Debug.WriteLine($"覆盖层画布尺寸: {overlayBounds}");

            // 坐标转换测试
            try
            {
                var directTransform = _targetElement.TransformToAncestor(_overlayCanvas);
                var directPoint = directTransform.Transform(new Point(0, 0));
                System.Diagnostics.Debug.WriteLine($"直接转换结果: {directPoint}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"直接转换失败: {ex.Message}");
            }

            try
            {
                var richTextBoxTransform = _richTextBox.TransformToAncestor(_overlayCanvas);
                var richTextBoxPoint = richTextBoxTransform.Transform(new Point(0, 0));
                System.Diagnostics.Debug.WriteLine($"RichTextBox在覆盖层中的位置: {richTextBoxPoint}");

                var elementTransform = _targetElement.TransformToAncestor(_richTextBox);
                var elementPoint = elementTransform.Transform(new Point(0, 0));
                System.Diagnostics.Debug.WriteLine($"元素在RichTextBox中的位置: {elementPoint}");

                var finalPoint = new Point(richTextBoxPoint.X + elementPoint.X, richTextBoxPoint.Y + elementPoint.Y);
                System.Diagnostics.Debug.WriteLine($"计算的最终位置: {finalPoint}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"分步转换失败: {ex.Message}");
            }

            System.Diagnostics.Debug.WriteLine("=== 调试信息结束 ===");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"调试失败: {ex.Message}");
        }
    }









    /// <summary>
    /// 移除覆盖层
    /// </summary>
    public void Remove()
    {
        try
        {
            // 移除事件监听
            if (_targetElement != null)
            {
                _targetElement.SizeChanged -= OnTargetElementChanged;
            }

            // 移除滑动条事件
            if (_sizeSlider != null)
            {
                _sizeSlider.ValueChanged -= SizeSlider_ValueChanged;
            }

            // 清理覆盖层画布
            _overlayCanvas.Children.Clear();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"移除覆盖层失败: {ex.Message}");
        }
    }
}

/// <summary>
/// 扩展的拖拽方向枚举
/// </summary>
public enum ResizeDirection
{
    Right,
    Bottom,
    BottomRight,
    TopLeft,
    TopRight,
    BottomLeft
}

/// <summary>
/// 编号格式辅助方法
/// </summary>
public static class NumberingHelper
{
    /// <summary>
    /// 获取圆圈数字
    /// </summary>
    public static string GetCircledNumber(int number)
    {
        if (number >= 1 && number <= 20)
        {
            string[] circledNumbers = { "①", "②", "③", "④", "⑤", "⑥", "⑦", "⑧", "⑨", "⑩",
                                      "⑪", "⑫", "⑬", "⑭", "⑮", "⑯", "⑰", "⑱", "⑲", "⑳" };
            return circledNumbers[number - 1];
        }
        return number.ToString();
    }

    /// <summary>
    /// 获取大写字母
    /// </summary>
    public static string GetUppercaseLetter(int number)
    {
        if (number >= 1 && number <= 26)
        {
            return ((char)('A' + number - 1)).ToString();
        }
        return number.ToString();
    }

    /// <summary>
    /// 获取小写字母
    /// </summary>
    public static string GetLowercaseLetter(int number)
    {
        if (number >= 1 && number <= 26)
        {
            return ((char)('a' + number - 1)).ToString();
        }
        return number.ToString();
    }

    /// <summary>
    /// 获取大写罗马数字
    /// </summary>
    public static string GetUppercaseRoman(int number)
    {
        if (number >= 1 && number <= 20)
        {
            string[] romans = { "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X",
                              "XI", "XII", "XIII", "XIV", "XV", "XVI", "XVII", "XVIII", "XIX", "XX" };
            return romans[number - 1];
        }
        return number.ToString();
    }

    /// <summary>
    /// 获取小写罗马数字
    /// </summary>
    public static string GetLowercaseRoman(int number)
    {
        if (number >= 1 && number <= 20)
        {
            string[] romans = { "i", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x",
                              "xi", "xii", "xiii", "xiv", "xv", "xvi", "xvii", "xviii", "xix", "xx" };
            return romans[number - 1];
        }
        return number.ToString();
    }

    /// <summary>
    /// 获取中文数字
    /// </summary>
    public static string GetChineseNumber(int number)
    {
        if (number >= 1 && number <= 10)
        {
            string[] chineseNumbers = { "一", "二", "三", "四", "五", "六", "七", "八", "九", "十" };
            return chineseNumbers[number - 1];
        }
        return number.ToString();
    }


}
