<Window x:Class="像素喵笔记.AdvancedSmoothingDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="高级平滑设置"
        Width="450"
        Height="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F8F9FA">

    <Window.Resources>
        <!-- 🔧 修复：引用项目的Figma样式资源 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 本地样式 -->
            <Style x:Key="LocalSliderStyle" TargetType="Slider">
                <Setter Property="Height" Value="20"/>
                <Setter Property="Margin" Value="0,4"/>
            </Style>

            <Style x:Key="LocalTextBlockStyle" TargetType="TextBlock">
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Foreground" Value="#333333"/>
                <Setter Property="VerticalAlignment" Value="Center"/>
            </Style>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="专业级平滑设置"
                   FontSize="16"
                   FontWeight="Bold"
                   Foreground="#1A1A1A"
                   Margin="0,0,0,20"/>

        <!-- 设置面板 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- 抖动修正设置 -->
                <GroupBox Header="抖动修正 (Anti-Shake)" Margin="0,0,0,16" Padding="12">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="抖动半径:" Style="{StaticResource LocalTextBlockStyle}"/>
                            <Slider Grid.Column="1"
                                   x:Name="antiShakeRadiusSlider"
                                   Minimum="1"
                                   Maximum="10"
                                   Value="3"
                                   Style="{StaticResource LocalSliderStyle}"
                                   ValueChanged="AntiShakeRadiusSlider_ValueChanged"/>
                            <TextBlock Grid.Column="2"
                                      x:Name="antiShakeRadiusText"
                                      Text="3px"
                                      Style="{StaticResource LocalTextBlockStyle}"
                                      HorizontalAlignment="Right"/>
                        </Grid>

                        <TextBlock Text="检测并修正小幅度的手部抖动，数值越大修正范围越广"
                                  FontSize="10"
                                  Foreground="#666666"
                                  Margin="0,4,0,0"
                                  TextWrapping="Wrap"/>
                    </StackPanel>
                </GroupBox>

                <!-- 稳定器设置 -->
                <GroupBox Header="稳定器 (Stabilizer)" Margin="0,0,0,16" Padding="12">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="延迟强度:" Style="{StaticResource LocalTextBlockStyle}"/>
                            <Slider Grid.Column="1"
                                   x:Name="stabilizerDelaySlider"
                                   Minimum="0"
                                   Maximum="1"
                                   Value="0.1"
                                   Style="{StaticResource LocalSliderStyle}"
                                   ValueChanged="StabilizerDelaySlider_ValueChanged"/>
                            <TextBlock Grid.Column="2"
                                      x:Name="stabilizerDelayText"
                                      Text="10%"
                                      Style="{StaticResource LocalTextBlockStyle}"
                                      HorizontalAlignment="Right"/>
                        </Grid>

                        <TextBlock Text="通过时间延迟来稳定笔迹，数值越大延迟越明显但线条越稳定"
                                  FontSize="10"
                                  Foreground="#666666"
                                  Margin="0,4,0,0"
                                  TextWrapping="Wrap"/>
                    </StackPanel>
                </GroupBox>

                <!-- 流畅度设置 -->
                <GroupBox Header="流畅度 (Flow Rate)" Margin="0,0,0,16" Padding="12">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="流畅度:" Style="{StaticResource LocalTextBlockStyle}"/>
                            <Slider Grid.Column="1"
                                   x:Name="flowRateSlider"
                                   Minimum="0.1"
                                   Maximum="1"
                                   Value="0.8"
                                   Style="{StaticResource LocalSliderStyle}"
                                   ValueChanged="FlowRateSlider_ValueChanged"/>
                            <TextBlock Grid.Column="2"
                                      x:Name="flowRateText"
                                      Text="80%"
                                      Style="{StaticResource LocalTextBlockStyle}"
                                      HorizontalAlignment="Right"/>
                        </Grid>

                        <TextBlock Text="控制线条的流畅程度，数值越高线条越流畅但可能失去细节"
                                  FontSize="10"
                                  Foreground="#666666"
                                  Margin="0,4,0,0"
                                  TextWrapping="Wrap"/>
                    </StackPanel>
                </GroupBox>

                <!-- 高级选项 -->
                <GroupBox Header="高级选项" Margin="0,0,0,16" Padding="12">
                    <StackPanel>
                        <!-- 缓冲区大小 -->
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="缓冲区大小:" Style="{StaticResource LocalTextBlockStyle}"/>
                            <Slider Grid.Column="1"
                                   x:Name="bufferSizeSlider"
                                   Minimum="2"
                                   Maximum="16"
                                   Value="8"
                                   Style="{StaticResource LocalSliderStyle}"
                                   ValueChanged="BufferSizeSlider_ValueChanged"/>
                            <TextBlock Grid.Column="2"
                                      x:Name="bufferSizeText"
                                      Text="8"
                                      Style="{StaticResource LocalTextBlockStyle}"
                                      HorizontalAlignment="Right"/>
                        </Grid>

                        <!-- 最小距离 -->
                        <Grid Margin="0,0,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="最小距离:" Style="{StaticResource LocalTextBlockStyle}"/>
                            <Slider Grid.Column="1"
                                   x:Name="minDistanceSlider"
                                   Minimum="0.5"
                                   Maximum="5"
                                   Value="1"
                                   Style="{StaticResource LocalSliderStyle}"
                                   ValueChanged="MinDistanceSlider_ValueChanged"/>
                            <TextBlock Grid.Column="2"
                                      x:Name="minDistanceText"
                                      Text="1px"
                                      Style="{StaticResource LocalTextBlockStyle}"
                                      HorizontalAlignment="Right"/>
                        </Grid>

                        <!-- 启用预测 -->
                        <CheckBox x:Name="enablePredictionCheckBox"
                                 Content="启用预测算法"
                                 IsChecked="True"
                                 FontSize="12"
                                 Margin="0,8,0,0"
                                 Checked="EnablePredictionCheckBox_Changed"
                                 Unchecked="EnablePredictionCheckBox_Changed"/>

                        <TextBlock Text="基于速度和加速度预测下一个点的位置，提高响应性"
                                  FontSize="10"
                                  Foreground="#666666"
                                  Margin="20,4,0,0"
                                  TextWrapping="Wrap"/>
                    </StackPanel>
                </GroupBox>

                <!-- 预设配置 -->
                <GroupBox Header="预设配置" Margin="0,0,0,16" Padding="12">
                    <StackPanel Orientation="Horizontal">
                        <Button Content="轻度平滑"
                               Style="{StaticResource FigmaButtonStyle}"
                               Background="#E3F2FD"
                               BorderBrush="#2196F3"
                               Margin="0,0,8,0"
                               Click="PresetLight_Click"/>
                        <Button Content="中度平滑"
                               Style="{StaticResource FigmaButtonStyle}"
                               Background="#E8F5E8"
                               BorderBrush="#4CAF50"
                               Margin="0,0,8,0"
                               Click="PresetMedium_Click"/>
                        <Button Content="重度平滑"
                               Style="{StaticResource FigmaButtonStyle}"
                               Background="#FFF3E0"
                               BorderBrush="#FF9800"
                               Margin="0,0,8,0"
                               Click="PresetHeavy_Click"/>
                        <Button Content="专业绘画"
                               Style="{StaticResource FigmaButtonStyle}"
                               Background="#F3E5F5"
                               BorderBrush="#9C27B0"
                               Click="PresetProfessional_Click"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2"
                   Orientation="Horizontal"
                   HorizontalAlignment="Right"
                   Margin="0,20,0,0">
            <Button Content="重置默认"
                   Style="{StaticResource FigmaButtonStyle}"
                   Background="Transparent"
                   BorderBrush="#E0E0E0"
                   Margin="0,0,12,0"
                   Click="ResetDefaults_Click"/>
            <Button Content="取消"
                   Style="{StaticResource FigmaButtonStyle}"
                   Background="Transparent"
                   BorderBrush="#E0E0E0"
                   Margin="0,0,8,0"
                   Click="Cancel_Click"/>
            <Button Content="确定"
                   Style="{StaticResource FigmaButtonStyle}"
                   Background="#0099FF"
                   BorderBrush="#0099FF"
                   Foreground="White"
                   Click="OK_Click"/>
        </StackPanel>
    </Grid>
</Window>
