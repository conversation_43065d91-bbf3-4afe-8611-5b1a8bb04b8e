using System;
using System.Windows;

namespace 像素喵笔记
{
    /// <summary>
    /// AdvancedSmoothingDialog.xaml 的交互逻辑
    /// </summary>
    public partial class AdvancedSmoothingDialog : Window
    {
        public AdvancedSmoothingEngine.SmoothingConfig Config { get; private set; }
        public new bool DialogResult { get; private set; } = false;

        public AdvancedSmoothingDialog(AdvancedSmoothingEngine.SmoothingConfig currentConfig)
        {
            InitializeComponent();
            Config = new AdvancedSmoothingEngine.SmoothingConfig
            {
                Mode = currentConfig.Mode,
                Strength = currentConfig.Strength,
                AntiShakeRadius = currentConfig.AntiShakeRadius,
                StabilizerDelay = currentConfig.StabilizerDelay,
                FlowRate = currentConfig.FlowRate,
                BufferSize = currentConfig.BufferSize,
                MinDistance = currentConfig.MinDistance,
                EnablePrediction = currentConfig.EnablePrediction
            };

            LoadConfigToUI();
        }

        private void LoadConfigToUI()
        {
            antiShakeRadiusSlider.Value = Config.AntiShakeRadius;
            stabilizerDelaySlider.Value = Config.StabilizerDelay;
            flowRateSlider.Value = Config.FlowRate;
            bufferSizeSlider.Value = Config.BufferSize;
            minDistanceSlider.Value = Config.MinDistance;
            enablePredictionCheckBox.IsChecked = Config.EnablePrediction;

            UpdateAllTexts();
        }

        private void UpdateAllTexts()
        {
            antiShakeRadiusText.Text = $"{antiShakeRadiusSlider.Value:F1}px";
            stabilizerDelayText.Text = $"{stabilizerDelaySlider.Value * 100:F0}%";
            flowRateText.Text = $"{flowRateSlider.Value * 100:F0}%";
            bufferSizeText.Text = $"{bufferSizeSlider.Value:F0}";
            minDistanceText.Text = $"{minDistanceSlider.Value:F1}px";
        }

        private void SaveConfigFromUI()
        {
            Config.AntiShakeRadius = antiShakeRadiusSlider.Value;
            Config.StabilizerDelay = stabilizerDelaySlider.Value;
            Config.FlowRate = flowRateSlider.Value;
            Config.BufferSize = (int)bufferSizeSlider.Value;
            Config.MinDistance = minDistanceSlider.Value;
            Config.EnablePrediction = enablePredictionCheckBox.IsChecked ?? true;
        }

        #region 滑块事件处理

        private void AntiShakeRadiusSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (antiShakeRadiusText != null)
                antiShakeRadiusText.Text = $"{e.NewValue:F1}px";
        }

        private void StabilizerDelaySlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (stabilizerDelayText != null)
                stabilizerDelayText.Text = $"{e.NewValue * 100:F0}%";
        }

        private void FlowRateSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (flowRateText != null)
                flowRateText.Text = $"{e.NewValue * 100:F0}%";
        }

        private void BufferSizeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (bufferSizeText != null)
                bufferSizeText.Text = $"{e.NewValue:F0}";
        }

        private void MinDistanceSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (minDistanceText != null)
                minDistanceText.Text = $"{e.NewValue:F1}px";
        }

        private void EnablePredictionCheckBox_Changed(object sender, RoutedEventArgs e)
        {
            // 预测算法状态改变时的处理
        }

        #endregion

        #region 预设配置

        private void PresetLight_Click(object sender, RoutedEventArgs e)
        {
            // 轻度平滑预设
            antiShakeRadiusSlider.Value = 2.0;
            stabilizerDelaySlider.Value = 0.05;
            flowRateSlider.Value = 0.9;
            bufferSizeSlider.Value = 4;
            minDistanceSlider.Value = 1.0;
            enablePredictionCheckBox.IsChecked = false;
        }

        private void PresetMedium_Click(object sender, RoutedEventArgs e)
        {
            // 中度平滑预设
            antiShakeRadiusSlider.Value = 3.0;
            stabilizerDelaySlider.Value = 0.1;
            flowRateSlider.Value = 0.8;
            bufferSizeSlider.Value = 6;
            minDistanceSlider.Value = 1.0;
            enablePredictionCheckBox.IsChecked = true;
        }

        private void PresetHeavy_Click(object sender, RoutedEventArgs e)
        {
            // 重度平滑预设
            antiShakeRadiusSlider.Value = 5.0;
            stabilizerDelaySlider.Value = 0.2;
            flowRateSlider.Value = 0.6;
            bufferSizeSlider.Value = 10;
            minDistanceSlider.Value = 1.5;
            enablePredictionCheckBox.IsChecked = true;
        }

        private void PresetProfessional_Click(object sender, RoutedEventArgs e)
        {
            // 专业绘画预设
            antiShakeRadiusSlider.Value = 4.0;
            stabilizerDelaySlider.Value = 0.15;
            flowRateSlider.Value = 0.75;
            bufferSizeSlider.Value = 8;
            minDistanceSlider.Value = 0.8;
            enablePredictionCheckBox.IsChecked = true;
        }

        private void ResetDefaults_Click(object sender, RoutedEventArgs e)
        {
            // 重置为默认值
            var defaultConfig = new AdvancedSmoothingEngine.SmoothingConfig();
            Config = defaultConfig;
            LoadConfigToUI();
        }

        #endregion

        #region 按钮事件

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            SaveConfigFromUI();
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        #endregion
    }
}
