using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;

namespace 像素喵笔记
{
    /// <summary>
    /// 高级平滑引擎 - 专业绘画软件级别的平滑功能
    /// 包含抖动修正、稳定器、流畅度等功能
    /// </summary>
    public class AdvancedSmoothingEngine
    {
        /// <summary>
        /// 平滑模式枚举
        /// </summary>
        public enum SmoothingMode
        {
            None,           // 无平滑
            Basic,          // 基础平滑
            AntiShake,      // 抖动修正
            Stabilizer,     // 稳定器
            Professional   // 专业模式（综合所有功能）
        }

        /// <summary>
        /// 平滑配置参数
        /// </summary>
        public class SmoothingConfig
        {
            public SmoothingMode Mode { get; set; } = SmoothingMode.Basic;
            public double Strength { get; set; } = 0.3;           // 平滑强度 0-1
            public double AntiShakeRadius { get; set; } = 3.0;    // 抖动修正半径（像素）
            public double StabilizerDelay { get; set; } = 0.1;    // 稳定器延迟（0-1）
            public double FlowRate { get; set; } = 0.8;           // 流畅度（0-1）
            public int BufferSize { get; set; } = 8;              // 缓冲区大小
            public double MinDistance { get; set; } = 1.0;        // 最小采样距离
            public bool EnablePrediction { get; set; } = true;    // 启用预测
        }

        private readonly SmoothingConfig _config;
        private readonly Queue<Point> _pointBuffer;
        private readonly Queue<DateTime> _timeBuffer;
        private readonly List<Point> _rawPoints;
        private Point _lastSmoothedPoint;
        private Point _lastRawPoint;
        private Vector _velocity;
        private Vector _acceleration;
        private DateTime _lastUpdateTime;
        private bool _isInitialized;

        public AdvancedSmoothingEngine(SmoothingConfig config)
        {
            _config = config ?? new SmoothingConfig();
            _pointBuffer = new Queue<Point>();
            _timeBuffer = new Queue<DateTime>();
            _rawPoints = new List<Point>();
            _velocity = new Vector(0, 0);
            _acceleration = new Vector(0, 0);
            _lastUpdateTime = DateTime.Now;
            _isInitialized = false;
        }

        /// <summary>
        /// 重置平滑引擎状态
        /// </summary>
        public void Reset()
        {
            _pointBuffer.Clear();
            _timeBuffer.Clear();
            _rawPoints.Clear();
            _velocity = new Vector(0, 0);
            _acceleration = new Vector(0, 0);
            _lastUpdateTime = DateTime.Now;
            _isInitialized = false;
        }

        /// <summary>
        /// 处理新的输入点，返回平滑后的点
        /// </summary>
        public Point ProcessPoint(Point inputPoint)
        {
            var currentTime = DateTime.Now;

            // 检查最小距离
            if (_isInitialized)
            {
                var distance = (inputPoint - _lastRawPoint).Length;
                if (distance < _config.MinDistance)
                {
                    return _lastSmoothedPoint; // 距离太小，返回上一个平滑点
                }
            }

            _lastRawPoint = inputPoint;
            _rawPoints.Add(inputPoint);

            // 根据模式选择处理方法
            Point smoothedPoint = _config.Mode switch
            {
                SmoothingMode.None => inputPoint,
                SmoothingMode.Basic => ApplyBasicSmoothing(inputPoint),
                SmoothingMode.AntiShake => ApplyAntiShakeSmoothing(inputPoint),
                SmoothingMode.Stabilizer => ApplyStabilizerSmoothing(inputPoint, currentTime),
                SmoothingMode.Professional => ApplyProfessionalSmoothing(inputPoint, currentTime),
                _ => inputPoint
            };

            // 更新状态
            UpdateVelocityAndAcceleration(smoothedPoint, currentTime);
            _lastSmoothedPoint = smoothedPoint;
            _lastUpdateTime = currentTime;
            _isInitialized = true;

            return smoothedPoint;
        }

        /// <summary>
        /// 基础平滑 - 指数移动平均
        /// </summary>
        private Point ApplyBasicSmoothing(Point inputPoint)
        {
            if (!_isInitialized)
                return inputPoint;

            var alpha = 1.0 - _config.Strength;
            alpha = Math.Max(0.1, Math.Min(0.9, alpha)); // 限制范围

            var smoothedX = alpha * inputPoint.X + (1 - alpha) * _lastSmoothedPoint.X;
            var smoothedY = alpha * inputPoint.Y + (1 - alpha) * _lastSmoothedPoint.Y;

            return new Point(smoothedX, smoothedY);
        }

        /// <summary>
        /// 抖动修正 - 检测并修正小幅抖动
        /// </summary>
        private Point ApplyAntiShakeSmoothing(Point inputPoint)
        {
            if (!_isInitialized)
                return inputPoint;

            // 计算与上一个点的距离
            var distance = (inputPoint - _lastSmoothedPoint).Length;

            // 如果距离小于抖动半径，应用强平滑
            if (distance <= _config.AntiShakeRadius)
            {
                var shakeFactor = distance / _config.AntiShakeRadius;
                var smoothingStrength = _config.Strength * (1 - shakeFactor);

                var alpha = 1.0 - smoothingStrength;
                var smoothedX = alpha * inputPoint.X + (1 - alpha) * _lastSmoothedPoint.X;
                var smoothedY = alpha * inputPoint.Y + (1 - alpha) * _lastSmoothedPoint.Y;

                return new Point(smoothedX, smoothedY);
            }

            // 距离较大时使用基础平滑
            return ApplyBasicSmoothing(inputPoint);
        }

        /// <summary>
        /// 稳定器 - 基于时间延迟的平滑
        /// </summary>
        private Point ApplyStabilizerSmoothing(Point inputPoint, DateTime currentTime)
        {
            // 添加到缓冲区
            _pointBuffer.Enqueue(inputPoint);
            _timeBuffer.Enqueue(currentTime);

            // 保持缓冲区大小
            while (_pointBuffer.Count > _config.BufferSize)
            {
                _pointBuffer.Dequeue();
                _timeBuffer.Dequeue();
            }

            if (_pointBuffer.Count < 2)
                return inputPoint;

            // 计算延迟时间
            var delayMs = _config.StabilizerDelay * 200; // 最大200ms延迟
            var targetTime = currentTime.AddMilliseconds(-delayMs);

            // 找到最接近目标时间的点
            var points = _pointBuffer.ToArray();
            var times = _timeBuffer.ToArray();

            Point targetPoint = points[0];
            for (int i = 0; i < times.Length; i++)
            {
                if (times[i] <= targetTime)
                {
                    targetPoint = points[i];
                }
                else
                {
                    break;
                }
            }

            // 应用基础平滑到目标点
            return ApplyBasicSmoothing(targetPoint);
        }

        /// <summary>
        /// 🔧 修复：专业模式 - 综合所有平滑技术，避免递归调用
        /// </summary>
        private Point ApplyProfessionalSmoothing(Point inputPoint, DateTime currentTime)
        {
            Point processedPoint = inputPoint;

            // 1. 首先应用抖动修正（直接实现，避免递归）
            if (!_isInitialized)
            {
                processedPoint = inputPoint;
            }
            else
            {
                var distance = (inputPoint - _lastSmoothedPoint).Length;
                if (distance <= _config.AntiShakeRadius)
                {
                    var shakeFactor = distance / _config.AntiShakeRadius;
                    var smoothingStrength = _config.Strength * (1 - shakeFactor);

                    var alpha = 1.0 - smoothingStrength;
                    alpha = Math.Max(0.1, Math.Min(0.9, alpha));

                    var smoothedX = alpha * inputPoint.X + (1 - alpha) * _lastSmoothedPoint.X;
                    var smoothedY = alpha * inputPoint.Y + (1 - alpha) * _lastSmoothedPoint.Y;

                    processedPoint = new Point(smoothedX, smoothedY);
                }
                else
                {
                    // 距离较大时使用基础平滑
                    var alpha = 1.0 - _config.Strength;
                    alpha = Math.Max(0.1, Math.Min(0.9, alpha));

                    var smoothedX = alpha * inputPoint.X + (1 - alpha) * _lastSmoothedPoint.X;
                    var smoothedY = alpha * inputPoint.Y + (1 - alpha) * _lastSmoothedPoint.Y;

                    processedPoint = new Point(smoothedX, smoothedY);
                }
            }

            // 2. 然后应用稳定器（如果启用）
            if (_config.StabilizerDelay > 0.01)
            {
                // 添加到缓冲区
                _pointBuffer.Enqueue(processedPoint);
                _timeBuffer.Enqueue(currentTime);

                // 保持缓冲区大小
                while (_pointBuffer.Count > _config.BufferSize)
                {
                    _pointBuffer.Dequeue();
                    _timeBuffer.Dequeue();
                }

                if (_pointBuffer.Count >= 2)
                {
                    // 计算延迟时间
                    var delayMs = _config.StabilizerDelay * 100; // 🔧 修复：减少最大延迟到100ms
                    var targetTime = currentTime.AddMilliseconds(-delayMs);

                    // 找到最接近目标时间的点
                    var points = _pointBuffer.ToArray();
                    var times = _timeBuffer.ToArray();

                    Point targetPoint = points[0];
                    for (int i = 0; i < times.Length; i++)
                    {
                        if (times[i] <= targetTime)
                        {
                            targetPoint = points[i];
                        }
                        else
                        {
                            break;
                        }
                    }
                    processedPoint = targetPoint;
                }
            }

            // 3. 最后应用预测平滑（如果启用）
            if (_config.EnablePrediction && _rawPoints.Count >= 3 && _velocity.Length >= 0.1)
            {
                // 基于当前速度和加速度预测下一个点
                var deltaTime = 0.016; // 假设60fps，约16ms
                var predictedVelocity = _velocity + _acceleration * deltaTime;
                var predictedPoint = _lastSmoothedPoint + predictedVelocity * deltaTime;

                // 将预测点与输入点混合
                var predictionWeight = Math.Min(0.2, _config.Strength * 0.3); // 🔧 修复：减少预测权重
                var smoothedX = (1 - predictionWeight) * processedPoint.X + predictionWeight * predictedPoint.X;
                var smoothedY = (1 - predictionWeight) * processedPoint.Y + predictionWeight * predictedPoint.Y;

                processedPoint = new Point(smoothedX, smoothedY);
            }

            return processedPoint;
        }

        /// <summary>
        /// 预测平滑 - 基于速度和加速度的预测
        /// </summary>
        private Point ApplyPredictiveSmoothing(Point inputPoint)
        {
            if (_velocity.Length < 0.1) // 速度太小时不预测
                return inputPoint;

            // 基于当前速度和加速度预测下一个点
            var deltaTime = 0.016; // 假设60fps，约16ms
            var predictedVelocity = _velocity + _acceleration * deltaTime;
            var predictedPoint = _lastSmoothedPoint + predictedVelocity * deltaTime;

            // 将预测点与输入点混合
            var predictionWeight = Math.Min(0.3, _config.Strength * 0.5);
            var smoothedX = (1 - predictionWeight) * inputPoint.X + predictionWeight * predictedPoint.X;
            var smoothedY = (1 - predictionWeight) * inputPoint.Y + predictionWeight * predictedPoint.Y;

            return new Point(smoothedX, smoothedY);
        }

        /// <summary>
        /// 更新速度和加速度
        /// </summary>
        private void UpdateVelocityAndAcceleration(Point currentPoint, DateTime currentTime)
        {
            if (!_isInitialized)
                return;

            var deltaTime = (currentTime - _lastUpdateTime).TotalSeconds;
            if (deltaTime <= 0)
                return;

            // 计算新速度
            var displacement = currentPoint - _lastSmoothedPoint;
            var newVelocity = new Vector(displacement.X / deltaTime, displacement.Y / deltaTime);

            // 计算加速度
            var velocityChange = newVelocity - _velocity;
            _acceleration = new Vector(velocityChange.X / deltaTime, velocityChange.Y / deltaTime);

            // 平滑速度变化
            var velocitySmoothing = 0.7;
            _velocity = velocitySmoothing * _velocity + (1 - velocitySmoothing) * newVelocity;
        }

        /// <summary>
        /// 获取当前速度
        /// </summary>
        public Vector GetCurrentVelocity() => _velocity;

        /// <summary>
        /// 获取当前加速度
        /// </summary>
        public Vector GetCurrentAcceleration() => _acceleration;

        /// <summary>
        /// 获取缓冲区中的点数量
        /// </summary>
        public int GetBufferSize() => _pointBuffer.Count;

        /// <summary>
        /// 平滑单个点（兼容性方法）
        /// </summary>
        public Point SmoothPoint(Point inputPoint)
        {
            return ProcessPoint(inputPoint);
        }

        /// <summary>
        /// 更新配置（兼容性方法）
        /// </summary>
        public void UpdateConfig(SmoothingConfig newConfig)
        {
            if (newConfig != null)
            {
                _config.Mode = newConfig.Mode;
                _config.Strength = newConfig.Strength;
                _config.AntiShakeRadius = newConfig.AntiShakeRadius;
                _config.StabilizerDelay = newConfig.StabilizerDelay;
                _config.FlowRate = newConfig.FlowRate;
                _config.BufferSize = newConfig.BufferSize;
                _config.MinDistance = newConfig.MinDistance;
                _config.EnablePrediction = newConfig.EnablePrediction;
            }
        }
    }
}
