﻿<Application x:Class="像素喵笔记.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:像素喵笔记"
>
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 引用Figma标准样式 -->
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局圆角样式 -->

            <!-- Button圆角样式 -->
            <Style TargetType="Button">
                <Setter Property="Background" Value="White"/>
                <Setter Property="BorderBrush" Value="#CCCCCC"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="8,4"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="6"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#0B8CE8"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter Property="Background" Value="#0A7BD4"/>
                                    <Setter Property="Foreground" Value="White"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- TextBox圆角样式 -->
            <Style TargetType="TextBox">
                <Setter Property="BorderBrush" Value="#CCCCCC"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="6,4"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="TextBox">
                            <Border Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="6">
                                <ScrollViewer x:Name="PART_ContentHost"
                                            Margin="{TemplateBinding Padding}"
                                            VerticalAlignment="Center"/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsFocused" Value="True">
                                    <Setter Property="BorderBrush" Value="#0B8CE8"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- ComboBox圆角样式 -->
            <Style TargetType="ComboBox">
                <Setter Property="BorderBrush" Value="#DADCE0"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Padding" Value="8,6"/>
                <Setter Property="Background" Value="#F8F9FA"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ComboBox">
                            <Grid>
                                <ToggleButton x:Name="ToggleButton"
                                            Grid.Column="2"
                                            Focusable="false"
                                            IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                            ClickMode="Press">
                                    <ToggleButton.Template>
                                        <ControlTemplate TargetType="ToggleButton">
                                            <Border Background="#F8F9FA"
                                                    BorderBrush="#DADCE0"
                                                    BorderThickness="1"
                                                    CornerRadius="6">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="20"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Path Grid.Column="1"
                                                          Data="M 0 0 L 4 4 L 8 0 Z"
                                                          Fill="#666666"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"/>
                                                </Grid>
                                            </Border>
                                        </ControlTemplate>
                                    </ToggleButton.Template>
                                    </ToggleButton>
                                    <ContentPresenter Name="ContentSite"
                                                    IsHitTestVisible="False"
                                                    Content="{TemplateBinding SelectionBoxItem}"
                                                    ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                    ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                    Margin="8,6"
                                                    VerticalAlignment="Center"
                                                    HorizontalAlignment="Left"/>
                                    <TextBox x:Name="PART_EditableTextBox"
                                           Style="{x:Null}"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           Margin="8,6"
                                           Focusable="True"
                                           Background="Transparent"
                                           BorderThickness="0"
                                           Visibility="Hidden"
                                           IsReadOnly="{TemplateBinding IsReadOnly}"/>
                                    <Popup Name="Popup"
                                           Placement="Bottom"
                                           IsOpen="{TemplateBinding IsDropDownOpen}"
                                           AllowsTransparency="True"
                                           Focusable="False"
                                           PopupAnimation="Slide">
                                        <Grid Name="DropDown"
                                              SnapsToDevicePixels="True"
                                              MinWidth="{TemplateBinding ActualWidth}"
                                              MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                            <Border x:Name="DropDownBorder"
                                                    Background="#F8F9FA"
                                                    BorderThickness="1"
                                                    BorderBrush="#DADCE0"
                                                    CornerRadius="6">
                                                <ScrollViewer Margin="6,8,6,8" SnapsToDevicePixels="True"
                                                            VerticalScrollBarVisibility="Auto"
                                                            HorizontalScrollBarVisibility="Auto">
                                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                                </ScrollViewer>
                                            </Border>
                                        </Grid>
                                    </Popup>
                                </Grid>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="HasItems" Value="false">
                                        <Setter TargetName="DropDownBorder" Property="MinHeight" Value="95"/>
                                    </Trigger>
                                    <Trigger Property="IsEnabled" Value="false">
                                        <Setter Property="Foreground" Value="#888888"/>
                                    </Trigger>
                                    <Trigger Property="IsGrouping" Value="true">
                                        <Setter Property="ScrollViewer.CanContentScroll" Value="false"/>
                                    </Trigger>
                                    <Trigger SourceName="Popup" Property="Popup.AllowsTransparency" Value="true">
                                        <Setter TargetName="DropDownBorder" Property="CornerRadius" Value="6"/>
                                        <Setter TargetName="DropDownBorder" Property="Margin" Value="0,2,0,0"/>
                                    </Trigger>
                                    <Trigger Property="IsEditable" Value="true">
                                        <Setter Property="IsTabStop" Value="false"/>
                                        <Setter TargetName="PART_EditableTextBox" Property="Visibility" Value="Visible"/>
                                        <Setter TargetName="ContentSite" Property="Visibility" Value="Hidden"/>
                                    </Trigger>
                                    <Trigger Property="IsFocused" Value="True">
                                        <Setter TargetName="ToggleButton" Property="BorderBrush" Value="#0B8CE8"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>

                <!-- ComboBoxItem圆角样式 -->
                <Style TargetType="ComboBoxItem">
                    <Setter Property="Padding" Value="10,6"/>
                    <Setter Property="Margin" Value="2,1"/>
                    <Setter Property="MinHeight" Value="28"/>
                    <Setter Property="HorizontalContentAlignment" Value="Left"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ComboBoxItem">
                                <Border x:Name="Border"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        CornerRadius="4"
                                        Padding="{TemplateBinding Padding}"
                                        MinHeight="{TemplateBinding MinHeight}">
                                    <ContentPresenter VerticalAlignment="Center"
                                                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsHighlighted" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#0B8CE8"/>
                                        <Setter Property="Foreground" Value="White"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#E3F2FD"/>
                                        <Setter Property="Foreground" Value="#0B8CE8"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>

                <!-- CheckBox圆角样式 -->
                <Style TargetType="CheckBox">
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="CheckBox">
                                <StackPanel Orientation="Horizontal">
                                    <Border Width="16" Height="16"
                                            Background="White"
                                            BorderBrush="#CCCCCC"
                                            BorderThickness="1"
                                            CornerRadius="3"
                                            Margin="0,0,4,0">
                                        <Path x:Name="CheckMark"
                                              Data="M 2 6 L 6 10 L 14 2"
                                              Stroke="#0B8CE8"
                                              StrokeThickness="2"
                                              Visibility="Collapsed"/>
                                    </Border>
                                    <ContentPresenter VerticalAlignment="Center"/>
                                </StackPanel>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsChecked" Value="True">
                                        <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>

                <!-- Rectangle圆角样式（通过Border实现） -->
                <Style x:Key="RoundedRectangle" TargetType="Border">
                    <Setter Property="CornerRadius" Value="6"/>
                    <Setter Property="BorderThickness" Value="1"/>
                    <Setter Property="BorderBrush" Value="#CCCCCC"/>
                </Style>

                <!-- Figma风格滚动条样式 -->
                <Style TargetType="ScrollBar">
                    <Setter Property="Background" Value="Transparent"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Width" Value="12"/>
                    <Setter Property="MinWidth" Value="12"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ScrollBar">
                                <Grid Background="{TemplateBinding Background}">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- 上箭头按钮 -->
                                    <RepeatButton Grid.Row="0" Height="16" Command="ScrollBar.LineUpCommand"
                                                Background="Transparent" BorderThickness="0">
                                        <RepeatButton.Template>
                                            <ControlTemplate TargetType="RepeatButton">
                                                <Border Background="{TemplateBinding Background}" CornerRadius="6,6,2,2">
                                                    <Path Data="M 4 8 L 8 4 L 12 8"
                                                          Stroke="#8A8A8A" StrokeThickness="2" StrokeLineJoin="Round" StrokeStartLineCap="Round" StrokeEndLineCap="Round"
                                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#F0F0F0"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </RepeatButton.Template>
                                    </RepeatButton>

                                    <!-- 滚动轨道 -->
                                    <Track Grid.Row="1" Name="PART_Track" IsDirectionReversed="True">
                                        <Track.Thumb>
                                            <Thumb>
                                                <Thumb.Template>
                                                    <ControlTemplate TargetType="Thumb">
                                                        <Border Background="#C0C0C0" CornerRadius="6" Margin="2,0"/>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#A0A0A0"/>
                                                            </Trigger>
                                                            <Trigger Property="IsDragging" Value="True">
                                                                <Setter Property="Background" Value="#808080"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Thumb.Template>
                                            </Thumb>
                                        </Track.Thumb>
                                    </Track>

                                    <!-- 下箭头按钮 -->
                                    <RepeatButton Grid.Row="2" Height="16" Command="ScrollBar.LineDownCommand"
                                                Background="Transparent" BorderThickness="0">
                                        <RepeatButton.Template>
                                            <ControlTemplate TargetType="RepeatButton">
                                                <Border Background="{TemplateBinding Background}" CornerRadius="2,2,6,6">
                                                    <Path Data="M 4 4 L 8 8 L 12 4"
                                                          Stroke="#8A8A8A" StrokeThickness="2" StrokeLineJoin="Round" StrokeStartLineCap="Round" StrokeEndLineCap="Round"
                                                          HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#F0F0F0"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </RepeatButton.Template>
                                    </RepeatButton>
                                </Grid>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>

                    <!-- 水平滚动条样式 -->
                    <Style.Triggers>
                        <Trigger Property="Orientation" Value="Horizontal">
                            <Setter Property="Height" Value="16"/>
                            <Setter Property="MinHeight" Value="16"/>
                            <Setter Property="Width" Value="Auto"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="ScrollBar">
                                        <Grid Background="{TemplateBinding Background}">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- 左箭头按钮 -->
                                            <RepeatButton Grid.Column="0" Width="16" Command="ScrollBar.LineLeftCommand"
                                                        Background="Transparent" BorderThickness="0">
                                                <RepeatButton.Template>
                                                    <ControlTemplate TargetType="RepeatButton">
                                                        <Border Background="{TemplateBinding Background}" CornerRadius="6,2,2,6">
                                                            <Path Data="M 8 4 L 4 8 L 8 12"
                                                                  Stroke="#8A8A8A" StrokeThickness="2" StrokeLineJoin="Round" StrokeStartLineCap="Round" StrokeEndLineCap="Round"
                                                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#F0F0F0"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </RepeatButton.Template>
                                            </RepeatButton>

                                            <!-- 滚动轨道 -->
                                            <Track Grid.Column="1" Name="PART_Track">
                                                <Track.Thumb>
                                                    <Thumb>
                                                        <Thumb.Template>
                                                            <ControlTemplate TargetType="Thumb">
                                                                <Border Background="#C0C0C0" CornerRadius="6" Margin="0,2"/>
                                                                <ControlTemplate.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="Background" Value="#A0A0A0"/>
                                                                    </Trigger>
                                                                    <Trigger Property="IsDragging" Value="True">
                                                                        <Setter Property="Background" Value="#808080"/>
                                                                    </Trigger>
                                                                </ControlTemplate.Triggers>
                                                            </ControlTemplate>
                                                        </Thumb.Template>
                                                    </Thumb>
                                                </Track.Thumb>
                                            </Track>

                                            <!-- 右箭头按钮 -->
                                            <RepeatButton Grid.Column="2" Width="16" Command="ScrollBar.LineRightCommand"
                                                        Background="Transparent" BorderThickness="0">
                                                <RepeatButton.Template>
                                                    <ControlTemplate TargetType="RepeatButton">
                                                        <Border Background="{TemplateBinding Background}" CornerRadius="2,6,6,2">
                                                            <Path Data="M 4 4 L 8 8 L 4 12"
                                                                  Stroke="#8A8A8A" StrokeThickness="2" StrokeLineJoin="Round" StrokeStartLineCap="Round" StrokeEndLineCap="Round"
                                                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#F0F0F0"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </RepeatButton.Template>
                                            </RepeatButton>
                                        </Grid>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </ResourceDictionary>
        </Application.Resources>
</Application>
