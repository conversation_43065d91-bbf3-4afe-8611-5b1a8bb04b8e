﻿using System.Windows;

namespace 像素喵笔记
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 设置全局异常处理器
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;



            // 正常启动主窗口
            try
            {
                var mainWindow = new MainWindow();
                mainWindow.Show();
                System.Diagnostics.Debug.WriteLine("主窗口已显示");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动主窗口失败：\n\n{ex.Message}\n\n堆栈跟踪：\n{ex.StackTrace}",
                              "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine($"UI线程未处理异常: {e.Exception.Message}");
            System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {e.Exception.StackTrace}");

            // 记录异常但不让应用程序崩溃
            e.Handled = true;

            // 显示详细的错误信息
            MessageBox.Show($"应用程序遇到错误：\n\n{e.Exception.Message}\n\n堆栈跟踪：\n{e.Exception.StackTrace}",
                          "错误详情", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用程序域未处理异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
}
