<Window x:Class="像素喵笔记.CardSettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="卡片设置" Height="600" Width="600"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ResizeMode="NoResize"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        RenderOptions.BitmapScalingMode="HighQuality">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <!-- 主容器，带圆角和阴影 -->
    <Border Background="#F8F9FA" CornerRadius="12" Margin="25">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.15" BlurRadius="16"/>
        </Border.Effect>

        <Grid Margin="24">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="卡片设置" FontSize="20" FontWeight="SemiBold" Foreground="#202124" VerticalAlignment="Center" FontFamily="Segoe UI"/>
                <Button x:Name="btnClose" Content="✕"
                       HorizontalAlignment="Right" Click="BtnClose_Click"
                       Style="{StaticResource FigmaDialogCloseButtonStyle}"/>
            </Grid>

            <!-- 设置内容 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                <StackPanel>
                    
                    <!-- 外观设置卡片 -->
                    <Border Style="{StaticResource StandardFigmaCardStyle}">
                        <StackPanel>
                            <TextBlock Text="外观设置" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,16"/>
                            
                            <!-- 背景颜色 -->
                            <StackPanel Margin="0,0,0,16">
                                <TextBlock Text="背景颜色" Style="{StaticResource FigmaLabelStyle}"/>
                                <Button x:Name="btnBackgroundColor" Height="36" Click="BtnBackgroundColor_Click"
                                       Style="{StaticResource FigmaColorPickerButtonStyle}">
                                    <StackPanel Orientation="Horizontal">
                                        <Rectangle x:Name="backgroundColorPreview" Width="20" Height="20"
                                                  Fill="White" Stroke="#DADCE0" StrokeThickness="1"
                                                  Margin="0,0,8,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="选择背景颜色" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>

                            <!-- 边框颜色 -->
                            <StackPanel Margin="0,0,0,16">
                                <TextBlock Text="边框颜色" Style="{StaticResource FigmaLabelStyle}"/>
                                <Button x:Name="btnBorderColor" Height="36" Click="BtnBorderColor_Click"
                                       Style="{StaticResource FigmaColorPickerButtonStyle}">
                                    <StackPanel Orientation="Horizontal">
                                        <Rectangle x:Name="borderColorPreview" Width="20" Height="20"
                                                  Fill="#E8EAED" Stroke="#DADCE0" StrokeThickness="1"
                                                  Margin="0,0,8,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="选择边框颜色" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>

                            <!-- 边框宽度 -->
                            <StackPanel>
                                <TextBlock Text="边框宽度" Style="{StaticResource FigmaLabelStyle}"/>
                                <ComboBox x:Name="cmbBorderThickness" Style="{StaticResource FigmaComboBoxStyle}">
                                    <ComboBoxItem Content="无边框" Tag="0"/>
                                    <ComboBoxItem Content="细边框" Tag="1" IsSelected="True"/>
                                    <ComboBoxItem Content="中等边框" Tag="2"/>
                                    <ComboBoxItem Content="粗边框" Tag="3"/>
                                </ComboBox>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- 尺寸设置卡片 -->
                    <Border Style="{StaticResource StandardFigmaCardStyle}">
                        <StackPanel>
                            <TextBlock Text="尺寸设置" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,16"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="12"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 宽度 -->
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="宽度" Style="{StaticResource FigmaLabelStyle}"/>
                                    <TextBox x:Name="txtWidth" Style="{StaticResource FigmaTextBoxStyle}" Text="200"/>
                                </StackPanel>

                                <!-- 高度 -->
                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="高度" Style="{StaticResource FigmaLabelStyle}"/>
                                    <TextBox x:Name="txtHeight" Style="{StaticResource FigmaTextBoxStyle}" Text="150"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 字体设置卡片 -->
                    <Border Style="{StaticResource StandardFigmaCardStyle}">
                        <StackPanel>
                            <TextBlock Text="字体设置" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,16"/>
                            
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="12"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>

                                <!-- 字体 -->
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="字体" Style="{StaticResource FigmaLabelStyle}"/>
                                    <ComboBox x:Name="cmbFontFamily" Style="{StaticResource FigmaComboBoxStyle}"/>
                                </StackPanel>

                                <!-- 字号 -->
                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="字号" Style="{StaticResource FigmaLabelStyle}"/>
                                    <ComboBox x:Name="cmbFontSize" Style="{StaticResource FigmaComboBoxStyle}" IsEditable="True"/>
                                </StackPanel>
                            </Grid>

                            <!-- 文字颜色 -->
                            <StackPanel Margin="0,16,0,0">
                                <TextBlock Text="文字颜色" Style="{StaticResource FigmaLabelStyle}"/>
                                <Button x:Name="btnTextColor" Height="36" Click="BtnTextColor_Click"
                                       Style="{StaticResource FigmaColorPickerButtonStyle}">
                                    <StackPanel Orientation="Horizontal">
                                        <Rectangle x:Name="textColorPreview" Width="20" Height="20"
                                                  Fill="#202124" Stroke="#DADCE0" StrokeThickness="1"
                                                  Margin="0,0,8,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="选择文字颜色" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
                <Button x:Name="btnCancel" Content="取消" Style="{StaticResource FigmaDialogCancelButtonStyle}" Click="BtnCancel_Click"/>
                <Button x:Name="btnApply" Content="应用" Style="{StaticResource FigmaDialogConfirmButtonStyle}" Click="BtnApply_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
