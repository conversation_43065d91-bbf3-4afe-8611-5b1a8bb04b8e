using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace 像素喵笔记
{
    /// <summary>
    /// CardSettingsDialog.xaml 的交互逻辑
    /// </summary>
    public partial class CardSettingsDialog : Window
    {
        private readonly FloatingCard _targetCard;
        private Color _selectedBackgroundColor;
        private Color _selectedBorderColor;
        private Color _selectedTextColor;

        public CardSettingsDialog(FloatingCard targetCard)
        {
            InitializeComponent();
            _targetCard = targetCard ?? throw new ArgumentNullException(nameof(targetCard));
            InitializeSettings();
        }

        /// <summary>
        /// 初始化设置
        /// </summary>
        private void InitializeSettings()
        {
            try
            {
                // 初始化字体列表
                InitializeFontFamilies();
                
                // 初始化字号列表
                InitializeFontSizes();
                
                // 加载当前卡片设置
                LoadCurrentSettings();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化字体列表
        /// </summary>
        private void InitializeFontFamilies()
        {
            var commonFonts = new[]
            {
                "Segoe UI", "微软雅黑", "宋体", "黑体", "楷体", "仿宋",
                "Arial", "Times New Roman", "Calibri", "Verdana"
            };

            foreach (var font in commonFonts)
            {
                cmbFontFamily.Items.Add(new ComboBoxItem { Content = font, Tag = font });
            }

            cmbFontFamily.SelectedIndex = 0; // 默认选择Segoe UI
        }

        /// <summary>
        /// 初始化字号列表
        /// </summary>
        private void InitializeFontSizes()
        {
            var fontSizes = new[] { 8, 9, 10, 11, 12, 13, 14, 16, 18, 20, 22, 24, 26, 28, 32, 36, 48, 72 };
            
            foreach (var size in fontSizes)
            {
                cmbFontSize.Items.Add(new ComboBoxItem { Content = size.ToString(), Tag = size });
            }

            cmbFontSize.SelectedIndex = 5; // 默认选择13
        }

        /// <summary>
        /// 加载当前卡片设置
        /// </summary>
        private void LoadCurrentSettings()
        {
            // 加载背景颜色
            if (_targetCard.Background is SolidColorBrush backgroundBrush)
            {
                _selectedBackgroundColor = backgroundBrush.Color;
                backgroundColorPreview.Fill = backgroundBrush;
            }
            else
            {
                _selectedBackgroundColor = Colors.White;
                backgroundColorPreview.Fill = new SolidColorBrush(Colors.White);
            }

            // 加载边框颜色
            if (_targetCard.BorderBrush is SolidColorBrush borderBrush)
            {
                _selectedBorderColor = borderBrush.Color;
                borderColorPreview.Fill = borderBrush;
            }
            else
            {
                _selectedBorderColor = Color.FromRgb(232, 234, 237);
                borderColorPreview.Fill = new SolidColorBrush(_selectedBorderColor);
            }

            // 加载边框宽度
            var borderThickness = (int)_targetCard.BorderThickness.Left;
            for (int i = 0; i < cmbBorderThickness.Items.Count; i++)
            {
                if (cmbBorderThickness.Items[i] is ComboBoxItem item && 
                    item.Tag?.ToString() == borderThickness.ToString())
                {
                    cmbBorderThickness.SelectedIndex = i;
                    break;
                }
            }

            // 加载尺寸
            txtWidth.Text = ((int)_targetCard.Width).ToString();
            txtHeight.Text = ((int)_targetCard.Height).ToString();

            // 加载字体设置（从内容编辑器获取）
            LoadFontSettings();
        }

        /// <summary>
        /// 加载字体设置
        /// </summary>
        private void LoadFontSettings()
        {
            try
            {
                // 获取内容编辑器的字体设置
                var contentEditor = FindContentEditor();
                if (contentEditor != null)
                {
                    // 字体
                    var fontFamily = contentEditor.FontFamily?.Source ?? "Segoe UI";
                    for (int i = 0; i < cmbFontFamily.Items.Count; i++)
                    {
                        if (cmbFontFamily.Items[i] is ComboBoxItem item && 
                            item.Tag?.ToString() == fontFamily)
                        {
                            cmbFontFamily.SelectedIndex = i;
                            break;
                        }
                    }

                    // 字号
                    var fontSize = (int)contentEditor.FontSize;
                    for (int i = 0; i < cmbFontSize.Items.Count; i++)
                    {
                        if (cmbFontSize.Items[i] is ComboBoxItem item && 
                            item.Tag?.ToString() == fontSize.ToString())
                        {
                            cmbFontSize.SelectedIndex = i;
                            break;
                        }
                    }

                    // 文字颜色
                    if (contentEditor.Foreground is SolidColorBrush textBrush)
                    {
                        _selectedTextColor = textBrush.Color;
                        textColorPreview.Fill = textBrush;
                    }
                    else
                    {
                        _selectedTextColor = Color.FromRgb(32, 33, 36);
                        textColorPreview.Fill = new SolidColorBrush(_selectedTextColor);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载字体设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 查找内容编辑器
        /// </summary>
        private RichTextBox? FindContentEditor()
        {
            return FindVisualChild<RichTextBox>(_targetCard);
        }

        /// <summary>
        /// 查找可视化子元素
        /// </summary>
        private static T? FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        /// <summary>
        /// 背景颜色按钮点击事件
        /// </summary>
        private void BtnBackgroundColor_Click(object sender, RoutedEventArgs e)
        {
            ShowColorPicker((color) =>
            {
                _selectedBackgroundColor = color;
                backgroundColorPreview.Fill = new SolidColorBrush(color);
            });
        }

        /// <summary>
        /// 边框颜色按钮点击事件
        /// </summary>
        private void BtnBorderColor_Click(object sender, RoutedEventArgs e)
        {
            ShowColorPicker((color) =>
            {
                _selectedBorderColor = color;
                borderColorPreview.Fill = new SolidColorBrush(color);
            });
        }

        /// <summary>
        /// 文字颜色按钮点击事件
        /// </summary>
        private void BtnTextColor_Click(object sender, RoutedEventArgs e)
        {
            ShowColorPicker((color) =>
            {
                _selectedTextColor = color;
                textColorPreview.Fill = new SolidColorBrush(color);
            });
        }

        /// <summary>
        /// 显示颜色选择器
        /// </summary>
        private void ShowColorPicker(Action<Color> onColorSelected)
        {
            try
            {
                var colorPicker = new ColorPickerDialog();
                colorPicker.Owner = this;

                if (colorPicker.ShowDialog() == true)
                {
                    onColorSelected?.Invoke(colorPicker.SelectedColor);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"颜色选择器错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void BtnApply_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ApplySettings();
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 应用设置到卡片
        /// </summary>
        private void ApplySettings()
        {
            // 应用背景颜色
            _targetCard.Background = new SolidColorBrush(_selectedBackgroundColor);

            // 应用边框颜色
            _targetCard.BorderBrush = new SolidColorBrush(_selectedBorderColor);

            // 应用边框宽度
            if (cmbBorderThickness.SelectedItem is ComboBoxItem borderItem &&
                int.TryParse(borderItem.Tag?.ToString(), out int borderThickness))
            {
                _targetCard.BorderThickness = new Thickness(borderThickness);
            }

            // 应用尺寸
            if (double.TryParse(txtWidth.Text, out double width) && width >= 100)
            {
                _targetCard.Width = width;
            }

            if (double.TryParse(txtHeight.Text, out double height) && height >= 80)
            {
                _targetCard.Height = height;
            }

            // 应用字体设置
            ApplyFontSettings();
        }

        /// <summary>
        /// 应用字体设置
        /// </summary>
        private void ApplyFontSettings()
        {
            var contentEditor = FindContentEditor();
            if (contentEditor == null) return;

            // 应用字体
            if (cmbFontFamily.SelectedItem is ComboBoxItem fontItem)
            {
                contentEditor.FontFamily = new FontFamily(fontItem.Tag?.ToString() ?? "Segoe UI");
            }

            // 应用字号
            if (cmbFontSize.SelectedItem is ComboBoxItem sizeItem &&
                double.TryParse(sizeItem.Tag?.ToString(), out double fontSize))
            {
                contentEditor.FontSize = fontSize;
            }

            // 应用文字颜色
            contentEditor.Foreground = new SolidColorBrush(_selectedTextColor);
        }
    }
}
