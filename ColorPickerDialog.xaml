<Window x:Class="像素喵笔记.ColorPickerDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:像素喵笔记"
        mc:Ignorable="d"
        Title="选择颜色" Height="655" Width="550"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        RenderOptions.BitmapScalingMode="HighQuality">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <!-- 主容器，带圆角和阴影 -->
    <Border Background="#F8F9FA" CornerRadius="12" Margin="15">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.15" BlurRadius="16"/>
        </Border.Effect>

        <Grid Margin="24">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="选择颜色" FontSize="20" FontWeight="SemiBold" Foreground="#202124" VerticalAlignment="Center" FontFamily="Segoe UI"/>
                <Button x:Name="btnClose" Content="✕" Style="{StaticResource FigmaCloseButtonStyle}" 
                        HorizontalAlignment="Right" Click="btnCancel_Click"/>
            </Grid>

            <!-- 预定义颜色 -->
            <Border Grid.Row="1" Style="{StaticResource StandardFigmaCardStyle}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="预定义颜色" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,12"/>
                    <UniformGrid Columns="8" Rows="6" Margin="0,5,0,0">
                        <!-- 第一行 - 基本颜色 -->
                        <Button Background="Black" Click="ColorButton_Click" Tag="#FF000000" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="White" Click="ColorButton_Click" Tag="#FFFFFFFF" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Red" Click="ColorButton_Click" Tag="#FFFF0000" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Green" Click="ColorButton_Click" Tag="#FF008000" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Blue" Click="ColorButton_Click" Tag="#FF0000FF" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Yellow" Click="ColorButton_Click" Tag="#FFFFFF00" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Cyan" Click="ColorButton_Click" Tag="#FF00FFFF" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Magenta" Click="ColorButton_Click" Tag="#FFFF00FF" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>

                        <!-- 第二行 - 深色系 -->
                        <Button Background="DarkRed" Click="ColorButton_Click" Tag="#FF8B0000" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="DarkGreen" Click="ColorButton_Click" Tag="#FF006400" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="DarkBlue" Click="ColorButton_Click" Tag="#FF00008B" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Orange" Click="ColorButton_Click" Tag="#FFFFA500" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Purple" Click="ColorButton_Click" Tag="#FF800080" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Brown" Click="ColorButton_Click" Tag="#FFA52A2A" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Pink" Click="ColorButton_Click" Tag="#FFFFC0CB" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Gray" Click="ColorButton_Click" Tag="#FF808080" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>

                        <!-- 第三行 - 浅色系 -->
                        <Button Background="#FFFF6B6B" Click="ColorButton_Click" Tag="#FFFF6B6B" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="LightGreen" Click="ColorButton_Click" Tag="#FF90EE90" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="LightBlue" Click="ColorButton_Click" Tag="#FFADD8E6" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="LightYellow" Click="ColorButton_Click" Tag="#FFFFFFE0" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="LightCyan" Click="ColorButton_Click" Tag="#FFE0FFFF" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="LightPink" Click="ColorButton_Click" Tag="#FFFFB6C1" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="LightGray" Click="ColorButton_Click" Tag="#FFD3D3D3" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Silver" Click="ColorButton_Click" Tag="#FFC0C0C0" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>

                        <!-- 第四行 - 其他颜色 -->
                        <Button Background="Navy" Click="ColorButton_Click" Tag="#FF000080" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Teal" Click="ColorButton_Click" Tag="#FF008080" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Olive" Click="ColorButton_Click" Tag="#FF808000" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Maroon" Click="ColorButton_Click" Tag="#FF800000" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Lime" Click="ColorButton_Click" Tag="#FF00FF00" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Aqua" Click="ColorButton_Click" Tag="#FF00FFFF" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Fuchsia" Click="ColorButton_Click" Tag="#FFFF00FF" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Gold" Click="ColorButton_Click" Tag="#FFFFD700" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>

                        <!-- 第五行 - 更多颜色 -->
                        <Button Background="Coral" Click="ColorButton_Click" Tag="#FFFF7F50" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Salmon" Click="ColorButton_Click" Tag="#FFFA8072" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Khaki" Click="ColorButton_Click" Tag="#FFF0E68C" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Plum" Click="ColorButton_Click" Tag="#FFDDA0DD" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Turquoise" Click="ColorButton_Click" Tag="#FF40E0D0" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Violet" Click="ColorButton_Click" Tag="#FFEE82EE" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Wheat" Click="ColorButton_Click" Tag="#FFF5DEB3" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="Tan" Click="ColorButton_Click" Tag="#FFD2B48C" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>

                        <!-- 第六行 - 灰度 -->
                        <Button Background="DarkGray" Click="ColorButton_Click" Tag="#FFA9A9A9" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="#FF696969" Click="ColorButton_Click" Tag="#FF696969" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="#FF555555" Click="ColorButton_Click" Tag="#FF555555" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="#FF444444" Click="ColorButton_Click" Tag="#FF444444" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="#FF333333" Click="ColorButton_Click" Tag="#FF333333" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="#FF222222" Click="ColorButton_Click" Tag="#FF222222" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="#FF111111" Click="ColorButton_Click" Tag="#FF111111" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                        <Button Background="#FF000000" Click="ColorButton_Click" Tag="#FF000000" Margin="2" Style="{StaticResource ColorPickerButtonStyle}"/>
                    </UniformGrid>
                </StackPanel>
            </Border>

            <!-- HSV色表 -->
            <Border Grid.Row="2" Style="{StaticResource StandardFigmaCardStyle}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="HSV色表" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,12"/>
                    <Grid Height="150">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                        </Grid.ColumnDefinitions>

                        <!-- 主色板 (饱和度 x 明度) -->
                        <Canvas x:Name="mainColorCanvas" Grid.Column="0" Background="White"
                                MouseLeftButtonDown="MainColorCanvas_MouseLeftButtonDown"
                                MouseMove="MainColorCanvas_MouseMove"
                                MouseLeftButtonUp="MainColorCanvas_MouseLeftButtonUp">
                            <!-- 主色板将在代码中动态生成 -->
                            <Ellipse x:Name="colorSelector" Width="8" Height="8"
                                     Stroke="White" StrokeThickness="2"
                                     Fill="Transparent" Visibility="Visible"/>
                        </Canvas>

                        <!-- 色相条 -->
                        <Canvas x:Name="hueCanvas" Grid.Column="1" Background="White" Margin="5,0,0,0"
                                MouseLeftButtonDown="HueCanvas_MouseLeftButtonDown"
                                MouseMove="HueCanvas_MouseMove"
                                MouseLeftButtonUp="HueCanvas_MouseLeftButtonUp">
                            <!-- 色相条将在代码中动态生成 -->
                            <Rectangle x:Name="hueSelector" Width="20" Height="4"
                                       Stroke="White" StrokeThickness="1"
                                       Fill="Transparent" Visibility="Visible"/>
                        </Canvas>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 自定义RGB输入 -->
            <Border Grid.Row="3" Style="{StaticResource StandardFigmaCardStyle}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="自定义RGB" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,12"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="70"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Text="R:" Grid.Column="0" Style="{StaticResource FigmaLabelStyle}" VerticalAlignment="Center"/>
                        <TextBox x:Name="txtR" Grid.Column="1" Style="{StaticResource FigmaTextBoxStyle}" Text="0" PreviewTextInput="NumberValidation" TextChanged="RgbTextChanged" Margin="0,0,5,0"/>
                        <TextBlock Text="G:" Grid.Column="2" Style="{StaticResource FigmaLabelStyle}" VerticalAlignment="Center"/>
                        <TextBox x:Name="txtG" Grid.Column="3" Style="{StaticResource FigmaTextBoxStyle}" Text="0" PreviewTextInput="NumberValidation" TextChanged="RgbTextChanged" Margin="0,0,5,0"/>
                        <TextBlock Text="B:" Grid.Column="4" Style="{StaticResource FigmaLabelStyle}" VerticalAlignment="Center"/>
                        <TextBox x:Name="txtB" Grid.Column="5" Style="{StaticResource FigmaTextBoxStyle}" Text="0" PreviewTextInput="NumberValidation" TextChanged="RgbTextChanged" Margin="0,0,5,0"/>
                        <TextBlock Text="十六进制:" Grid.Column="6" Style="{StaticResource FigmaLabelStyle}" VerticalAlignment="Center" Margin="5,0,5,0"/>
                        <TextBox x:Name="txtHex" Grid.Column="7" Style="{StaticResource FigmaTextBoxStyle}" Text="#000000" TextChanged="HexTextChanged"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 当前选择的颜色预览 -->
            <Border Grid.Row="4" Style="{StaticResource StandardFigmaCardStyle}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="当前颜色" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,12"/>
                    <Rectangle x:Name="colorPreview" Height="36" Margin="0"
                               Stroke="#DADCE0" StrokeThickness="1" Fill="Black"/>
                </StackPanel>
            </Border>

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                <Button x:Name="btnCancel" Content="取消" Style="{StaticResource FigmaDialogCancelButtonStyle}" Click="btnCancel_Click"/>
                <Button x:Name="btnOK" Content="确定" Style="{StaticResource FigmaDialogConfirmButtonStyle}" Click="btnOK_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
