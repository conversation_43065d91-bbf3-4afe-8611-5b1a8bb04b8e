using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;

namespace 像素喵笔记
{
    /// <summary>
    /// ColorPickerDialog.xaml 的交互逻辑
    /// </summary>
    public partial class ColorPickerDialog : Window
    {
        public Color SelectedColor { get; set; } = Colors.Black;
        private bool isUpdatingFromCode = false;
        private bool isDragging = false;
        private double currentHue = 0; // 当前色相 (0-360)
        private double currentSaturation = 1; // 当前饱和度 (0-1)
        private double currentValue = 1; // 当前明度 (0-1)

        public ColorPickerDialog()
        {
            try
            {
                InitializeComponent();
                this.Loaded += ColorPickerDialog_Loaded;
                UpdatePreview();
                UpdateRgbInputs();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"颜色选择器初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 颜色按钮点击事件
        /// </summary>
        private void ColorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is string colorString)
                {
                    var color = (Color)ColorConverter.ConvertFromString(colorString);
                    SelectedColor = color;

                    // 更新HSV值
                    RgbToHsv(color, out currentHue, out currentSaturation, out currentValue);

                    // 重新生成色表和更新界面
                    GenerateMainColorCanvas();
                    UpdatePreview();
                    UpdateRgbInputs();
                    UpdateColorSelectors();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"颜色选择错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                System.Diagnostics.Debug.WriteLine($"颜色转换错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新颜色预览
        /// </summary>
        private void UpdatePreview()
        {
            try
            {
                if (colorPreview != null)
                {
                    colorPreview.Fill = new SolidColorBrush(SelectedColor);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新颜色预览错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void btnOK_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 窗口加载完成事件
        /// </summary>
        private void ColorPickerDialog_Loaded(object sender, RoutedEventArgs e)
        {
            // 延迟生成色表，确保控件已经完全渲染
            Dispatcher.BeginInvoke(new Action(() =>
            {
                GenerateHsvColorTable();
                UpdateColorSelectors();
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }

        /// <summary>
        /// 生成HSV色表
        /// </summary>
        private void GenerateHsvColorTable()
        {
            try
            {
                GenerateMainColorCanvas();
                GenerateHueCanvas();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成HSV色表错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成主色板 (饱和度 x 明度)
        /// </summary>
        private void GenerateMainColorCanvas()
        {
            mainColorCanvas.Children.Clear();

            int width = (int)mainColorCanvas.ActualWidth;
            int height = (int)mainColorCanvas.ActualHeight;

            if (width <= 0 || height <= 0) return;

            // 创建渐变背景
            var gradientBrush = new LinearGradientBrush();
            gradientBrush.StartPoint = new Point(0, 0);
            gradientBrush.EndPoint = new Point(1, 0);

            // 从白色到当前色相的纯色
            Color baseColor = HsvToRgb(currentHue, 1, 1);
            gradientBrush.GradientStops.Add(new GradientStop(Colors.White, 0));
            gradientBrush.GradientStops.Add(new GradientStop(baseColor, 1));

            Rectangle horizontalGradient = new Rectangle
            {
                Width = width,
                Height = height,
                Fill = gradientBrush
            };
            mainColorCanvas.Children.Add(horizontalGradient);

            // 添加从透明到黑色的垂直渐变
            var verticalGradient = new LinearGradientBrush();
            verticalGradient.StartPoint = new Point(0, 0);
            verticalGradient.EndPoint = new Point(0, 1);
            verticalGradient.GradientStops.Add(new GradientStop(Colors.Transparent, 0));
            verticalGradient.GradientStops.Add(new GradientStop(Colors.Black, 1));

            Rectangle blackGradient = new Rectangle
            {
                Width = width,
                Height = height,
                Fill = verticalGradient
            };
            mainColorCanvas.Children.Add(blackGradient);

            // 重新添加选择器
            mainColorCanvas.Children.Add(colorSelector);
        }

        /// <summary>
        /// 生成色相条
        /// </summary>
        private void GenerateHueCanvas()
        {
            hueCanvas.Children.Clear();

            int width = (int)hueCanvas.ActualWidth;
            int height = (int)hueCanvas.ActualHeight;

            if (width <= 0 || height <= 0) return;

            // 创建色相渐变
            var hueGradient = new LinearGradientBrush();
            hueGradient.StartPoint = new Point(0, 0);
            hueGradient.EndPoint = new Point(0, 1);

            // 添加色相渐变停止点
            for (int i = 0; i <= 6; i++)
            {
                double hue = i * 60;
                Color color = HsvToRgb(hue, 1, 1);
                hueGradient.GradientStops.Add(new GradientStop(color, (double)i / 6));
            }

            Rectangle hueRect = new Rectangle
            {
                Width = width,
                Height = height,
                Fill = hueGradient
            };
            hueCanvas.Children.Add(hueRect);

            // 重新添加选择器
            hueCanvas.Children.Add(hueSelector);
        }

        /// <summary>
        /// HSV转RGB
        /// </summary>
        private Color HsvToRgb(double h, double s, double v)
        {
            int hi = (int)(h / 60) % 6;
            double f = h / 60 - hi;
            double p = v * (1 - s);
            double q = v * (1 - f * s);
            double t = v * (1 - (1 - f) * s);

            double r, g, b;
            switch (hi)
            {
                case 0: r = v; g = t; b = p; break;
                case 1: r = q; g = v; b = p; break;
                case 2: r = p; g = v; b = t; break;
                case 3: r = p; g = q; b = v; break;
                case 4: r = t; g = p; b = v; break;
                default: r = v; g = p; b = q; break;
            }

            return Color.FromRgb(
                (byte)(r * 255),
                (byte)(g * 255),
                (byte)(b * 255)
            );
        }

        /// <summary>
        /// 主色板鼠标按下事件
        /// </summary>
        private void MainColorCanvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                isDragging = true;
                mainColorCanvas.CaptureMouse();
                UpdateMainColorSelection(e.GetPosition(mainColorCanvas));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"主色板点击错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 主色板鼠标移动事件
        /// </summary>
        private void MainColorCanvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging && e.LeftButton == MouseButtonState.Pressed)
            {
                UpdateMainColorSelection(e.GetPosition(mainColorCanvas));
            }
        }

        /// <summary>
        /// 主色板鼠标释放事件
        /// </summary>
        private void MainColorCanvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            isDragging = false;
            mainColorCanvas.ReleaseMouseCapture();
        }

        /// <summary>
        /// 色相条鼠标按下事件
        /// </summary>
        private void HueCanvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                isDragging = true;
                hueCanvas.CaptureMouse();
                UpdateHueSelection(e.GetPosition(hueCanvas));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"色相条点击错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 色相条鼠标移动事件
        /// </summary>
        private void HueCanvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging && e.LeftButton == MouseButtonState.Pressed)
            {
                UpdateHueSelection(e.GetPosition(hueCanvas));
            }
        }

        /// <summary>
        /// 色相条鼠标释放事件
        /// </summary>
        private void HueCanvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            isDragging = false;
            hueCanvas.ReleaseMouseCapture();
        }

        /// <summary>
        /// 数字验证
        /// </summary>
        private void NumberValidation(object sender, TextCompositionEventArgs e)
        {
            Regex regex = new Regex("[^0-9]+");
            e.Handled = regex.IsMatch(e.Text);
        }

        /// <summary>
        /// RGB文本框变化事件
        /// </summary>
        private void RgbTextChanged(object sender, TextChangedEventArgs e)
        {
            if (isUpdatingFromCode) return;

            try
            {
                if (int.TryParse(txtR.Text, out int r) &&
                    int.TryParse(txtG.Text, out int g) &&
                    int.TryParse(txtB.Text, out int b))
                {
                    r = Math.Max(0, Math.Min(255, r));
                    g = Math.Max(0, Math.Min(255, g));
                    b = Math.Max(0, Math.Min(255, b));

                    SelectedColor = Color.FromRgb((byte)r, (byte)g, (byte)b);

                    // 更新HSV值
                    RgbToHsv(SelectedColor, out currentHue, out currentSaturation, out currentValue);

                    // 重新生成色表和更新界面
                    GenerateMainColorCanvas();
                    UpdatePreview();
                    UpdateColorSelectors();

                    isUpdatingFromCode = true;
                    txtHex.Text = $"#{r:X2}{g:X2}{b:X2}";
                    isUpdatingFromCode = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"RGB文本变化错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 十六进制文本框变化事件
        /// </summary>
        private void HexTextChanged(object sender, TextChangedEventArgs e)
        {
            if (isUpdatingFromCode) return;

            try
            {
                string hex = txtHex.Text;
                if (hex.StartsWith("#") && hex.Length == 7)
                {
                    var color = (Color)ColorConverter.ConvertFromString(hex);
                    SelectedColor = color;

                    // 更新HSV值
                    RgbToHsv(color, out currentHue, out currentSaturation, out currentValue);

                    // 重新生成色表和更新界面
                    GenerateMainColorCanvas();
                    UpdatePreview();
                    UpdateColorSelectors();

                    isUpdatingFromCode = true;
                    txtR.Text = color.R.ToString();
                    txtG.Text = color.G.ToString();
                    txtB.Text = color.B.ToString();
                    isUpdatingFromCode = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"十六进制文本变化错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新RGB输入框
        /// </summary>
        private void UpdateRgbInputs()
        {
            try
            {
                isUpdatingFromCode = true;
                txtR.Text = SelectedColor.R.ToString();
                txtG.Text = SelectedColor.G.ToString();
                txtB.Text = SelectedColor.B.ToString();
                txtHex.Text = $"#{SelectedColor.R:X2}{SelectedColor.G:X2}{SelectedColor.B:X2}";
                isUpdatingFromCode = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新RGB输入框错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新主色板选择
        /// </summary>
        private void UpdateMainColorSelection(Point position)
        {
            try
            {
                double width = mainColorCanvas.ActualWidth;
                double height = mainColorCanvas.ActualHeight;

                if (width <= 0 || height <= 0) return;

                // 限制位置在画布范围内
                position.X = Math.Max(0, Math.Min(width, position.X));
                position.Y = Math.Max(0, Math.Min(height, position.Y));

                // 计算饱和度和明度
                currentSaturation = position.X / width;
                currentValue = 1.0 - (position.Y / height);

                // 更新颜色
                SelectedColor = HsvToRgb(currentHue, currentSaturation, currentValue);
                UpdatePreview();
                UpdateRgbInputs();
                UpdateColorSelectors();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新主色板选择错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新色相选择
        /// </summary>
        private void UpdateHueSelection(Point position)
        {
            try
            {
                double height = hueCanvas.ActualHeight;

                if (height <= 0) return;

                // 限制位置在画布范围内
                position.Y = Math.Max(0, Math.Min(height, position.Y));

                // 计算色相
                currentHue = (position.Y / height) * 360;

                // 重新生成主色板（因为色相改变了）
                GenerateMainColorCanvas();

                // 更新颜色
                SelectedColor = HsvToRgb(currentHue, currentSaturation, currentValue);
                UpdatePreview();
                UpdateRgbInputs();
                UpdateColorSelectors();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新色相选择错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新颜色选择器位置
        /// </summary>
        private void UpdateColorSelectors()
        {
            try
            {
                // 更新主色板选择器位置
                double mainWidth = mainColorCanvas.ActualWidth;
                double mainHeight = mainColorCanvas.ActualHeight;

                if (mainWidth > 0 && mainHeight > 0)
                {
                    double x = currentSaturation * mainWidth - colorSelector.Width / 2;
                    double y = (1.0 - currentValue) * mainHeight - colorSelector.Height / 2;

                    Canvas.SetLeft(colorSelector, Math.Max(0, Math.Min(mainWidth - colorSelector.Width, x)));
                    Canvas.SetTop(colorSelector, Math.Max(0, Math.Min(mainHeight - colorSelector.Height, y)));
                }

                // 更新色相条选择器位置
                double hueHeight = hueCanvas.ActualHeight;

                if (hueHeight > 0)
                {
                    double y = (currentHue / 360) * hueHeight - hueSelector.Height / 2;
                    Canvas.SetTop(hueSelector, Math.Max(0, Math.Min(hueHeight - hueSelector.Height, y)));
                    Canvas.SetLeft(hueSelector, 0);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新颜色选择器位置错误: {ex.Message}");
            }
        }

        /// <summary>
        /// RGB转HSV
        /// </summary>
        private void RgbToHsv(Color color, out double h, out double s, out double v)
        {
            double r = color.R / 255.0;
            double g = color.G / 255.0;
            double b = color.B / 255.0;

            double max = Math.Max(r, Math.Max(g, b));
            double min = Math.Min(r, Math.Min(g, b));
            double delta = max - min;

            // 明度
            v = max;

            // 饱和度
            s = max == 0 ? 0 : delta / max;

            // 色相
            if (delta == 0)
            {
                h = 0;
            }
            else if (max == r)
            {
                h = 60 * (((g - b) / delta) % 6);
            }
            else if (max == g)
            {
                h = 60 * ((b - r) / delta + 2);
            }
            else
            {
                h = 60 * ((r - g) / delta + 4);
            }

            if (h < 0) h += 360;
        }
    }
}
