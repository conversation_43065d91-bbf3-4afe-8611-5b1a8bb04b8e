using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Windows.Documents;
using System.Diagnostics;
using System.Linq;
using System.Windows;

namespace 像素喵笔记
{
    /// <summary>
    /// 文档图片管理器 - 新的简单实现方式
    /// </summary>
    public class DocumentImageManager
    {


        /// <summary>
        /// 文档信息类 - 简化版
        /// </summary>
        public class DocumentInfo
        {
            public string TextContent { get; set; } = string.Empty;
        }

        private readonly string _documentPath;
        private DocumentInfo _currentDocument;

        public DocumentImageManager(string documentPath)
        {
            _documentPath = documentPath;
            _currentDocument = new DocumentInfo();
        }

        /// <summary>
        /// 保存文档信息（HTML-like方式）
        /// </summary>
        public void SaveDocument(FlowDocument document)
        {
            try
            {
                Debug.WriteLine("=== 开始保存文档信息（HTML-like方式） ===");

                // 1. 将图片替换为HTML-like标记，保存为RTF
                var documentWithMarkers = CreateDocumentWithImageMarkers(document);

                // 2. 保存为RTF格式（保持格式）
                var textRange = new TextRange(documentWithMarkers.ContentStart, documentWithMarkers.ContentEnd);
                using (var stream = new MemoryStream())
                {
                    textRange.Save(stream, DataFormats.Rtf);
                    _currentDocument.TextContent = System.Text.Encoding.UTF8.GetString(stream.ToArray());
                }

                // 3. 保存到文件
                var infoPath = Path.ChangeExtension(_documentPath, ".rtf");
                File.WriteAllText(infoPath, _currentDocument.TextContent);

                Debug.WriteLine($"文档已保存为RTF: {infoPath}");
                Debug.WriteLine($"RTF长度: {_currentDocument.TextContent.Length}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存文档信息失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 加载文档信息并重建文档（HTML-like方式）
        /// </summary>
        public bool LoadDocument(FlowDocument document)
        {
            try
            {
                Debug.WriteLine("=== 开始加载文档信息（HTML-like方式） ===");

                var infoPath = Path.ChangeExtension(_documentPath, ".rtf");
                if (!File.Exists(infoPath))
                {
                    Debug.WriteLine("RTF文档文件不存在");
                    return false;
                }

                // 1. 加载RTF内容
                _currentDocument.TextContent = File.ReadAllText(infoPath);

                // 2. 从RTF重建文档并解析图片标记
                LoadFromRtfWithImageMarkers(document);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载文档信息失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 创建带图片标记的文档副本
        /// </summary>
        private FlowDocument CreateDocumentWithImageMarkers(FlowDocument original)
        {
            try
            {
                // 使用XAML序列化克隆文档
                var xaml = System.Windows.Markup.XamlWriter.Save(original);
                var clonedDoc = (FlowDocument)System.Windows.Markup.XamlReader.Parse(xaml);

                // 替换图片控件为HTML-like标记
                ReplaceImagesWithMarkers(clonedDoc);

                return clonedDoc;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建标记文档失败: {ex.Message}");
                return new FlowDocument();
            }
        }

        /// <summary>
        /// 替换图片控件为HTML-like标记
        /// </summary>
        private void ReplaceImagesWithMarkers(FlowDocument document)
        {
            var imagesToReplace = new List<(InlineUIContainer container, string marker)>();

            // 查找所有图片控件
            foreach (var block in document.Blocks)
            {
                if (block is Paragraph paragraph)
                {
                    foreach (var inline in paragraph.Inlines.ToList())
                    {
                        if (inline is InlineUIContainer container &&
                            container.Child is ResizableImageControl imageControl)
                        {
                            var filePath = imageControl.GetImageFilePath();
                            if (!string.IsNullOrEmpty(filePath))
                            {
                                // 转换为相对路径（相对于节点文件夹）
                                var nodeFolder = Path.GetDirectoryName(_documentPath);
                                var relativePath = Path.GetRelativePath(nodeFolder, filePath);

                                // 创建HTML-like标记，使用相对路径
                                var marker = $"<img src=\"{relativePath}\" width=\"{imageControl.Width}\" height=\"{imageControl.Height}\" id=\"{imageControl.Name}\"/>";
                                imagesToReplace.Add((container, marker));

                                Debug.WriteLine($"保存图片标记: src={relativePath}, 原始路径={filePath}");
                            }
                        }
                    }
                }
            }

            // 替换图片为标记
            foreach (var (container, marker) in imagesToReplace)
            {
                if (container.Parent is Paragraph paragraph)
                {
                    paragraph.Inlines.Remove(container);
                    paragraph.Inlines.Add(new Run(marker));
                    Debug.WriteLine($"替换图片为标记: {marker}");
                }
            }
        }

        /// <summary>
        /// 从RTF加载并解析图片标记
        /// </summary>
        private void LoadFromRtfWithImageMarkers(FlowDocument document)
        {
            try
            {
                // 1. 从RTF加载文档
                using (var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(_currentDocument.TextContent)))
                {
                    var textRange = new TextRange(document.ContentStart, document.ContentEnd);
                    textRange.Load(stream, DataFormats.Rtf);
                }

                // 2. 解析并替换图片标记
                ParseAndReplaceImageMarkers(document);

                Debug.WriteLine("RTF文档加载并解析图片标记完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从RTF加载失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 解析并替换图片标记
        /// </summary>
        private void ParseAndReplaceImageMarkers(FlowDocument document)
        {
            try
            {
                var markersToReplace = new List<(Run run, string marker)>();

                // 查找所有图片标记
                foreach (var block in document.Blocks)
                {
                    if (block is Paragraph paragraph)
                    {
                        foreach (var inline in paragraph.Inlines.ToList())
                        {
                            if (inline is Run run && run.Text.Contains("<img "))
                            {
                                var text = run.Text;
                                var startIndex = text.IndexOf("<img ");
                                while (startIndex >= 0)
                                {
                                    var endIndex = text.IndexOf("/>", startIndex);
                                    if (endIndex > startIndex)
                                    {
                                        var marker = text.Substring(startIndex, endIndex - startIndex + 2);
                                        markersToReplace.Add((run, marker));
                                        Debug.WriteLine($"找到图片标记: {marker}");
                                    }
                                    startIndex = text.IndexOf("<img ", endIndex + 1);
                                }
                            }
                        }
                    }
                }

                // 替换标记为图片控件
                foreach (var (run, marker) in markersToReplace)
                {
                    ReplaceMarkerWithImage(run, marker);
                }

                Debug.WriteLine($"解析并替换了 {markersToReplace.Count} 个图片标记");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"解析图片标记失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 替换单个标记为图片控件
        /// </summary>
        private void ReplaceMarkerWithImage(Run run, string marker)
        {
            try
            {
                Debug.WriteLine($"开始处理图片标记: {marker}");

                // 解析标记属性
                var src = ExtractAttribute(marker, "src");
                var widthStr = ExtractAttribute(marker, "width");
                var heightStr = ExtractAttribute(marker, "height");
                var id = ExtractAttribute(marker, "id");

                Debug.WriteLine($"解析属性: src={src}, width={widthStr}, height={heightStr}, id={id}");

                if (!string.IsNullOrEmpty(src))
                {
                    // 转换相对路径为绝对路径
                    var nodeFolder = Path.GetDirectoryName(_documentPath);
                    var absolutePath = Path.IsPathRooted(src) ? src : Path.Combine(nodeFolder, src);

                    Debug.WriteLine($"路径转换: nodeFolder={nodeFolder}");
                    Debug.WriteLine($"路径转换: src={src}, absolutePath={absolutePath}");
                    Debug.WriteLine($"文件存在检查: {File.Exists(absolutePath)}");

                    if (File.Exists(absolutePath))
                    {
                        // 创建图片控件
                        var imageControl = new ResizableImageControl();
                        imageControl.LoadImageFile(absolutePath);
                        imageControl.Name = id ?? Guid.NewGuid().ToString("N");

                        // 如果有保存的尺寸信息，使用保存的尺寸；否则使用原始尺寸
                        if (!string.IsNullOrEmpty(widthStr) && !string.IsNullOrEmpty(heightStr) &&
                            double.TryParse(widthStr, out double width) && double.TryParse(heightStr, out double height))
                        {
                            imageControl.Width = width;
                            imageControl.Height = height;
                        }
                        // 如果没有尺寸信息，ResizableImageControl会使用图片的原始尺寸

                        var container = new InlineUIContainer(imageControl);

                        // 替换Run中的标记
                        if (run.Parent is Paragraph paragraph)
                        {
                            var text = run.Text;
                            var markerIndex = text.IndexOf(marker);
                            var beforeMarker = text.Substring(0, markerIndex);
                            var afterMarker = text.Substring(markerIndex + marker.Length);

                            paragraph.Inlines.Remove(run);

                            // 添加标记前的文本
                            if (!string.IsNullOrEmpty(beforeMarker))
                            {
                                paragraph.Inlines.Add(new Run(beforeMarker));
                            }

                            // 添加图片
                            paragraph.Inlines.Add(container);

                            // 添加标记后的文本
                            if (!string.IsNullOrEmpty(afterMarker))
                            {
                                paragraph.Inlines.Add(new Run(afterMarker));
                            }

                            Debug.WriteLine($"✅ 成功替换标记为图片: {absolutePath}");
                        }
                    }
                    else
                    {
                        Debug.WriteLine($"❌ 图片文件不存在: {absolutePath}");
                        Debug.WriteLine($"   当前工作目录: {Directory.GetCurrentDirectory()}");

                        // 尝试查找可能的文件位置
                        var fileName = Path.GetFileName(absolutePath);
                        var possiblePaths = new[]
                        {
                            Path.Combine(nodeFolder, "附件", fileName),
                            Path.Combine(nodeFolder, fileName),
                            Path.Combine(Path.GetDirectoryName(nodeFolder), fileName)
                        };

                        foreach (var possiblePath in possiblePaths)
                        {
                            if (File.Exists(possiblePath))
                            {
                                Debug.WriteLine($"   找到可能的文件位置: {possiblePath}");

                                // 使用找到的路径创建图片控件
                                var imageControl = new ResizableImageControl();
                                imageControl.LoadImageFile(possiblePath);
                                imageControl.Name = id ?? Guid.NewGuid().ToString("N");

                                // 如果有保存的尺寸信息，使用保存的尺寸；否则使用原始尺寸
                                if (!string.IsNullOrEmpty(widthStr) && !string.IsNullOrEmpty(heightStr) &&
                                    double.TryParse(widthStr, out double width) && double.TryParse(heightStr, out double height))
                                {
                                    imageControl.Width = width;
                                    imageControl.Height = height;
                                }
                                // 如果没有尺寸信息，ResizableImageControl会使用图片的原始尺寸

                                var container = new InlineUIContainer(imageControl);

                                // 替换Run中的标记
                                if (run.Parent is Paragraph paragraph)
                                {
                                    var text = run.Text;
                                    var markerIndex = text.IndexOf(marker);
                                    var beforeMarker = text.Substring(0, markerIndex);
                                    var afterMarker = text.Substring(markerIndex + marker.Length);

                                    paragraph.Inlines.Remove(run);

                                    // 添加标记前的文本
                                    if (!string.IsNullOrEmpty(beforeMarker))
                                    {
                                        paragraph.Inlines.Add(new Run(beforeMarker));
                                    }

                                    // 添加图片
                                    paragraph.Inlines.Add(container);

                                    // 添加标记后的文本
                                    if (!string.IsNullOrEmpty(afterMarker))
                                    {
                                        paragraph.Inlines.Add(new Run(afterMarker));
                                    }

                                    Debug.WriteLine($"✅ 使用备用路径成功替换标记为图片: {possiblePath}");
                                }
                                return; // 成功找到并替换，退出方法
                            }
                        }

                        Debug.WriteLine($"❌ 未找到图片文件，保留标记文本");
                    }
                }
                else
                {
                    Debug.WriteLine($"❌ 图片标记src为空: {marker}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ 替换标记为图片失败: {ex.Message}");
                Debug.WriteLine($"   标记内容: {marker}");
                Debug.WriteLine($"   异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 提取HTML属性值
        /// </summary>
        private string? ExtractAttribute(string marker, string attributeName)
        {
            var pattern = $"{attributeName}=\"([^\"]+)\"";
            var match = Regex.Match(marker, pattern);
            return match.Success ? match.Groups[1].Value : null;
        }
        /// <summary>
        /// 从RTF内容直接加载文档 - 用于启动时加载
        /// </summary>
        public void LoadDocumentFromRtfContent(FlowDocument document, string rtfContent)
        {
            try
            {
                Debug.WriteLine("=== 从RTF内容直接加载文档 ===");

                // 1. 从RTF内容加载文档
                using (var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(rtfContent)))
                {
                    var textRange = new TextRange(document.ContentStart, document.ContentEnd);
                    textRange.Load(stream, DataFormats.Rtf);
                }

                // 2. 解析并替换图片标记
                ParseAndReplaceImageMarkers(document);

                Debug.WriteLine("RTF内容加载并解析图片标记完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"从RTF内容加载失败: {ex.Message}");
            }
        }






    }
}
