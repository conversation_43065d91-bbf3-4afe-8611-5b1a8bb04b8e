using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Windows.Documents;
using System.Windows.Markup;
using System.Xml;

namespace 像素喵笔记
{
    /// <summary>
    /// 文档序列化器
    /// 负责保存和加载富文本文档内容
    /// </summary>
    public class DocumentSerializer
    {
        #region 公共方法

        /// <summary>
        /// 保存文档内容
        /// </summary>
        public static void SaveDocument(PageNode pageNode, FlowDocument document, List<MediaFileInfo> mediaFiles)
        {
            try
            {
                var documentData = new DocumentData
                {
                    PageId = pageNode.Id,
                    PageName = pageNode.Name,
                    Content = SerializeFlowDocument(document),
                    MediaFiles = mediaFiles,
                    CreatedTime = DateTime.Now,
                    ModifiedTime = DateTime.Now
                };

                var json = JsonSerializer.Serialize(documentData, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                var filePath = GetDocumentFilePath(pageNode.Id);
                File.WriteAllText(filePath, json);

                System.Diagnostics.Debug.WriteLine($"文档已保存: {filePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存文档失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 加载文档内容
        /// </summary>
        public static DocumentData? LoadDocument(string pageId)
        {
            try
            {
                var filePath = GetDocumentFilePath(pageId);
                if (!File.Exists(filePath))
                {
                    return null;
                }

                var json = File.ReadAllText(filePath);
                var documentData = JsonSerializer.Deserialize<DocumentData>(json);

                System.Diagnostics.Debug.WriteLine($"文档已加载: {filePath}");
                return documentData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载文档失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 删除文档
        /// </summary>
        public static bool DeleteDocument(string pageId)
        {
            try
            {
                var filePath = GetDocumentFilePath(pageId);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    System.Diagnostics.Debug.WriteLine($"文档已删除: {filePath}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除文档失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 序列化FlowDocument
        /// </summary>
        public static string SerializeFlowDocument(FlowDocument document)
        {
            try
            {
                using var stringWriter = new StringWriter();
                using var xmlWriter = XmlWriter.Create(stringWriter);
                XamlWriter.Save(document, xmlWriter);
                return stringWriter.ToString();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"序列化FlowDocument失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 反序列化FlowDocument
        /// </summary>
        public static FlowDocument? DeserializeFlowDocument(string xaml)
        {
            try
            {
                if (string.IsNullOrEmpty(xaml))
                {
                    return null;
                }

                using var stringReader = new StringReader(xaml);
                using var xmlReader = XmlReader.Create(stringReader);
                return (FlowDocument)XamlReader.Load(xmlReader);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"反序列化FlowDocument失败: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取文档文件路径
        /// </summary>
        private static string GetDocumentFilePath(string pageId)
        {
            var documentsDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "像素喵笔记", "Documents");

            if (!Directory.Exists(documentsDirectory))
            {
                Directory.CreateDirectory(documentsDirectory);
            }

            return Path.Combine(documentsDirectory, $"{pageId}.json");
        }

        #endregion
    }

    /// <summary>
    /// 文档数据结构
    /// </summary>
    public class DocumentData
    {
        public string PageId { get; set; } = string.Empty;
        public string PageName { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public List<MediaFileInfo> MediaFiles { get; set; } = new List<MediaFileInfo>();
        public DateTime CreatedTime { get; set; }
        public DateTime ModifiedTime { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 文档导出器
    /// </summary>
    public class DocumentExporter
    {
        /// <summary>
        /// 导出为HTML
        /// </summary>
        public static string ExportToHtml(DocumentData documentData)
        {
            try
            {
                var html = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>{documentData.PageName}</title>
    <style>
        body {{ font-family: 'Segoe UI', sans-serif; margin: 40px; line-height: 1.6; }}
        .header {{ border-bottom: 1px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }}
        .content {{ max-width: 800px; }}
        .media {{ margin: 20px 0; }}
        .footer {{ margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>{documentData.PageName}</h1>
        <p>创建时间: {documentData.CreatedTime:yyyy-MM-dd HH:mm:ss}</p>
        <p>修改时间: {documentData.ModifiedTime:yyyy-MM-dd HH:mm:ss}</p>
    </div>
    <div class='content'>
        <!-- 这里可以添加内容转换逻辑 -->
        <p>文档内容将在这里显示</p>
    </div>
    <div class='footer'>
        <p>由像素喵笔记生成</p>
    </div>
</body>
</html>";
                return html;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出HTML失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 导出为Markdown
        /// </summary>
        public static string ExportToMarkdown(DocumentData documentData)
        {
            try
            {
                var markdown = $@"# {documentData.PageName}

**创建时间:** {documentData.CreatedTime:yyyy-MM-dd HH:mm:ss}
**修改时间:** {documentData.ModifiedTime:yyyy-MM-dd HH:mm:ss}

---

<!-- 文档内容 -->

---

*由像素喵笔记生成*
";
                return markdown;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出Markdown失败: {ex.Message}");
                return string.Empty;
            }
        }
    }
}
