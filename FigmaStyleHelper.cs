using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Effects;

namespace 像素喵笔记
{
    /// <summary>
    /// Figma风格样式辅助类
    /// 统一管理Figma风格的颜色、样式和UI创建
    /// </summary>
    public static class FigmaStyleHelper
    {
        // Figma标准颜色定义
        public static class Colors
        {
            public static readonly Color Primary = Color.FromRgb(0, 153, 255); // #0099ff

            public static readonly Color Background = Color.FromRgb(248, 249, 250);   // #F8F9FA
            public static readonly Color Border = Color.FromRgb(218, 220, 224);       // #DADCE0
            public static readonly Color Text = Color.FromRgb(32, 33, 36);            // #202124
            public static readonly Color TextSecondary = Color.FromRgb(95, 99, 104);  // #5F6368
            public static readonly Color Error = Color.FromRgb(255, 35, 35);          // #FF2323
            public static readonly Color Success = Color.FromRgb(52, 168, 83);        // #34A853
            public static readonly Color Warning = Color.FromRgb(251, 188, 4);        // #FBBC04
        }

        // 标准尺寸定义
        public static class Sizes
        {
            public const double BorderRadius = 8.0;
            public const double ButtonRadius = 6.0;
            public const double CardRadius = 12.0;
            public const double StandardPadding = 16.0;
            public const double SmallPadding = 8.0;
            public const double LargePadding = 24.0;
            public const double StandardFontSize = 13.0;
            public const double HeaderFontSize = 16.0;
            public const double SmallFontSize = 11.0;
        }

        /// <summary>
        /// 创建Figma风格的卡片容器
        /// </summary>
        public static Border CreateCard(string title = "", double cornerRadius = 0)
        {
            if (cornerRadius == 0) cornerRadius = Sizes.CardRadius;

            var card = new Border
            {
                Background = new SolidColorBrush(System.Windows.Media.Colors.White),
                BorderBrush = new SolidColorBrush(Colors.Border),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(cornerRadius),
                Padding = new Thickness(Sizes.StandardPadding),
                Margin = new Thickness(0, 0, 0, Sizes.SmallPadding),
                Effect = CreateShadow() // 使用统一的标准阴影
            };

            if (!string.IsNullOrEmpty(title))
            {
                var stackPanel = new StackPanel();
                var titleBlock = CreateLabel(title, true);
                titleBlock.Margin = new Thickness(0, 0, 0, Sizes.SmallPadding);
                stackPanel.Children.Add(titleBlock);
                card.Child = stackPanel;
                return card;
            }

            return card;
        }

        /// <summary>
        /// 创建Figma风格的标签
        /// </summary>
        public static TextBlock CreateLabel(string text, bool isHeader = false)
        {
            return new TextBlock
            {
                Text = text,
                FontFamily = new FontFamily("Segoe UI"),
                FontSize = isHeader ? Sizes.HeaderFontSize : Sizes.StandardFontSize,
                FontWeight = isHeader ? FontWeights.SemiBold : FontWeights.Normal,
                Foreground = new SolidColorBrush(Colors.Text),
                Margin = new Thickness(0, 0, 0, 4)
            };
        }

        /// <summary>
        /// 创建Figma风格的按钮
        /// </summary>
        public static Button CreateButton(string text, bool isPrimary = false)
        {
            var button = new Button
            {
                Content = text,
                FontFamily = new FontFamily("Segoe UI"),
                FontSize = Sizes.StandardFontSize,
                FontWeight = FontWeights.Medium,
                Padding = new Thickness(Sizes.StandardPadding, Sizes.SmallPadding, Sizes.StandardPadding, Sizes.SmallPadding),
                MinWidth = 80,
                Height = 36,
                BorderThickness = new Thickness(1),
                Cursor = System.Windows.Input.Cursors.Hand
            };

            if (isPrimary)
            {
                button.Background = new SolidColorBrush(Colors.Primary);
                button.Foreground = new SolidColorBrush(System.Windows.Media.Colors.White);
                button.BorderBrush = new SolidColorBrush(Colors.Primary);
            }
            else
            {
                button.Background = new SolidColorBrush(Colors.Background);
                button.Foreground = new SolidColorBrush(Colors.Text);
                button.BorderBrush = new SolidColorBrush(Colors.Border);
            }

            // 应用圆角
            var template = new ControlTemplate(typeof(Button));
            var border = new FrameworkElementFactory(typeof(Border));
            border.SetValue(Border.CornerRadiusProperty, new CornerRadius(Sizes.ButtonRadius));
            border.SetValue(Border.BackgroundProperty, new TemplateBindingExtension(Button.BackgroundProperty));
            border.SetValue(Border.BorderBrushProperty, new TemplateBindingExtension(Button.BorderBrushProperty));
            border.SetValue(Border.BorderThicknessProperty, new TemplateBindingExtension(Button.BorderThicknessProperty));

            var contentPresenter = new FrameworkElementFactory(typeof(ContentPresenter));
            contentPresenter.SetValue(ContentPresenter.HorizontalAlignmentProperty, HorizontalAlignment.Center);
            contentPresenter.SetValue(ContentPresenter.VerticalAlignmentProperty, VerticalAlignment.Center);
            border.AppendChild(contentPresenter);

            template.VisualTree = border;
            button.Template = template;

            return button;
        }

        /// <summary>
        /// 创建Figma风格的文本框
        /// </summary>
        public static TextBox CreateTextBox(string placeholder = "")
        {
            return new TextBox
            {
                FontFamily = new FontFamily("Segoe UI"),
                FontSize = Sizes.StandardFontSize,
                Padding = new Thickness(12, 8, 12, 8),
                BorderThickness = new Thickness(1),
                BorderBrush = new SolidColorBrush(Colors.Border),
                Background = new SolidColorBrush(System.Windows.Media.Colors.White),
                Foreground = new SolidColorBrush(Colors.Text),
                Height = 36,
                VerticalContentAlignment = VerticalAlignment.Center
            };
        }

        /// <summary>
        /// 创建Figma风格的下拉框
        /// </summary>
        public static ComboBox CreateComboBox()
        {
            return new ComboBox
            {
                FontFamily = new FontFamily("微软雅黑"),
                FontSize = Sizes.StandardFontSize,
                Padding = new Thickness(12, 8, 12, 8),
                BorderThickness = new Thickness(1),
                BorderBrush = new SolidColorBrush(Colors.Border),
                Background = new SolidColorBrush(System.Windows.Media.Colors.White),
                Foreground = new SolidColorBrush(Colors.Text),
                Height = 36
            };
        }

        /// <summary>
        /// 创建Figma风格的复选框
        /// </summary>
        public static CheckBox CreateCheckBox(string text)
        {
            return new CheckBox
            {
                Content = text,
                FontFamily = new FontFamily("Segoe UI"),
                FontSize = Sizes.StandardFontSize,
                Foreground = new SolidColorBrush(Colors.Text),
                VerticalContentAlignment = VerticalAlignment.Center
            };
        }

        /// <summary>
        /// 创建Figma风格的分隔线
        /// </summary>
        public static Border CreateSeparator()
        {
            return new Border
            {
                Height = 1,
                Background = new SolidColorBrush(Colors.Border),
                Margin = new Thickness(0, Sizes.SmallPadding, 0, Sizes.SmallPadding)
            };
        }

        /// <summary>
        /// 应用Figma风格的阴影效果 - 统一标准阴影
        /// </summary>
        public static DropShadowEffect CreateShadow(double depth = 1, double blur = 4, double opacity = 0.08)
        {
            return new DropShadowEffect
            {
                Color = System.Windows.Media.Colors.Black,
                Direction = 270,
                ShadowDepth = depth,
                Opacity = opacity,
                BlurRadius = blur,
                RenderingBias = RenderingBias.Quality
            };
        }

        /// <summary>
        /// 获取Figma风格的字体设置
        /// </summary>
        public static FontSettings GetDefaultFontSettings()
        {
            return new FontSettings
            {
                FontFamily = "微软雅黑",
                FontSize = Sizes.StandardFontSize,
                IsBold = false,
                IsItalic = false,
                TextColor = Colors.Text,
                BackgroundColor = System.Windows.Media.Colors.Transparent,
                IsUnderline = false,
                IsStrikethrough = false
            };
        }

        /// <summary>
        /// 创建Figma风格的对话框容器
        /// </summary>
        public static Border CreateDialogContainer(double width = 400, double height = 300)
        {
            return new Border
            {
                Background = new SolidColorBrush(System.Windows.Media.Colors.White),
                CornerRadius = new CornerRadius(Sizes.BorderRadius),
                Effect = CreateShadow(), // 使用统一的标准阴影
                Width = width,
                Height = height,
                Padding = new Thickness(Sizes.LargePadding)
            };
        }
    }
}
