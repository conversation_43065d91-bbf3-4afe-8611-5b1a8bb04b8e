<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 标准Figma风格卡片样式 - 带阴影，支持主题切换 -->
    <Style x:Key="StandardFigmaCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource AppCardBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.15" BlurRadius="16"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 标准Figma风格窗口阴影 -->
    <DropShadowEffect x:Key="StandardFigmaWindowShadow"
                     Color="Black"
                     Direction="270"
                     ShadowDepth="4"
                     Opacity="0.15"
                     BlurRadius="16"/>

    <!-- 标准Figma风格下拉菜单阴影 -->
    <DropShadowEffect x:Key="StandardFigmaDropdownShadow"
                     Color="Black"
                     Direction="270"
                     ShadowDepth="1"
                     Opacity="0.08"
                     BlurRadius="4"/>

    <!-- 统一的标准阴影样�?- 所有界面元素使用此标准 -->
    <DropShadowEffect x:Key="StandardFigmaShadow"
                     Color="Black"
                     Direction="270"
                     ShadowDepth="1"
                     Opacity="0.08"
                     BlurRadius="4"
                     RenderingBias="Quality"/>

    <!-- 标准标签样式 -->
    <Style x:Key="FigmaStandardLabelStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="#5F6368"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
    </Style>

    <!-- 标准Label控件样式 -->
    <Style x:Key="FigmaLabelControlStyle" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="#5F6368"/>
        <Setter Property="Margin" Value="0,0,0,4"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Figma风格卡片样式 - 支持主题切换 -->
    <Style x:Key="FigmaCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource AppCardBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4"
                                Opacity="0.15" BlurRadius="16"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 内部卡片样式 - 无阴�?-->
    <Style x:Key="FigmaInnerCardStyle" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DADCE0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="12"/>
    </Style>



    <!-- 颜色选择按钮样式 -->
    <Style x:Key="FigmaColorPickerButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DADCE0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="#5F6368"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="Center"
                                        Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="border" Value="#0099ff"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="#0099ff"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>



    <!-- ComboBox样式 - 修复下拉问题，支持主题切换 -->
    <Style x:Key="FigmaComboBoxStyle" TargetType="ComboBox">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="MinHeight" Value="36"/>
        <!-- 🔧 删除强制的IsEditable和IsReadOnly设置，让控件自己决定 -->
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <Style TargetType="ComboBoxItem">
                    <Setter Property="Padding" Value="12,8"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ComboBoxItem">
                                <!-- 🔧 修复ComboBoxItem，确保文字显示，支持主题切换 -->
                                <Border x:Name="ItemBorder" Background="Transparent" Padding="{TemplateBinding Padding}">
                                    <ContentPresenter TextElement.Foreground="{DynamicResource ButtonForegroundBrush}" TextElement.FontSize="12"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="ItemBorder" Property="Background" Value="{DynamicResource MenuItemHoverBrush}"/>
                                    </Trigger>
                                    <Trigger Property="IsSelected" Value="True">
                                        <Setter TargetName="ItemBorder" Property="Background" Value="#D2E3FC"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ComboBox">
                    <Grid>
                        <ToggleButton x:Name="ToggleButton"
                                      BorderBrush="{TemplateBinding BorderBrush}"
                                      Background="{TemplateBinding Background}"
                                      Focusable="False"
                                      IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                      ClickMode="Press">
                            <ToggleButton.Template>
                                <ControlTemplate TargetType="ToggleButton">
                                    <Border x:Name="border"
                                            Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="1"
                                            CornerRadius="8">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <!-- 🔧 修复ContentPresenter，确保文字显�?-->
                                            <ContentPresenter Grid.Column="0"
                                                             Margin="12,8"
                                                             HorizontalAlignment="Left"
                                                             VerticalAlignment="Center"
                                                             Content="{Binding Path=SelectionBoxItem, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}"
                                                             ContentTemplate="{Binding Path=SelectionBoxItemTemplate, RelativeSource={RelativeSource AncestorType={x:Type ComboBox}}}"
                                                             TextElement.Foreground="#202124"
                                                             TextElement.FontSize="12"/>
                                            <Path Grid.Column="1"
                                                  Data="M0,0 L4,4 L8,0"
                                                  Stroke="#5F6368"
                                                  StrokeThickness="1.5"
                                                  Margin="0,0,12,0"
                                                  VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                                        </Trigger>
                                        <Trigger Property="IsChecked" Value="True">
                                            <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </ToggleButton.Template>
                        </ToggleButton>

                        <!-- 🔧 添加可编辑文本框支持 -->
                        <TextBox x:Name="PART_EditableTextBox"
                                 Margin="12,8,32,8"
                                 Background="Transparent"
                                 BorderThickness="0"
                                 Foreground="#202124"
                                 FontSize="12"
                                 VerticalAlignment="Center"
                                 HorizontalAlignment="Left"
                                 Focusable="True"
                                 Visibility="Hidden"
                                 IsReadOnly="{Binding Path=IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}"/>
                        <Popup x:Name="PART_Popup"
                               IsOpen="{Binding Path=IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                               Placement="Bottom"
                               PlacementTarget="{Binding ElementName=ToggleButton}"
                               AllowsTransparency="True"
                               Focusable="False"
                               PopupAnimation="Slide">
                            <Border Background="White"
                                    BorderBrush="#DADCE0"
                                    BorderThickness="1"
                                    CornerRadius="8"
                                    MinWidth="{Binding ActualWidth, ElementName=ToggleButton}"
                                    MaxHeight="300">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black"
                                                      Direction="270"
                                                      ShadowDepth="3"
                                                      Opacity="0.2"
                                                      BlurRadius="10"/>
                                </Border.Effect>
                                <ScrollViewer VerticalScrollBarVisibility="Auto"
                                             HorizontalScrollBarVisibility="Disabled">
                                    <ItemsPresenter KeyboardNavigation.DirectionalNavigation="Contained"/>
                                </ScrollViewer>
                            </Border>
                        </Popup>
                    </Grid>

                    <!-- 🔧 添加可编辑模式触发器 -->
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEditable" Value="True">
                            <Setter Property="Visibility" TargetName="PART_EditableTextBox" Value="Visible"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格TextBox样式 -->
    <Style x:Key="FigmaTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DADCE0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="#202124"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Focusable="false"
                                    HorizontalScrollBarVisibility="Hidden"
                                    VerticalScrollBarVisibility="Hidden"
                                    Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 符号按钮样式 -->
    <Style x:Key="SymbolButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DADCE0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="#202124"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="border" Value="#E8F0FE"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="#D2E3FC"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 编号样式按钮 - 支持主题切换 -->
    <Style x:Key="NumberingButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Width" Value="100"/>
        <Setter Property="Height" Value="80"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="border" Value="#E8F0FE"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="#D2E3FC"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                        </Trigger>
                        <Trigger Property="Tag" Value="Selected">
                            <Setter Property="Background" TargetName="border" Value="#E8F0FE"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                            <Setter Property="BorderThickness" TargetName="border" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 选项卡按钮样�?-->
    <Style x:Key="TabButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Foreground" Value="#5F6368"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="Border" Background="Transparent" BorderThickness="0,0,0,2" BorderBrush="Transparent" Padding="20,12">
                        <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Center"
                                        Margin="0" RecognizesAccessKey="True"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Tag" Value="Selected">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <ColorAnimation Storyboard.TargetName="Border"
                                                      Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                      To="#0099ff" Duration="0:0:0.3">
                                            <ColorAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseOut"/>
                                            </ColorAnimation.EasingFunction>
                                        </ColorAnimation>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Setter TargetName="Border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="#0099ff"/>
                            <Setter TargetName="ContentSite" Property="TextElement.FontWeight" Value="SemiBold"/>
                        </Trigger>
                        <Trigger Property="Tag" Value="">
                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="#5F6368"/>
                            <Setter TargetName="ContentSite" Property="TextElement.FontWeight" Value="Normal"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="#0099ff"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>



    <!-- 主窗口按钮样式 - 支持主题切换 -->
    <Style x:Key="FigmaMainButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="Margin" Value="2,0"/>
        <Setter Property="MinWidth" Value="36"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="#F1F3F4"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#DADCE0"/>
                            <Setter Property="Foreground" Value="#9AA0A6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 主窗口列表按钮样式 - 支持主题切换 -->
    <Style x:Key="FigmaListButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="Width" Value="32"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格通用按钮样式 - 支持主题切换 -->
    <Style x:Key="FigmaButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.08" BlurRadius="4"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0066cc"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0066cc"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="#F1F3F4"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#DADCE0"/>
                            <Setter Property="Foreground" Value="#9AA0A6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 🔧 修复：Figma风格工具按钮样式 - 支持选中状态和主题切换 -->
    <Style x:Key="FigmaToolButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter x:Name="contentPresenter"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 🔧 修复：选中状�?- 蓝色背景，白色图�?-->
                        <Trigger Property="Tag" Value="Selected">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>

                        <!-- 🔧 修复：悬停状�?- 只在未选中时生�?-->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="Tag" Value="{x:Null}"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="#F0F0F0"/>
                        </MultiTrigger>

                        <!-- 🔧 修复：选中状态下的悬�?- 保持蓝色背景和白色图�?-->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True"/>
                                <Condition Property="Tag" Value="Selected"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </MultiTrigger>

                        <!-- 按下状�?-->
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#E0E0E0"/>
                        </Trigger>

                        <!-- 选中状态下的按�?-->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsPressed" Value="True"/>
                                <Condition Property="Tag" Value="Selected"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="border" Property="Background" Value="#0077cc"/>
                            <Setter Property="Foreground" Value="White"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 弹出动画样式 -->
    <Storyboard x:Key="PopupShowAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.2"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" From="0.8" To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" From="0.8" To="1" Duration="0:0:0.2">
            <DoubleAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="PopupHideAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity" From="1" To="0" Duration="0:0:0.15"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)" From="1" To="0.8" Duration="0:0:0.15"/>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)" From="1" To="0.8" Duration="0:0:0.15"/>
    </Storyboard>

    <!-- Figma风格复选框样式 -->
    <Style x:Key="FigmaCheckBoxStyle" TargetType="CheckBox">
        <Setter Property="Foreground" Value="#202124"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="CheckBox">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 复选框图标 -->
                        <Border x:Name="checkBoxBorder"
                                Grid.Column="0"
                                Width="16"
                                Height="16"
                                Background="White"
                                BorderBrush="#DADCE0"
                                BorderThickness="1"
                                CornerRadius="3"
                                Margin="0,0,8,0">
                            <Path x:Name="checkMark"
                                  Data="M1,5 L5,9 L11,2"
                                  Stroke="#FFFFFF"
                                  StrokeThickness="2"
                                  Visibility="Collapsed"/>
                        </Border>

                        <!-- 内容 -->
                        <ContentPresenter Grid.Column="1"
                                          VerticalAlignment="Center"
                                          HorizontalAlignment="Left"/>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="checkBoxBorder" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="checkBoxBorder" Property="BorderBrush" Value="#0099ff"/>
                            <Setter TargetName="checkMark" Property="Visibility" Value="Visible"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="checkBoxBorder" Property="BorderBrush" Value="#0099ff"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格单选按钮样�?-->
    <Style x:Key="FigmaRadioButtonStyle" TargetType="RadioButton">
        <Setter Property="Foreground" Value="#202124"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RadioButton">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 单选按钮图�?-->
                        <Border x:Name="radioButtonBorder"
                                Grid.Column="0"
                                Width="16"
                                Height="16"
                                Background="White"
                                BorderBrush="#DADCE0"
                                BorderThickness="1"
                                CornerRadius="8"
                                Margin="0,0,8,0">
                            <Ellipse x:Name="radioMark"
                                    Width="8"
                                    Height="8"
                                    Fill="#0099ff"
                                    Visibility="Collapsed"/>
                        </Border>

                        <!-- 内容 -->
                        <ContentPresenter Grid.Column="1"
                                          VerticalAlignment="Center"
                                          HorizontalAlignment="Left"/>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="radioButtonBorder" Property="BorderBrush" Value="#0099ff"/>
                            <Setter TargetName="radioMark" Property="Visibility" Value="Visible"/>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="radioButtonBorder" Property="BorderBrush" Value="#0099ff"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 颜色选择器按钮样�?-->
    <Style x:Key="ColorPickerButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="24"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            BorderBrush="#DADCE0"
                            BorderThickness="1"
                            CornerRadius="4"
                            Background="{Binding RelativeSource={RelativeSource Mode=TemplatedParent}, Path=Background}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                            <Setter Property="BorderThickness" TargetName="border" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>



    <!-- 浮动元素圆点按钮样式 -->
    <Style x:Key="FigmaFloatingButtonStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="16"/>
        <Setter Property="Height" Value="16"/>
        <Setter Property="Stroke" Value="White"/>
        <Setter Property="StrokeThickness" Value="2"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Visibility" Value="Hidden"/>
    </Style>

    <!-- 胶囊形状颜色面板样式 -->
    <Style x:Key="FigmaColorPanelStyle" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#E8EAED"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="20"/>
        <Setter Property="Effect" Value="{StaticResource StandardFigmaShadow}"/>
    </Style>

    <!-- 颜色预设按钮样式 -->
    <Style x:Key="FigmaColorPresetStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="24"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Stroke" Value="#E8EAED"/>
        <Setter Property="StrokeThickness" Value="1"/>
        <Setter Property="Margin" Value="4"/>
        <Setter Property="Cursor" Value="Hand"/>
    </Style>



    <!-- Figma风格滚动条滑块样�?-->
    <Style x:Key="FigmaScrollBarThumbStyle" TargetType="Thumb">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Thumb">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            Margin="1"/>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#5F6368"/>
                        </Trigger>
                        <Trigger Property="IsDragging" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#202124"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格滚动条样式 - 支持主题切换 -->
    <Style x:Key="FigmaScrollBarStyle" TargetType="ScrollBar">
        <Setter Property="Background" Value="{DynamicResource ScrollBarTrackBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Width" Value="12"/>
        <Setter Property="MinWidth" Value="12"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollBar">
                    <Grid x:Name="GridRoot" Width="12" Background="{TemplateBinding Background}">
                        <Track x:Name="PART_Track" IsDirectionReversed="True" Focusable="False">
                            <Track.Thumb>
                                <Thumb x:Name="Thumb" Background="{DynamicResource ScrollBarThumbBrush}" Style="{StaticResource FigmaScrollBarThumbStyle}"/>
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton x:Name="PageUp" Command="ScrollBar.PageDownCommand" Opacity="0" Focusable="False"/>
                            </Track.IncreaseRepeatButton>
                            <Track.DecreaseRepeatButton>
                                <RepeatButton x:Name="PageDown" Command="ScrollBar.PageUpCommand" Opacity="0" Focusable="False"/>
                            </Track.DecreaseRepeatButton>
                        </Track>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Thumb" Property="Background" Value="#9AA0A6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Orientation" Value="Horizontal">
                <Setter Property="Width" Value="Auto"/>
                <Setter Property="MinWidth" Value="0"/>
                <Setter Property="Height" Value="8"/>
                <Setter Property="MinHeight" Value="8"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ScrollBar">
                            <Grid x:Name="GridRoot" Height="8" Background="{TemplateBinding Background}">
                                <Track x:Name="PART_Track" IsDirectionReversed="False" Focusable="False">
                                    <Track.Thumb>
                                        <Thumb x:Name="Thumb" Background="#DADCE0" Style="{StaticResource FigmaScrollBarThumbStyle}"/>
                                    </Track.Thumb>
                                    <Track.IncreaseRepeatButton>
                                        <RepeatButton x:Name="PageUp" Command="ScrollBar.PageRightCommand" Opacity="0" Focusable="False"/>
                                    </Track.IncreaseRepeatButton>
                                    <Track.DecreaseRepeatButton>
                                        <RepeatButton x:Name="PageDown" Command="ScrollBar.PageLeftCommand" Opacity="0" Focusable="False"/>
                                    </Track.DecreaseRepeatButton>
                                </Track>
                            </Grid>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Thumb" Property="Background" Value="#9AA0A6"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Figma风格对话框样�?-->
    <Style x:Key="FigmaDialogStyle" TargetType="Window">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="WindowStyle" Value="None"/>
        <Setter Property="AllowsTransparency" Value="True"/>
        <Setter Property="ResizeMode" Value="NoResize"/>
        <Setter Property="ShowInTaskbar" Value="False"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Window">
                    <Grid>
                        <!-- 外层透明容器，为阴影留出空间 -->
                        <Border Background="Transparent" Margin="20">
                            <Border Background="White"
                                    BorderBrush="#DADCE0"
                                    BorderThickness="1"
                                    CornerRadius="12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.25" BlurRadius="16"/>
                                </Border.Effect>
                                <ContentPresenter Margin="0"/>
                            </Border>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格关闭按钮样式 - 支持主题切换 -->
    <Style x:Key="FigmaCloseButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Foreground" Value="{DynamicResource AppSecondaryTextBrush}"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Width" Value="24"/>
        <Setter Property="Height" Value="24"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            BorderThickness="0">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F1F3F4"/>
                <Setter Property="Foreground" Value="#FF2323"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Figma风格选择样式 -->
    <Style x:Key="FigmaSelectionStyle" TargetType="ListBoxItem">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="Margin" Value="0,1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListBoxItem">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F1F3F4"/>
            </Trigger>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="#E8F0FE"/>
                <Setter Property="Foreground" Value="#0099ff"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Figma风格主要按钮样式 -->
    <Style x:Key="FigmaPrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#0099ff"/>
        <Setter Property="BorderBrush" Value="#0099ff"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="8,0,0,0"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#1557B0"/>
                <Setter Property="BorderBrush" Value="#1557B0"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#0F4C8C"/>
                <Setter Property="BorderBrush" Value="#0F4C8C"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Figma风格次要按钮样式 -->
    <Style x:Key="FigmaSecondaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DADCE0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="#202124"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="8,0,0,0"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F8F9FA"/>
                <Setter Property="BorderBrush" Value="#DADCE0"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#F1F3F4"/>
                <Setter Property="BorderBrush" Value="#DADCE0"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Figma风格对话框标题样�?-->
    <Style x:Key="FigmaDialogTitleStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="微软雅黑"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="#202124"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="TextAlignment" Value="Center"/>
    </Style>

    <!-- Figma风格对话框内容样�?-->
    <Style x:Key="FigmaDialogContentStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="微软雅黑"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="#202124"/>
        <Setter Property="Margin" Value="0,0,0,24"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- Figma风格对话框关闭按钮样式 - 支持主题切换 -->
    <Style x:Key="FigmaDialogCloseButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontFamily" Value="微软雅黑"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Width" Value="32"/>
        <Setter Property="Height" Value="32"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#FF2323"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#FF2323"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#E01E1E"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#E01E1E"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格对话框取消按钮样�?-->
    <Style x:Key="FigmaDialogCancelButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DADCE0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="#202124"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontFamily" Value="微软雅黑"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="Margin" Value="4,0"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格对话框确认按钮样�?-->
    <Style x:Key="FigmaDialogConfirmButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DADCE0"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="#202124"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontFamily" Value="微软雅黑"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="20,10"/>
        <Setter Property="Margin" Value="4,0"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="MinHeight" Value="36"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#0099ff"/>
                            <Setter TargetName="border" Property="BorderBrush" Value="#0099ff"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>





    <!-- Figma风格取消按钮样式 - 支持主题切换 -->
    <Style x:Key="FigmaCancelButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="8,0,0,0"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#F8F9FA"/>
                <Setter Property="BorderBrush" Value="#DADCE0"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#F1F3F4"/>
                <Setter Property="BorderBrush" Value="#DADCE0"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Figma风格对话框按钮样�?-->
    <Style x:Key="FigmaDialogButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#0099ff"/>
        <Setter Property="BorderBrush" Value="#0099ff"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="8,0,0,0"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#1557B0"/>
                <Setter Property="BorderBrush" Value="#1557B0"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#0F4C8C"/>
                <Setter Property="BorderBrush" Value="#0F4C8C"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Figma风格标签样式 - 支持主题切换 -->
    <Style x:Key="FigmaLabelStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="微软雅黑"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 代码编辑器彩色按钮样�?-->
    <Style x:Key="CodeEditorColorButtonStyle" TargetType="Button">
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.1" BlurRadius="3"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                        TextBlock.Foreground="{TemplateBinding Foreground}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.15" BlurRadius="6"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                            <Setter TargetName="border" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="2"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="border" Property="Background" Value="#F1F3F4"/>
                            <Setter Property="Foreground" Value="#9AA0A6"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格右键菜单样式 -->
    <Style x:Key="FigmaContextMenuStyle" TargetType="ContextMenu">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ContextMenu">
                    <!-- 外层容器，为阴影留出空间 -->
                    <Grid Margin="10">
                        <Border Background="{DynamicResource AppCardBackgroundBrush}"
                                BorderBrush="{DynamicResource AppBorderBrush}"
                                BorderThickness="1"
                                CornerRadius="8"
                                Padding="4">
                            <Border.Effect>
                                <DropShadowEffect Color="Black"
                                                Direction="270"
                                                ShadowDepth="3"
                                                Opacity="0.15"
                                                BlurRadius="8"
                                                RenderingBias="Quality"/>
                            </Border.Effect>
                            <StackPanel IsItemsHost="True"/>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格菜单项样式 - 动态主题 -->
    <Style x:Key="FigmaMenuItemStyle" TargetType="MenuItem">
        <Setter Property="Foreground" Value="{DynamicResource AppForegroundBrush}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="MenuItem">
                    <Border x:Name="Border"
                            Background="Transparent"
                            BorderThickness="0"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- 图标 -->
                            <ContentPresenter x:Name="Icon"
                                              Grid.Column="0"
                                              Content="{TemplateBinding Icon}"
                                              Margin="0,0,8,0"
                                              VerticalAlignment="Center"/>

                            <!-- 文本 -->
                            <ContentPresenter x:Name="HeaderHost"
                                              Grid.Column="1"
                                              Content="{TemplateBinding Header}"
                                              VerticalAlignment="Center"/>

                            <!-- 子菜单箭头 -->
                            <Path x:Name="ArrowPath"
                                  Grid.Column="2"
                                  Data="M0,0 L4,4 L0,8 Z"
                                  Fill="{DynamicResource AppSecondaryTextBrush}"
                                  Margin="8,0,0,0"
                                  VerticalAlignment="Center"
                                  Visibility="Collapsed"/>
                        </Grid>
                    </Border>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource MenuItemHoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="{DynamicResource MenuItemPressedBrush}"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Foreground" Value="{DynamicResource AppSecondaryTextBrush}"/>
                        </Trigger>
                        <Trigger Property="Role" Value="SubmenuHeader">
                            <Setter TargetName="ArrowPath" Property="Visibility" Value="Visible"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格分隔符样�?-->
    <Style x:Key="FigmaSeparatorStyle" TargetType="Separator">
        <Setter Property="Background" Value="#E8EAED"/>
        <Setter Property="Height" Value="1"/>
        <Setter Property="Margin" Value="8,4"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Separator">
                    <Border Background="{TemplateBinding Background}"
                            Height="{TemplateBinding Height}"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Figma风格滑块样式 -->
    <Style x:Key="FigmaSliderStyle" TargetType="Slider">
        <Setter Property="Background" Value="#E8EAED"/>
        <Setter Property="Foreground" Value="#4285F4"/>
        <Setter Property="Height" Value="4"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Slider">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto" MinHeight="{TemplateBinding MinHeight}"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 滑块轨道 -->
                        <Border Grid.Row="1"
                                x:Name="TrackBackground"
                                Background="{TemplateBinding Background}"
                                Height="4"
                                CornerRadius="2"
                                VerticalAlignment="Center"/>

                        <!-- 进度�?-->
                        <Border Grid.Row="1"
                                x:Name="PART_SelectionRange"
                                Background="{TemplateBinding Foreground}"
                                Height="4"
                                CornerRadius="2"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"/>

                        <!-- 滑块拖拽�?-->
                        <Track Grid.Row="1" x:Name="PART_Track">
                            <Track.Thumb>
                                <Thumb x:Name="Thumb"
                                       Background="White"
                                       BorderBrush="{TemplateBinding Foreground}"
                                       BorderThickness="2"
                                       Width="16"
                                       Height="16"
                                       Template="{DynamicResource SliderThumbTemplate}"/>
                            </Track.Thumb>
                        </Track>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 滑块拖拽点模�?-->
    <ControlTemplate x:Key="SliderThumbTemplate" TargetType="Thumb">
        <Border Background="{TemplateBinding Background}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="{TemplateBinding BorderThickness}"
                CornerRadius="8"
                Effect="{StaticResource StandardFigmaShadow}"/>
    </ControlTemplate>

    <!-- ToolTip 样式 - 修复定位问题 -->
    <Style TargetType="ToolTip" x:Key="FigmaToolTipStyle">
        <Setter Property="Background" Value="#2D2D30"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#3F3F46"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="Placement" Value="Bottom"/>
        <Setter Property="HorizontalOffset" Value="0"/>
        <Setter Property="VerticalOffset" Value="5"/>
        <Setter Property="HasDropShadow" Value="True"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="4"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToolTip">
                    <Grid>
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}"
                                Effect="{TemplateBinding Effect}"
                                HorizontalAlignment="Center">
                            <ContentPresenter Content="{TemplateBinding Content}"
                                            ContentTemplate="{TemplateBinding ContentTemplate}"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 默认 ToolTip 样式 -->
    <Style TargetType="ToolTip" BasedOn="{StaticResource FigmaToolTipStyle}"/>

    <!-- Figma风格进度条样�?-->
    <Style x:Key="FigmaProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Background" Value="#F1F3F4"/>
        <Setter Property="Foreground" Value="#0099ff"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Height" Value="4"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border x:Name="TemplateRoot"
                            Background="{TemplateBinding Background}"
                            CornerRadius="2"
                            SnapsToDevicePixels="True">
                        <Grid>
                            <Rectangle x:Name="PART_Track"
                                       Fill="{TemplateBinding Background}"
                                       RadiusX="2"
                                       RadiusY="2"/>
                            <Grid x:Name="PART_Indicator"
                                  ClipToBounds="True"
                                  HorizontalAlignment="Left">
                                <Rectangle x:Name="Indicator"
                                           Fill="{TemplateBinding Foreground}"
                                           RadiusX="2"
                                           RadiusY="2"/>
                                <Rectangle x:Name="Animation"
                                           Fill="{TemplateBinding Foreground}"
                                           RadiusX="2"
                                           RadiusY="2"
                                           RenderTransformOrigin="0.5,0.5">
                                    <Rectangle.RenderTransform>
                                        <TransformGroup>
                                            <ScaleTransform/>
                                            <SkewTransform/>
                                            <RotateTransform/>
                                            <TranslateTransform/>
                                        </TransformGroup>
                                    </Rectangle.RenderTransform>
                                </Rectangle>
                            </Grid>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsIndeterminate" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard RepeatBehavior="Forever">
                                        <DoubleAnimation Storyboard.TargetName="Animation"
                                                         Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[3].(TranslateTransform.X)"
                                                         From="-100" To="100" Duration="0:0:1"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- ========== 深色主题样式 ========== -->

    <!-- 深色主题 - 标准卡片样式 -->
    <Style x:Key="DarkFigmaCardStyle" TargetType="Border">
        <Setter Property="Background" Value="#2D3748"/>
        <Setter Property="BorderBrush" Value="#4A5568"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.3" BlurRadius="16"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 深色主题 - 标准按钮样式 -->
    <Style x:Key="DarkFigmaButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#4A5568"/>
        <Setter Property="Foreground" Value="#F7FAFC"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.15" BlurRadius="4"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#718096"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#2D3748"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 深色主题 - 主要按钮样式 -->
    <Style x:Key="DarkFigmaPrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="#3182CE"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="MinHeight" Value="32"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="4"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#2C5282"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#2A4365"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 深色主题 - 文本样式 -->
    <Style x:Key="DarkFigmaTextStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="#F7FAFC"/>
    </Style>

    <!-- 深色主题 - 标题样式 -->
    <Style x:Key="DarkFigmaTitleStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="#F7FAFC"/>
    </Style>

    <!-- 深色主题 - 右键菜单样式 -->
    <Style x:Key="DarkFigmaContextMenuStyle" TargetType="ContextMenu">
        <Setter Property="Background" Value="#2D3748"/>
        <Setter Property="BorderBrush" Value="#4A5568"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="4"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 深色主题 - 菜单项样式 -->
    <Style x:Key="DarkFigmaMenuItemStyle" TargetType="MenuItem">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="#F7FAFC"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="MenuItem">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Left" VerticalAlignment="Center"
                                        TextBlock.Foreground="{TemplateBinding Foreground}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#4A5568"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="border" Property="Background" Value="#718096"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 深色主题 - 文本编辑器样式 -->
    <Style x:Key="DarkFigmaTextBoxStyle" TargetType="TextBox">
        <Setter Property="Background" Value="#2D3748"/>
        <Setter Property="Foreground" Value="#F7FAFC"/>
        <Setter Property="BorderBrush" Value="#4A5568"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="CaretBrush" Value="#F7FAFC"/>
        <Setter Property="SelectionBrush" Value="#4299E1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Focusable="false"
                                    HorizontalScrollBarVisibility="Hidden"
                                    VerticalScrollBarVisibility="Hidden"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="#63B3ED"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="#4299E1"/>
                            <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 深色主题 - RichTextBox样式 -->
    <Style x:Key="DarkFigmaRichTextBoxStyle" TargetType="RichTextBox">
        <Setter Property="Background" Value="#2D3748"/>
        <Setter Property="Foreground" Value="#F7FAFC"/>
        <Setter Property="BorderBrush" Value="#4A5568"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="CaretBrush" Value="#F7FAFC"/>
        <Setter Property="SelectionBrush" Value="#4299E1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="RichTextBox">
                    <Border x:Name="border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="6">
                        <ScrollViewer x:Name="PART_ContentHost"
                                    Focusable="false"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="#63B3ED"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="border" Property="BorderBrush" Value="#4299E1"/>
                            <Setter TargetName="border" Property="BorderThickness" Value="2"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
