using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Diagnostics;

namespace 像素喵笔记
{
    /// <summary>
    /// 文件引用管理器 - 管理文件在不同节点间的引用关系
    /// </summary>
    public class FileReferenceManager
    {
        private readonly string _saveRootPath;
        private readonly string _attachmentsFolderName = "附件";

        public FileReferenceManager()
        {
            _saveRootPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SAVE");
        }

        /// <summary>
        /// 检查文件是否被其他节点引用
        /// </summary>
        public bool IsFileReferencedByOtherNodes(string filePath, string currentNodePath = "")
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    return false;

                var fileName = Path.GetFileName(filePath);
                var currentAttachmentDir = Path.GetDirectoryName(filePath);

                // 如果没有提供当前节点路径，从文件路径推断
                if (string.IsNullOrEmpty(currentNodePath) && !string.IsNullOrEmpty(currentAttachmentDir))
                {
                    currentNodePath = Path.GetDirectoryName(currentAttachmentDir) ?? "";
                }

                Debug.WriteLine($"检查文件引用: {fileName}");
                Debug.WriteLine($"当前节点路径: {currentNodePath}");

                // 获取所有附件文件夹
                var allAttachmentDirs = GetAllAttachmentDirectories();
                int referenceCount = 0;

                foreach (var attachmentDir in allAttachmentDirs)
                {
                    // 跳过当前节点的附件文件夹
                    var nodeDir = Path.GetDirectoryName(attachmentDir);
                    if (!string.IsNullOrEmpty(currentNodePath) && 
                        string.Equals(nodeDir, currentNodePath, StringComparison.OrdinalIgnoreCase))
                        continue;

                    var otherFilePath = Path.Combine(attachmentDir, fileName);
                    if (File.Exists(otherFilePath))
                    {
                        referenceCount++;
                        Debug.WriteLine($"发现引用: {otherFilePath}");
                    }
                }

                Debug.WriteLine($"文件 {fileName} 被 {referenceCount} 个其他节点引用");
                return referenceCount > 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查文件引用失败: {filePath}, {ex.Message}");
                return true; // 出错时保守处理，假设被引用
            }
        }

        /// <summary>
        /// 获取文件的所有引用位置
        /// </summary>
        public List<string> GetFileReferences(string fileName)
        {
            var references = new List<string>();

            try
            {
                var allAttachmentDirs = GetAllAttachmentDirectories();

                foreach (var attachmentDir in allAttachmentDirs)
                {
                    var filePath = Path.Combine(attachmentDir, fileName);
                    if (File.Exists(filePath))
                    {
                        references.Add(filePath);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取文件引用失败: {fileName}, {ex.Message}");
            }

            return references;
        }

        /// <summary>
        /// 安全删除文件 - 只有当文件不被其他节点引用时才删除
        /// </summary>
        public bool SafeDeleteFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    Debug.WriteLine($"文件不存在，删除成功: {filePath}");
                    return true;
                }

                if (IsFileReferencedByOtherNodes(filePath))
                {
                    Debug.WriteLine($"文件被其他节点引用，跳过删除: {filePath}");
                    return true; // 被引用时不删除，但返回成功
                }

                // 确保文件不是只读的
                File.SetAttributes(filePath, FileAttributes.Normal);
                File.Delete(filePath);

                bool deleted = !File.Exists(filePath);
                Debug.WriteLine($"文件删除{(deleted ? "成功" : "失败")}: {filePath}");
                return deleted;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"安全删除文件失败: {filePath}, {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 清理节点的所有附件文件
        /// </summary>
        public void CleanupNodeAttachments(string nodePath)
        {
            try
            {
                var attachmentPath = Path.Combine(nodePath, _attachmentsFolderName);
                if (!Directory.Exists(attachmentPath))
                    return;

                Debug.WriteLine($"开始清理节点附件: {attachmentPath}");

                var files = Directory.GetFiles(attachmentPath);
                foreach (var file in files)
                {
                    SafeDeleteFile(file);
                }

                // 如果附件文件夹为空，删除文件夹
                if (Directory.Exists(attachmentPath) && !Directory.EnumerateFileSystemEntries(attachmentPath).Any())
                {
                    Directory.Delete(attachmentPath);
                    Debug.WriteLine($"已删除空的附件文件夹: {attachmentPath}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"清理节点附件失败: {nodePath}, {ex.Message}");
            }
        }

        /// <summary>
        /// 获取所有附件目录
        /// </summary>
        private List<string> GetAllAttachmentDirectories()
        {
            var attachmentDirs = new List<string>();

            try
            {
                if (Directory.Exists(_saveRootPath))
                {
                    attachmentDirs = Directory.GetDirectories(_saveRootPath, _attachmentsFolderName, SearchOption.AllDirectories)
                        .ToList();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取附件目录失败: {ex.Message}");
            }

            return attachmentDirs;
        }

        /// <summary>
        /// 智能复制文件到节点 - 避免重复复制
        /// </summary>
        public string SmartCopyFileToNode(string sourceFilePath, string targetNodePath)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                {
                    Debug.WriteLine($"源文件不存在: {sourceFilePath}");
                    return string.Empty;
                }

                var fileName = Path.GetFileName(sourceFilePath);
                var targetAttachmentDir = Path.Combine(targetNodePath, _attachmentsFolderName);
                var targetFilePath = Path.Combine(targetAttachmentDir, fileName);

                // 如果目标文件已存在且内容相同，直接返回现有路径
                if (File.Exists(targetFilePath) && AreFilesIdentical(sourceFilePath, targetFilePath))
                {
                    Debug.WriteLine($"文件已存在且内容相同，跳过复制: {targetFilePath}");
                    return targetFilePath;
                }

                // 确保目标目录存在
                if (!Directory.Exists(targetAttachmentDir))
                {
                    Directory.CreateDirectory(targetAttachmentDir);
                }

                // 如果文件名冲突但内容不同，生成新文件名
                if (File.Exists(targetFilePath))
                {
                    targetFilePath = GenerateUniqueFilePath(targetAttachmentDir, fileName);
                }

                // 复制文件
                File.Copy(sourceFilePath, targetFilePath, true);
                Debug.WriteLine($"文件复制成功: {sourceFilePath} -> {targetFilePath}");
                return targetFilePath;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"智能复制文件失败: {sourceFilePath}, {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查两个文件是否相同（通过文件大小和修改时间）
        /// </summary>
        private bool AreFilesIdentical(string file1, string file2)
        {
            try
            {
                if (!File.Exists(file1) || !File.Exists(file2))
                    return false;

                var info1 = new FileInfo(file1);
                var info2 = new FileInfo(file2);

                // 先比较文件大小
                if (info1.Length != info2.Length)
                    return false;

                // 如果大小相同，比较文件内容的哈希值（对于小文件）
                if (info1.Length < 1024 * 1024) // 小于1MB的文件直接比较内容
                {
                    return File.ReadAllBytes(file1).SequenceEqual(File.ReadAllBytes(file2));
                }

                // 大文件只比较大小和修改时间
                return Math.Abs((info1.LastWriteTime - info2.LastWriteTime).TotalSeconds) < 2;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"比较文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成唯一的文件路径
        /// </summary>
        private string GenerateUniqueFilePath(string directory, string fileName)
        {
            var nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
            var extension = Path.GetExtension(fileName);
            var counter = 1;
            string uniquePath;

            do
            {
                var uniqueFileName = $"{nameWithoutExt}_{counter}{extension}";
                uniquePath = Path.Combine(directory, uniqueFileName);
                counter++;
            } while (File.Exists(uniquePath));

            return uniquePath;
        }

        /// <summary>
        /// 单例实例
        /// </summary>
        public static FileReferenceManager Instance { get; } = new FileReferenceManager();
    }
}
