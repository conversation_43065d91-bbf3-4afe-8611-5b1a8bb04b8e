using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace 像素喵笔记
{
    /// <summary>
    /// 浮动卡片控件 - 类似便签纸的体验
    /// </summary>
    public class FloatingCard : FloatingElementBase
    {
        private RichTextBox _contentEditor = new RichTextBox();
        private Border _dragHandle = new Border(); // 拖动区域

        /// <summary>
        /// 获取内容编辑器
        /// </summary>
        public RichTextBox ContentEditor => _contentEditor;

        public FloatingCard()
        {
            // 设置初始尺寸
            Width = 200;
            Height = 150;

            // 创建内容区域
            CreateContent();
        }

        /// <summary>
        /// 创建内容区域
        /// </summary>
        private void CreateContent()
        {
            var grid = new Grid();

            // 创建可视化拖动手柄
            _dragHandle = new Border
            {
                Width = 24,
                Height = 8,
                Background = new SolidColorBrush(Color.FromRgb(189, 193, 198)), // #BDC1C6
                CornerRadius = new CornerRadius(4),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Top,
                Margin = new Thickness(0, 6, 0, 0),
                Cursor = Cursors.SizeAll
            };
            grid.Children.Add(_dragHandle);

            // 内容编辑器
            _contentEditor = new RichTextBox
            {
                Background = Brushes.Transparent,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(8),
                Margin = new Thickness(0, 20, 22, 0), // 为拖动手柄和右侧按钮留出空间，滚动条更靠近按钮
                FontFamily = new FontFamily("Segoe UI"),
                FontSize = 13,
                Foreground = new SolidColorBrush(Color.FromRgb(32, 33, 36)),
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Hidden,
                AcceptsReturn = true,
                AcceptsTab = true
            };
            grid.Children.Add(_contentEditor);

            // 设置初始内容
            var document = new FlowDocument();
            var paragraph = new Paragraph(new Run("点击这里开始编辑..."))
            {
                Margin = new Thickness(0),
                FontSize = 13,
                Foreground = new SolidColorBrush(Color.FromRgb(128, 128, 128))
            };
            document.Blocks.Add(paragraph);
            _contentEditor.Document = document;

            // 焦点事件
            _contentEditor.GotFocus += (s, e) =>
            {
                if (_contentEditor.Document.Blocks.Count == 1 &&
                    _contentEditor.Document.Blocks.FirstBlock is Paragraph p &&
                    p.Inlines.Count == 1 &&
                    p.Inlines.FirstInline is Run r &&
                    r.Text == "点击这里开始编辑...")
                {
                    _contentEditor.Document.Blocks.Clear();
                    _contentEditor.Document.Blocks.Add(new Paragraph());
                }
            };

            // 创建通用手柄
            CreateCommonHandles(grid);

            Child = grid;
        }



        /// <summary>
        /// 子类实现特定的鼠标按下处理
        /// </summary>
        protected override void HandleSpecificMouseDown(Point position, MouseButtonEventArgs e)
        {
            // 检查是否点击了拖动区域
            if (IsPointInDragHandle(position))
            {
                _isDragging = true;
                _lastPosition = e.GetPosition((UIElement)Parent);
                CaptureMouse();
                e.Handled = true;
                return;
            }

            // 检查是否点击了内容编辑器
            if (IsPointInContentEditor(position))
            {
                _contentEditor.Focus();
                return;
            }
        }



        /// <summary>
        /// 显示右键菜单
        /// </summary>
        protected override void ShowContextMenu()
        {
            var contextMenu = new ContextMenu();
            contextMenu.Style = (Style)FindResource("FigmaContextMenuStyle");

            // 插入图片
            var insertImageItem = CreateMenuItem("插入图片", "🖼️", InsertImage);
            contextMenu.Items.Add(insertImageItem);

            // 插入视频
            var insertVideoItem = CreateMenuItem("插入视频", "🎥", InsertVideo);
            contextMenu.Items.Add(insertVideoItem);

            contextMenu.Items.Add(new Separator { Style = (Style)FindResource("FigmaSeparatorStyle") });

            // 置于顶层
            var bringToFrontItem = CreateMenuItem("置于顶层", "⬆️", BringToFront);
            contextMenu.Items.Add(bringToFrontItem);

            // 卡片设置
            var settingsItem = CreateMenuItem("卡片设置", "⚙️", ShowSettings);
            contextMenu.Items.Add(settingsItem);

            // 删除卡片
            var deleteItem = CreateMenuItem("删除卡片", "🗑️", () => DeleteElement());
            contextMenu.Items.Add(deleteItem);

            contextMenu.PlacementTarget = this;
            contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint;
            contextMenu.IsOpen = true;
        }

        // 辅助方法

        private bool IsPointInDragHandle(Point point)
        {
            // 考虑Padding的影响，拖动手柄在内容区域内
            var paddingLeft = Padding.Left;
            var paddingTop = Padding.Top;

            // 拖动手柄位置：中心位置，宽24高8，顶部margin 6
            var contentWidth = ActualWidth - Padding.Left - Padding.Right;
            var handleCenterX = paddingLeft + contentWidth / 2;
            var handleTop = paddingTop + 6;
            var handleBottom = handleTop + 8;
            var handleLeft = handleCenterX - 12;
            var handleRight = handleCenterX + 12;

            return point.X >= handleLeft && point.X <= handleRight &&
                   point.Y >= handleTop && point.Y <= handleBottom;
        }

        private bool IsPointInContentEditor(Point point)
        {
            // 考虑Padding的影响，内容编辑器在拖动手柄下方
            var paddingLeft = Padding.Left;
            var paddingTop = Padding.Top;
            var paddingRight = Padding.Right;
            var paddingBottom = Padding.Bottom;

            return point.X >= paddingLeft + 8 && point.X <= ActualWidth - paddingRight - 8 &&
                   point.Y >= paddingTop + 20 && point.Y <= ActualHeight - paddingBottom - 8;
        }



        /// <summary>
        /// 显示颜色选择面板
        /// </summary>
        protected override void ShowColorSelectionPanel()
        {
            // 创建胶囊形状的颜色预设面板
            var colorPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = Brushes.White,
                Margin = new Thickness(12, 8, 12, 8)
            };

            // 胶囊形状容器 - 使用统一的标准阴影
            var capsuleBorder = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(232, 234, 237)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(20), // 胶囊形状
                Child = colorPanel,
                Effect = new DropShadowEffect
                {
                    Color = Colors.Black,
                    Direction = 270,
                    ShadowDepth = 1,
                    Opacity = 0.08,
                    BlurRadius = 4,
                    RenderingBias = RenderingBias.Quality
                }
            };

            // 创建外层容器，为阴影留出空间（减少内边距，因为阴影更小了）
            var shadowContainer = new Border
            {
                Background = Brushes.Transparent,
                Padding = new Thickness(8), // 为标准阴影留出足够空间
                Child = capsuleBorder
            };

            // 预制颜色选项（4个淡彩色 + 1个淡灰色 + 1个自定义）
            var figmaColors = new[]
            {
                Color.FromRgb(255, 242, 242), // 淡红色
                Color.FromRgb(240, 253, 244), // 淡绿色
                Color.FromRgb(240, 249, 255), // 淡蓝色
                Color.FromRgb(255, 251, 235), // 淡黄色
                Color.FromRgb(248, 249, 250), // 淡灰色
            };

            foreach (var color in figmaColors)
            {
                var colorButton = new Ellipse
                {
                    Width = 24,
                    Height = 24,
                    Fill = new SolidColorBrush(color),
                    Stroke = new SolidColorBrush(Color.FromRgb(232, 234, 237)),
                    StrokeThickness = 1,
                    Margin = new Thickness(4),
                    Cursor = Cursors.Hand
                };
                colorButton.MouseLeftButtonUp += (s, args) => ApplyBackgroundColor(color);
                colorPanel.Children.Add(colorButton);
            }

            // 自定义颜色按钮
            var customColorButton = new Border
            {
                Width = 24,
                Height = 24,
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(232, 234, 237)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(12),
                Margin = new Thickness(4),
                Cursor = Cursors.Hand,
                Child = new TextBlock
                {
                    Text = "⚙",
                    FontSize = 12,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    Foreground = new SolidColorBrush(Color.FromRgb(128, 128, 128))
                }
            };
            customColorButton.MouseLeftButtonUp += OnCustomColorClick;
            colorPanel.Children.Add(customColorButton);

            // 设置初始透明度为0，准备淡入动画
            shadowContainer.Opacity = 0;

            // 创建Popup显示颜色面板
            var popup = new System.Windows.Controls.Primitives.Popup
            {
                Child = shadowContainer, // 使用shadowContainer而不是capsuleBorder
                PlacementTarget = _colorButton,
                Placement = System.Windows.Controls.Primitives.PlacementMode.Bottom,
                StaysOpen = false,
                AllowsTransparency = true, // 允许透明度，确保阴影正确显示
                IsOpen = true
            };

            // 添加淡入动画效果
            var fadeInAnimation = new System.Windows.Media.Animation.DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(200), // 200ms淡入动画
                EasingFunction = new System.Windows.Media.Animation.QuadraticEase
                {
                    EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut
                }
            };

            shadowContainer.BeginAnimation(UIElement.OpacityProperty, fadeInAnimation);
        }

        /// <summary>
        /// 自定义颜色按钮点击事件
        /// </summary>
        private void OnCustomColorClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                e.Handled = true;
                var colorPicker = new ColorPickerDialog();
                colorPicker.Owner = Application.Current.MainWindow;

                if (colorPicker.ShowDialog() == true)
                {
                    ApplyBackgroundColor(colorPicker.SelectedColor);
                }
            }
            catch (Exception ex)
            {
                // 静默处理错误，不显示弹窗
                System.Diagnostics.Debug.WriteLine($"颜色选择器错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用背景颜色
        /// </summary>
        private void ApplyBackgroundColor(Color color)
        {
            Background = new SolidColorBrush(color);

            // 根据背景颜色自动调整文字颜色
            var textColor = GetContrastColor(color);
            UpdateTextColor(textColor);
        }

        /// <summary>
        /// 获取对比色
        /// </summary>
        private Color GetContrastColor(Color backgroundColor)
        {
            // 计算亮度
            var brightness = (backgroundColor.R * 0.299 + backgroundColor.G * 0.587 + backgroundColor.B * 0.114) / 255;

            // 亮度大于0.5使用深色文字，否则使用白色文字
            return brightness > 0.5 ? Color.FromRgb(32, 33, 36) : Colors.White;
        }

        /// <summary>
        /// 更新文字颜色
        /// </summary>
        private void UpdateTextColor(Color textColor)
        {
            var textBrush = new SolidColorBrush(textColor);
            _contentEditor.Foreground = textBrush;

            // 更新文档中所有文本的颜色
            var textRange = new TextRange(_contentEditor.Document.ContentStart, _contentEditor.Document.ContentEnd);
            textRange.ApplyPropertyValue(TextElement.ForegroundProperty, textBrush);
        }

        // 菜单操作方法

        /// <summary>
        /// 插入图片
        /// </summary>
        private void InsertImage()
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "选择图片",
                    Filter = "图片文件|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff|所有文件|*.*",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var image = CreateResizableImage(openFileDialog.FileName);

                    // 将图片插入到RichTextBox中
                    var paragraph = new Paragraph();
                    var container = new InlineUIContainer(image);
                    paragraph.Inlines.Add(container);
                    _contentEditor.Document.Blocks.Add(paragraph);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入图片失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 插入视频
        /// </summary>
        private void InsertVideo()
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "选择视频",
                    Filter = "视频文件|*.mp4;*.avi;*.wmv;*.mov;*.mkv;*.flv|所有文件|*.*",
                    Multiselect = false
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    // 创建视频播放器容器
                    var videoContainer = CreateVideoPlayer(openFileDialog.FileName);

                    // 将视频播放器插入到RichTextBox中
                    var paragraph = new Paragraph();
                    var container = new InlineUIContainer(videoContainer);
                    paragraph.Inlines.Add(container);
                    _contentEditor.Document.Blocks.Add(paragraph);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入视频失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建Figma风格可调整大小的图片
        /// </summary>
        private Grid CreateResizableImage(string imagePath)
        {
            var mainGrid = new Grid
            {
                Width = 200,
                Height = 150,
                Margin = new Thickness(0, 8, 0, 8)
            };

            var container = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(232, 234, 237)),
                BorderThickness = new Thickness(0),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(4),
                Effect = new DropShadowEffect
                {
                    Color = Colors.Black,
                    Direction = 270,
                    ShadowDepth = 2,
                    Opacity = 0.1,
                    BlurRadius = 8
                }
            };

            var image = new Image
            {
                Stretch = Stretch.Uniform,
                VerticalAlignment = VerticalAlignment.Center,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // 安全加载图片
            try
            {
                image.Source = new BitmapImage(new Uri(imagePath));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"图片加载失败: {ex.Message}");
                var errorText = new TextBlock
                {
                    Text = "图片加载失败",
                    Foreground = new SolidColorBrush(Color.FromRgb(234, 67, 53)),
                    FontSize = 12,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };
                container.Child = errorText;
                mainGrid.Children.Add(container);
                return mainGrid;
            }

            container.Child = image;
            mainGrid.Children.Add(container);

            // 创建右下角拖动缩放区域
            var resizeHandle = new Border
            {
                Width = 16,
                Height = 16,
                Background = new SolidColorBrush(Color.FromRgb(26, 115, 232)),
                CornerRadius = new CornerRadius(2),
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Bottom,
                Margin = new Thickness(0, 0, 2, 2),
                Cursor = Cursors.SizeNWSE,
                Opacity = 0
            };

            // 添加拖动手柄图标
            var handleIcon = new TextBlock
            {
                Text = "⋮⋮",
                FontSize = 8,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontWeight = FontWeights.Bold
            };
            resizeHandle.Child = handleIcon;
            mainGrid.Children.Add(resizeHandle);

            // 添加拖动缩放功能
            bool isResizing = false;
            Point resizeStartPoint;
            Size originalSize;

            // 悬停显示拖动手柄
            mainGrid.MouseEnter += (s, e) =>
            {
                container.BorderThickness = new Thickness(2);
                container.BorderBrush = new SolidColorBrush(Color.FromRgb(26, 115, 232));
                resizeHandle.Opacity = 1;
                container.Effect = new DropShadowEffect
                {
                    Color = Colors.Black,
                    Direction = 270,
                    ShadowDepth = 4,
                    Opacity = 0.15,
                    BlurRadius = 12
                };
            };

            mainGrid.MouseLeave += (s, e) =>
            {
                if (!isResizing)
                {
                    container.BorderThickness = new Thickness(0);
                    container.BorderBrush = new SolidColorBrush(Color.FromRgb(232, 234, 237));
                    resizeHandle.Opacity = 0;
                    container.Effect = new DropShadowEffect
                    {
                        Color = Colors.Black,
                        Direction = 270,
                        ShadowDepth = 2,
                        Opacity = 0.1,
                        BlurRadius = 8
                    };
                }
            };

            // 拖动手柄事件 - 修复事件绑定
            resizeHandle.MouseLeftButtonDown += (s, e) =>
            {
                isResizing = true;
                resizeStartPoint = e.GetPosition(mainGrid);
                originalSize = new Size(mainGrid.Width, mainGrid.Height);
                resizeHandle.CaptureMouse();
                e.Handled = true;
                System.Diagnostics.Debug.WriteLine("开始拖动调整大小");
            };

            // 使用mainGrid的MouseMove事件而不是resizeHandle的
            mainGrid.MouseMove += (s, e) =>
            {
                if (isResizing)
                {
                    var currentPoint = e.GetPosition(mainGrid);
                    var deltaX = currentPoint.X - resizeStartPoint.X;
                    var deltaY = currentPoint.Y - resizeStartPoint.Y;

                    var newWidth = Math.Max(100, originalSize.Width + deltaX);
                    var newHeight = Math.Max(80, originalSize.Height + deltaY);

                    // 保持宽高比
                    var aspectRatio = originalSize.Width / originalSize.Height;
                    if (Math.Abs(deltaX) > Math.Abs(deltaY))
                    {
                        newHeight = newWidth / aspectRatio;
                    }
                    else
                    {
                        newWidth = newHeight * aspectRatio;
                    }

                    mainGrid.Width = newWidth;
                    mainGrid.Height = newHeight;
                }
            };

            mainGrid.MouseLeftButtonUp += (s, e) =>
            {
                if (isResizing)
                {
                    isResizing = false;
                    resizeHandle.ReleaseMouseCapture();
                    System.Diagnostics.Debug.WriteLine("结束拖动调整大小");
                }
            };

            return mainGrid;
        }

        /// <summary>
        /// 创建Figma风格视频播放器
        /// </summary>
        private Border CreateVideoPlayer(string videoPath)
        {
            // 主容器
            var container = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(232, 234, 237)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Width = 280,
                Height = 180,
                Margin = new Thickness(0, 8, 0, 8),
                Effect = new DropShadowEffect
                {
                    Color = Colors.Black,
                    Direction = 270,
                    ShadowDepth = 4,
                    Opacity = 0.15,
                    BlurRadius = 16
                }
            };

            var mainGrid = new Grid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(40) });

            // 视频显示区域
            var videoArea = new Grid
            {
                Background = Brushes.Black,
                Margin = new Thickness(4, 4, 4, 0)
            };
            Grid.SetRow(videoArea, 0);

            // 视频元素
            var mediaElement = new MediaElement
            {
                LoadedBehavior = MediaState.Manual,
                UnloadedBehavior = MediaState.Manual,
                Stretch = Stretch.Uniform,
                Volume = 0.5
            };

            // 状态管理
            bool isPlaying = false;
            bool isLoaded = false;

            // 加载指示器
            var loadingIndicator = new TextBlock
            {
                Text = "加载中...",
                Foreground = Brushes.White,
                FontSize = 12,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Background = new SolidColorBrush(Color.FromArgb(150, 0, 0, 0)),
                Padding = new Thickness(8, 4, 8, 4)
            };

            videoArea.Children.Add(mediaElement);
            videoArea.Children.Add(loadingIndicator);
            mainGrid.Children.Add(videoArea);

            // 控制面板
            var controlPanel = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(232, 234, 237)),
                BorderThickness = new Thickness(0, 1, 0, 0),
                Height = 40,
                Margin = new Thickness(0)
            };
            Grid.SetRow(controlPanel, 1);

            var controlGrid = new Grid();
            controlGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(50) });
            controlGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(50) });
            controlGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // 播放按钮
            var playButton = new Button
            {
                Content = "▶",
                Width = 36,
                Height = 28,
                Background = new SolidColorBrush(Color.FromRgb(26, 115, 232)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(8, 6, 4, 6),
                Cursor = Cursors.Hand
            };

            // 停止按钮
            var stopButton = new Button
            {
                Content = "⏹",
                Width = 36,
                Height = 28,
                Background = new SolidColorBrush(Color.FromRgb(95, 99, 104)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontSize = 10,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(4, 6, 8, 6),
                Cursor = Cursors.Hand
            };

            // 时间显示
            var timeDisplay = new TextBlock
            {
                Text = "00:00 / 00:00",
                Foreground = new SolidColorBrush(Color.FromRgb(95, 99, 104)),
                FontSize = 11,
                FontFamily = new FontFamily("Segoe UI"),
                FontWeight = FontWeights.Medium,
                VerticalAlignment = VerticalAlignment.Center,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(8, 0, 8, 0)
            };

            Grid.SetColumn(playButton, 0);
            Grid.SetColumn(stopButton, 1);
            Grid.SetColumn(timeDisplay, 2);

            controlGrid.Children.Add(playButton);
            controlGrid.Children.Add(stopButton);
            controlGrid.Children.Add(timeDisplay);
            controlPanel.Child = controlGrid;
            mainGrid.Children.Add(controlPanel);

            // 时间更新定时器
            var timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(1000)
            };

            timer.Tick += (s, e) =>
            {
                if (isLoaded && mediaElement.NaturalDuration.HasTimeSpan)
                {
                    var position = mediaElement.Position;
                    var duration = mediaElement.NaturalDuration.TimeSpan;
                    timeDisplay.Text = $"{position:mm\\:ss} / {duration:mm\\:ss}";
                }
            };

            // 延迟加载视频 - 确保UI完全构建后再加载
            container.Loaded += (s, e) =>
            {
                try
                {
                    if (!System.IO.File.Exists(videoPath))
                    {
                        throw new System.IO.FileNotFoundException("视频文件不存在");
                    }

                    System.Diagnostics.Debug.WriteLine($"开始加载视频: {videoPath}");
                    mediaElement.Source = new Uri(videoPath, UriKind.Absolute);

                    // 强制触发加载
                    mediaElement.LoadedBehavior = MediaState.Manual;
                    mediaElement.UnloadedBehavior = MediaState.Manual;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"视频加载失败: {ex.Message}");
                    loadingIndicator.Text = "加载失败";
                    loadingIndicator.Foreground = new SolidColorBrush(Color.FromRgb(234, 67, 53));
                }
            };

            // 媒体事件 - 增强调试信息
            mediaElement.MediaOpened += (s, e) =>
            {
                System.Diagnostics.Debug.WriteLine("视频MediaOpened事件触发");
                isLoaded = true;
                loadingIndicator.Visibility = Visibility.Collapsed;
                if (mediaElement.NaturalDuration.HasTimeSpan)
                {
                    var duration = mediaElement.NaturalDuration.TimeSpan;
                    timeDisplay.Text = $"00:00 / {duration:mm\\:ss}";
                    System.Diagnostics.Debug.WriteLine($"视频时长: {duration}");
                }
                timer.Start();
                System.Diagnostics.Debug.WriteLine("视频加载完成，可以播放");
            };

            mediaElement.MediaFailed += (s, e) =>
            {
                System.Diagnostics.Debug.WriteLine($"视频加载失败: {e.ErrorException?.Message}");
                loadingIndicator.Text = "加载失败";
                loadingIndicator.Foreground = new SolidColorBrush(Color.FromRgb(234, 67, 53));
            };

            mediaElement.MediaEnded += (s, e) =>
            {
                isPlaying = false;
                playButton.Content = "▶";
                mediaElement.Position = TimeSpan.Zero;
                timer.Stop();
            };

            // 播放按钮事件 - 增强调试
            playButton.Click += (s, e) =>
            {
                System.Diagnostics.Debug.WriteLine($"播放按钮点击，isLoaded: {isLoaded}, isPlaying: {isPlaying}");

                if (!isLoaded)
                {
                    System.Diagnostics.Debug.WriteLine("视频未加载完成，无法播放");
                    return;
                }

                if (isPlaying)
                {
                    mediaElement.Pause();
                    playButton.Content = "▶";
                    isPlaying = false;
                    timer.Stop();
                    System.Diagnostics.Debug.WriteLine("视频暂停");
                }
                else
                {
                    mediaElement.Play();
                    playButton.Content = "⏸";
                    isPlaying = true;
                    timer.Start();
                    System.Diagnostics.Debug.WriteLine("视频播放");
                }
            };

            // 停止按钮事件
            stopButton.Click += (s, e) =>
            {
                if (!isLoaded) return;

                mediaElement.Stop();
                playButton.Content = "▶";
                isPlaying = false;
                mediaElement.Position = TimeSpan.Zero;
                timer.Stop();
                if (mediaElement.NaturalDuration.HasTimeSpan)
                {
                    var duration = mediaElement.NaturalDuration.TimeSpan;
                    timeDisplay.Text = $"00:00 / {duration:mm\\:ss}";
                }
            };

            // 按钮悬停效果
            playButton.MouseEnter += (s, e) => playButton.Background = new SolidColorBrush(Color.FromRgb(21, 87, 176));
            playButton.MouseLeave += (s, e) => playButton.Background = new SolidColorBrush(Color.FromRgb(26, 115, 232));
            stopButton.MouseEnter += (s, e) => stopButton.Background = new SolidColorBrush(Color.FromRgb(75, 79, 84));
            stopButton.MouseLeave += (s, e) => stopButton.Background = new SolidColorBrush(Color.FromRgb(95, 99, 104));

            container.Child = mainGrid;
            return container;
        }

        /// <summary>
        /// 显示卡片设置对话框
        /// </summary>
        private void ShowSettings()
        {
            try
            {
                var settingsDialog = new CardSettingsDialog(this);
                settingsDialog.Owner = Application.Current.MainWindow;
                settingsDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示卡片设置失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 清理事件处理器和资源
        /// </summary>
        public void Cleanup()
        {
            try
            {
                // 清理鼠标事件
                MouseLeftButtonDown -= OnMouseLeftButtonDown;
                MouseMove -= OnMouseMove;
                MouseLeftButtonUp -= OnMouseLeftButtonUp;
                MouseRightButtonUp -= OnMouseRightButtonUp;
                MouseEnter -= OnMouseEnter;
                MouseLeave -= OnMouseLeave;

                // 清理颜色按钮事件
                if (_colorButton != null)
                {
                    _colorButton.MouseLeftButtonUp -= OnColorButtonClick;
                }

                // 清理内容编辑器
                if (_contentEditor != null)
                {
                    // 如果有文本变化事件，在这里清理
                    // _contentEditor.TextChanged -= OnContentChanged;
                }
            }
            catch (Exception ex)
            {
                // 静默处理清理过程中的错误
                System.Diagnostics.Debug.WriteLine($"FloatingCard清理时发生错误: {ex.Message}");
            }
        }

    }
}
