using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Effects;
using System.Windows.Shapes;

namespace 像素喵笔记
{
    /// <summary>
    /// 浮动元素基类 - 提供通用的拖拽、缩放、旋转功能
    /// </summary>
    public abstract class FloatingElementBase : Border
    {
        // 共同的状态字段
        protected bool _isDragging = false;
        protected Point _lastPosition;
        protected bool _isResizing = false;
        protected Point _resizeStartPoint;
        protected Size _resizeStartSize;
        protected bool _isRotating = false;
        protected Point _rotateCenter;
        protected double _initialAngle;

        // 双击检测相关字段
        private DateTime _lastRotateHandleClickTime = DateTime.MinValue;
        private const int DoubleClickInterval = 300; // 双击间隔时间（毫秒）

        // 共同的UI元素
        protected Ellipse _resizeHandle = null!;
        protected Ellipse _rotateHandle = null!;
        protected Ellipse _colorButton = null!;
        protected RotateTransform _rotateTransform = null!;
        protected Canvas? _parentCanvas;

        protected FloatingElementBase()
        {
            InitializeBaseElement();
        }

        /// <summary>
        /// 初始化基础元素
        /// </summary>
        private void InitializeBaseElement()
        {
            // 基础样式
            Background = Brushes.White;
            BorderBrush = new SolidColorBrush(Color.FromRgb(232, 234, 237)); // #E8EAED
            BorderThickness = new Thickness(1);
            CornerRadius = new CornerRadius(12);
            MinWidth = 100;
            MinHeight = 80;
            Padding = new Thickness(16);

            // 阴影效果 - 使用统一的标准阴影
            Effect = new DropShadowEffect
            {
                Color = Colors.Black,
                Direction = 270,
                ShadowDepth = 1,
                Opacity = 0.08,
                BlurRadius = 4
            };

            // 变换组
            var transformGroup = new TransformGroup();
            _rotateTransform = new RotateTransform();
            transformGroup.Children.Add(_rotateTransform);
            RenderTransform = transformGroup;
            RenderTransformOrigin = new Point(0.5, 0.5);

            // 绑定事件
            MouseLeftButtonDown += OnMouseLeftButtonDown;
            MouseMove += OnMouseMove;
            MouseLeftButtonUp += OnMouseLeftButtonUp;
            MouseRightButtonUp += OnMouseRightButtonUp;
            MouseEnter += OnMouseEnter;
            MouseLeave += OnMouseLeave;

            // 设置层级
            Panel.SetZIndex(this, 1000);

            // 监听父容器变化
            Loaded += OnLoaded;
        }

        /// <summary>
        /// 控件加载完成事件
        /// </summary>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            _parentCanvas = Parent as Canvas;
        }

        /// <summary>
        /// 创建通用的手柄元素
        /// </summary>
        protected void CreateCommonHandles(Grid grid)
        {
            // 调整大小手柄
            _resizeHandle = new Ellipse
            {
                Width = 16,
                Height = 16,
                Fill = new SolidColorBrush(Color.FromRgb(10, 150, 255)), // #0A96FF 蓝色
                Stroke = Brushes.White,
                StrokeThickness = 2,
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Bottom,
                Margin = new Thickness(0, 0, -8, -8),
                Cursor = Cursors.SizeNWSE,
                Visibility = Visibility.Hidden
            };
            grid.Children.Add(_resizeHandle);

            // 旋转手柄
            _rotateHandle = new Ellipse
            {
                Width = 16,
                Height = 16,
                Fill = new SolidColorBrush(Color.FromRgb(255, 193, 7)), // 黄色
                Stroke = Brushes.White,
                StrokeThickness = 2,
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Top,
                Margin = new Thickness(0, -8, -8, 0),
                Cursor = Cursors.Hand,
                Visibility = Visibility.Hidden
            };
            grid.Children.Add(_rotateHandle);

            // 颜色选择按钮
            _colorButton = new Ellipse
            {
                Width = 16,
                Height = 16,
                Fill = new SolidColorBrush(Color.FromRgb(220, 53, 69)), // 红色
                Stroke = Brushes.White,
                StrokeThickness = 2,
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Top,
                Margin = new Thickness(0, 12, -8, 0), // 在旋转按钮下方20像素
                Cursor = Cursors.Hand,
                Visibility = Visibility.Hidden
            };
            _colorButton.MouseLeftButtonUp += OnColorButtonClick;
            grid.Children.Add(_colorButton);
        }

        /// <summary>
        /// 鼠标进入事件
        /// </summary>
        protected virtual void OnMouseEnter(object sender, MouseEventArgs e)
        {
            _resizeHandle.Visibility = Visibility.Visible;
            _rotateHandle.Visibility = Visibility.Visible;
            _colorButton.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 鼠标离开事件
        /// </summary>
        protected virtual void OnMouseLeave(object sender, MouseEventArgs e)
        {
            if (!_isDragging && !_isResizing && !_isRotating)
            {
                _resizeHandle.Visibility = Visibility.Hidden;
                _rotateHandle.Visibility = Visibility.Hidden;
                _colorButton.Visibility = Visibility.Hidden;
            }
        }

        /// <summary>
        /// 鼠标左键按下事件
        /// </summary>
        protected virtual void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            var position = e.GetPosition(this);

            // 检查是否点击了旋转手柄
            if (IsPointInRotateHandle(position))
            {
                // 检查是否为双击
                var currentTime = DateTime.Now;
                var timeSinceLastClick = _lastRotateHandleClickTime == DateTime.MinValue ?
                    double.MaxValue : (currentTime - _lastRotateHandleClickTime).TotalMilliseconds;

                if (timeSinceLastClick <= DoubleClickInterval && _lastRotateHandleClickTime != DateTime.MinValue)
                {
                    // 双击 - 执行旋转复位
                    ResetRotation();
                    // 重要：重置时间戳，避免影响下次双击检测
                    _lastRotateHandleClickTime = DateTime.MinValue;
                    e.Handled = true;
                    System.Diagnostics.Debug.WriteLine($"双击复位触发，时间间隔: {timeSinceLastClick}ms");
                    return;
                }

                // 单击 - 开始旋转操作
                // 记录点击时间
                _lastRotateHandleClickTime = currentTime;
                System.Diagnostics.Debug.WriteLine($"单击旋转手柄，记录时间: {currentTime:HH:mm:ss.fff}");

                // 停止任何正在运行的旋转动画
                _rotateTransform.BeginAnimation(RotateTransform.AngleProperty, null);

                _isRotating = true;
                _rotateCenter = new Point(ActualWidth / 2, ActualHeight / 2);
                var parentPosition = e.GetPosition((UIElement)Parent);
                var vector = parentPosition - TransformToParent(_rotateCenter);
                _initialAngle = Math.Atan2(vector.Y, vector.X) * 180 / Math.PI;
                CaptureMouse();
                e.Handled = true;
                return;
            }

            // 检查是否点击了调整大小手柄
            if (IsPointInResizeHandle(position))
            {
                _isResizing = true;
                _resizeStartPoint = e.GetPosition((UIElement)Parent);
                _resizeStartSize = new Size(ActualWidth, ActualHeight);
                CaptureMouse();
                e.Handled = true;
                return;
            }

            // 子类处理特定的点击逻辑
            HandleSpecificMouseDown(position, e);
        }

        /// <summary>
        /// 子类实现特定的鼠标按下处理
        /// </summary>
        protected abstract void HandleSpecificMouseDown(Point position, MouseButtonEventArgs e);

        /// <summary>
        /// 鼠标移动事件
        /// </summary>
        protected virtual void OnMouseMove(object sender, MouseEventArgs e)
        {
            if (_isRotating)
            {
                HandleRotation(e);
                return;
            }

            if (_isResizing)
            {
                HandleResizing(e);
                return;
            }

            if (_isDragging)
            {
                HandleDragging(e);
            }
        }

        /// <summary>
        /// 处理旋转
        /// </summary>
        protected virtual void HandleRotation(MouseEventArgs e)
        {
            var parentPosition = e.GetPosition((UIElement)Parent);
            var centerInParent = TransformToParent(_rotateCenter);
            var vector = parentPosition - centerInParent;
            var currentAngle = Math.Atan2(vector.Y, vector.X) * 180 / Math.PI;

            // 使用绝对角度，避免累积误差
            var deltaAngle = currentAngle - _initialAngle;

            // 限制角度变化范围，避免大幅跳跃
            while (deltaAngle > 180) deltaAngle -= 360;
            while (deltaAngle < -180) deltaAngle += 360;

            _rotateTransform.Angle += deltaAngle;
            _initialAngle = currentAngle;
        }

        /// <summary>
        /// 处理缩放
        /// </summary>
        protected virtual void HandleResizing(MouseEventArgs e)
        {
            var currentPosition = e.GetPosition((UIElement)Parent);
            var deltaX = currentPosition.X - _resizeStartPoint.X;
            var deltaY = currentPosition.Y - _resizeStartPoint.Y;

            var newWidth = Math.Max(MinWidth, _resizeStartSize.Width + deltaX);
            var newHeight = Math.Max(MinHeight, _resizeStartSize.Height + deltaY);

            // 检查调整大小后是否会超出边界（考虑滚动条）
            if (_parentCanvas != null)
            {
                var currentLeft = Canvas.GetLeft(this);
                var currentTop = Canvas.GetTop(this);

                if (double.IsNaN(currentLeft)) currentLeft = 0;
                if (double.IsNaN(currentTop)) currentTop = 0;

                // 为滚动条预留空间
                var scrollBarWidth = 20;
                var scrollBarHeight = 20;

                // 限制宽度不超出右边界（避免遮挡滚动条）
                var maxWidth = _parentCanvas.ActualWidth - currentLeft - scrollBarWidth;
                if (maxWidth > 0)
                    newWidth = Math.Min(newWidth, maxWidth);

                // 限制高度不超出下边界（避免遮挡滚动条）
                var maxHeight = _parentCanvas.ActualHeight - currentTop - scrollBarHeight;
                if (maxHeight > 0)
                    newHeight = Math.Min(newHeight, maxHeight);
            }

            Width = newWidth;
            Height = newHeight;

            // 强制更新布局
            InvalidateVisual();
            UpdateLayout();
        }

        /// <summary>
        /// 处理拖拽
        /// </summary>
        protected virtual void HandleDragging(MouseEventArgs e)
        {
            var currentPosition = e.GetPosition((UIElement)Parent);
            var deltaX = currentPosition.X - _lastPosition.X;
            var deltaY = currentPosition.Y - _lastPosition.Y;

            var currentLeft = Canvas.GetLeft(this);
            var currentTop = Canvas.GetTop(this);

            // 处理NaN值
            if (double.IsNaN(currentLeft)) currentLeft = 0;
            if (double.IsNaN(currentTop)) currentTop = 0;

            // 计算新位置
            var newLeft = currentLeft + deltaX;
            var newTop = currentTop + deltaY;

            // 应用边界限制
            var constrainedPosition = ConstrainToBounds(newLeft, newTop);

            Canvas.SetLeft(this, constrainedPosition.X);
            Canvas.SetTop(this, constrainedPosition.Y);

            _lastPosition = currentPosition;
        }

        /// <summary>
        /// 鼠标左键释放事件
        /// </summary>
        protected virtual void OnMouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isDragging || _isResizing || _isRotating)
            {
                _isDragging = false;
                _isResizing = false;
                _isRotating = false;
                ReleaseMouseCapture();
                e.Handled = true;
            }
        }

        /// <summary>
        /// 鼠标右键释放事件
        /// </summary>
        protected virtual void OnMouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            ShowContextMenu();
            e.Handled = true;
        }

        /// <summary>
        /// 显示右键菜单 - 子类实现
        /// </summary>
        protected abstract void ShowContextMenu();

        /// <summary>
        /// 颜色选择按钮点击事件
        /// </summary>
        protected virtual void OnColorButtonClick(object sender, MouseButtonEventArgs e)
        {
            e.Handled = true;
            ShowColorSelectionPanel();
        }

        /// <summary>
        /// 显示颜色选择面板 - 子类实现
        /// </summary>
        protected abstract void ShowColorSelectionPanel();

        // 辅助方法
        protected bool IsPointInRotateHandle(Point point) =>
            point.X >= ActualWidth - 24 && point.X <= ActualWidth + 8 &&
            point.Y >= -8 && point.Y <= 24;

        protected bool IsPointInResizeHandle(Point point) =>
            point.X >= ActualWidth - 30 && point.X <= ActualWidth + 10 &&
            point.Y >= ActualHeight - 30 && point.Y <= ActualHeight + 10;

        /// <summary>
        /// 将本地坐标转换为父容器坐标
        /// </summary>
        protected Point TransformToParent(Point localPoint)
        {
            if (Parent is UIElement parent)
            {
                return TransformToAncestor(parent).Transform(localPoint);
            }
            return localPoint;
        }

        /// <summary>
        /// 约束元素位置在Canvas边界内，避免遮挡滚动条
        /// </summary>
        protected Point ConstrainToBounds(double left, double top)
        {
            if (_parentCanvas == null)
                return new Point(left, top);

            // 获取Canvas的实际尺寸
            var canvasWidth = _parentCanvas.ActualWidth;
            var canvasHeight = _parentCanvas.ActualHeight;

            // 如果Canvas尺寸为0，不进行约束
            if (canvasWidth <= 0 || canvasHeight <= 0)
                return new Point(left, top);

            // 为滚动条预留空间（右侧和底部各预留20像素）
            var scrollBarWidth = 20;
            var scrollBarHeight = 20;

            // 约束左边界（不能超出左边）
            var constrainedLeft = Math.Max(0, left);

            // 约束右边界（不能遮挡右侧滚动条）
            constrainedLeft = Math.Min(constrainedLeft, canvasWidth - ActualWidth - scrollBarWidth);

            // 约束上边界（不能超出上边）
            var constrainedTop = Math.Max(0, top);

            // 约束下边界（不能遮挡底部滚动条）
            constrainedTop = Math.Min(constrainedTop, canvasHeight - ActualHeight - scrollBarHeight);

            return new Point(constrainedLeft, constrainedTop);
        }

        /// <summary>
        /// 创建菜单项的通用方法
        /// </summary>
        protected MenuItem CreateMenuItem(string text, string icon, Action action)
        {
            var menuItem = new MenuItem();
            menuItem.Style = (Style)FindResource("FigmaMenuItemStyle");

            var stackPanel = new StackPanel { Orientation = Orientation.Horizontal };
            stackPanel.Children.Add(new TextBlock { Text = icon, FontSize = 14, Margin = new Thickness(0, 0, 8, 0), Width = 20 });
            stackPanel.Children.Add(new TextBlock { Text = text, FontSize = 13 });

            menuItem.Header = stackPanel;
            menuItem.Click += (s, e) => action?.Invoke();

            return menuItem;
        }

        /// <summary>
        /// 置于顶层的通用方法
        /// </summary>
        protected void BringToFront()
        {
            if (Parent is Panel parentPanel)
            {
                var maxZIndex = 1000;
                foreach (UIElement child in parentPanel.Children)
                {
                    var zIndex = Panel.GetZIndex(child);
                    if (zIndex > maxZIndex)
                        maxZIndex = zIndex;
                }
                Panel.SetZIndex(this, maxZIndex + 1);
            }
        }

        /// <summary>
        /// 删除元素的通用方法
        /// </summary>
        protected void DeleteElement()
        {
            if (Parent is Panel parentPanel)
            {
                parentPanel.Children.Remove(this);
            }
        }

        /// <summary>
        /// 旋转复位方法 - 双击黄色按钮时调用
        /// </summary>
        protected void ResetRotation()
        {
            try
            {
                var currentAngle = _rotateTransform.Angle;
                System.Diagnostics.Debug.WriteLine($"开始旋转复位：当前角度 {currentAngle:F1}° → 0°");

                // 创建旋转复位动画
                var rotationAnimation = new System.Windows.Media.Animation.DoubleAnimation
                {
                    From = currentAngle, // 从当前角度开始
                    To = 0, // 目标角度为0度
                    Duration = TimeSpan.FromMilliseconds(300), // 300ms动画时长
                    EasingFunction = new System.Windows.Media.Animation.QuadraticEase
                    {
                        EasingMode = System.Windows.Media.Animation.EasingMode.EaseOut
                    }
                };

                // 添加动画完成事件
                rotationAnimation.Completed += (s, e) =>
                {
                    System.Diagnostics.Debug.WriteLine($"旋转复位动画完成，最终角度: {_rotateTransform.Angle:F1}°");
                };

                // 应用动画到旋转变换
                _rotateTransform.BeginAnimation(RotateTransform.AngleProperty, rotationAnimation);

                System.Diagnostics.Debug.WriteLine("旋转复位动画已启动");
            }
            catch (Exception ex)
            {
                // 如果动画失败，直接设置角度为0
                _rotateTransform.Angle = 0;
                System.Diagnostics.Debug.WriteLine($"旋转复位动画失败，直接复位: {ex.Message}");
            }
        }
    }
}
