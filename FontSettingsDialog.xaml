<Window x:Class="像素喵笔记.FontSettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:像素喵笔记"
        mc:Ignorable="d"
        Title="字体设置" Height="748" Width="950"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        RenderOptions.BitmapScalingMode="HighQuality">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <!-- 主容器，带圆角和阴影 -->
    <Border Background="{DynamicResource AppCardBackgroundBrush}" CornerRadius="12" Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.15" BlurRadius="16"/>
        </Border.Effect>

        <Grid Margin="24">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="字体设置"
                          FontSize="20"
                          FontWeight="SemiBold"
                          Foreground="{DynamicResource AppForegroundBrush}"
                          HorizontalAlignment="Left"
                          VerticalAlignment="Center"/>
                <Button x:Name="btnClose" Content="✕"
                       HorizontalAlignment="Right" Click="btnCancel_Click"
                       Style="{StaticResource FigmaDialogCloseButtonStyle}"/>
            </Grid>

            <!-- 上部分：基本设置、段落设置和颜色设置 -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 基本设置卡片 -->
                <Border Grid.Column="0" Style="{StaticResource FigmaCardStyle}">
                    <StackPanel>
                        <TextBlock Text="基本设置" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,16"/>

                        <!-- 字体和字号并排 -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="字体" Style="{StaticResource FigmaLabelStyle}"/>
                                <ComboBox x:Name="cmbFontFamily" Style="{StaticResource FigmaComboBoxStyle}" IsEditable="False"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Text="字号" Style="{StaticResource FigmaLabelStyle}"/>
                                <ComboBox x:Name="cmbFontSize" Style="{StaticResource FigmaComboBoxStyle}" IsEditable="True" Width="100">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Content}" Margin="4,0"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                    <ComboBoxItem Content="8"/>
                                    <ComboBoxItem Content="9"/>
                                    <ComboBoxItem Content="10"/>
                                    <ComboBoxItem Content="11"/>
                                    <ComboBoxItem Content="12"/>
                                    <ComboBoxItem Content="14"/>
                                    <ComboBoxItem Content="16"/>
                                    <ComboBoxItem Content="18"/>
                                    <ComboBoxItem Content="20"/>
                                    <ComboBoxItem Content="22"/>
                                    <ComboBoxItem Content="24"/>
                                    <ComboBoxItem Content="26"/>
                                    <ComboBoxItem Content="28"/>
                                    <ComboBoxItem Content="36"/>
                                    <ComboBoxItem Content="48"/>
                                    <ComboBoxItem Content="72"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>

                        <!-- 基础字体样式 - 2行2列布局 -->
                        <StackPanel>
                            <TextBlock Text="基础样式" Style="{StaticResource FigmaLabelStyle}"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <CheckBox x:Name="chkBold" Content="粗体" Grid.Row="0" Grid.Column="0" Margin="0,0,12,8" VerticalAlignment="Center" Style="{StaticResource FigmaCheckBoxStyle}"/>
                                <CheckBox x:Name="chkItalic" Content="斜体" Grid.Row="0" Grid.Column="1" Margin="0,0,0,8" VerticalAlignment="Center" Style="{StaticResource FigmaCheckBoxStyle}"/>
                                <CheckBox x:Name="chkUnderline" Content="下划线" Grid.Row="1" Grid.Column="0" Margin="0,0,12,0" VerticalAlignment="Center" Style="{StaticResource FigmaCheckBoxStyle}"/>
                                <CheckBox x:Name="chkStrikethrough" Content="删除线" Grid.Row="1" Grid.Column="1" VerticalAlignment="Center" Style="{StaticResource FigmaCheckBoxStyle}"/>
                            </Grid>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 段落设置卡片 -->
                <Border Grid.Column="2" Style="{StaticResource FigmaCardStyle}">
                    <StackPanel>
                        <TextBlock Text="段落设置" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,16"/>

                        <!-- 行距 -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="行距" Style="{StaticResource FigmaLabelStyle}"/>
                            <ComboBox x:Name="cmbLineSpacing" Style="{StaticResource FigmaComboBoxStyle}">
                                <ComboBoxItem Content="单倍"/>
                                <ComboBoxItem Content="1.5倍"/>
                                <ComboBoxItem Content="双倍"/>
                            </ComboBox>
                        </StackPanel>

                        <!-- 段前段后 -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="12"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="段前间距" Style="{StaticResource FigmaLabelStyle}"/>
                                <ComboBox x:Name="cmbParagraphBefore" Style="{StaticResource FigmaComboBoxStyle}">
                                    <ComboBoxItem Content="0"/>
                                    <ComboBoxItem Content="3"/>
                                    <ComboBoxItem Content="6"/>
                                    <ComboBoxItem Content="12"/>
                                    <ComboBoxItem Content="18"/>
                                    <ComboBoxItem Content="24"/>
                                </ComboBox>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Text="段后间距" Style="{StaticResource FigmaLabelStyle}"/>
                                <ComboBox x:Name="cmbParagraphAfter" Style="{StaticResource FigmaComboBoxStyle}">
                                    <ComboBoxItem Content="0"/>
                                    <ComboBoxItem Content="3"/>
                                    <ComboBoxItem Content="6"/>
                                    <ComboBoxItem Content="12"/>
                                    <ComboBoxItem Content="18"/>
                                    <ComboBoxItem Content="24"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>

                        <!-- 对齐方式 - 4个并排 -->
                        <StackPanel>
                            <TextBlock Text="对齐方式" Style="{StaticResource FigmaLabelStyle}"/>
                            <StackPanel Orientation="Horizontal">
                                <RadioButton x:Name="rbLeft" Content="左对齐" Margin="0,0,12,0" VerticalAlignment="Center" IsChecked="True" Style="{StaticResource FigmaRadioButtonStyle}"/>
                                <RadioButton x:Name="rbCenter" Content="居中" Margin="0,0,12,0" VerticalAlignment="Center" Style="{StaticResource FigmaRadioButtonStyle}"/>
                                <RadioButton x:Name="rbRight" Content="右对齐" Margin="0,0,12,0" VerticalAlignment="Center" Style="{StaticResource FigmaRadioButtonStyle}"/>
                                <RadioButton x:Name="rbJustify" Content="分散对齐" VerticalAlignment="Center" Style="{StaticResource FigmaRadioButtonStyle}"/>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- 颜色设置卡片 -->
                <Border Grid.Column="4" Style="{StaticResource FigmaCardStyle}">
                    <StackPanel>
                        <TextBlock Text="颜色设置" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,16"/>

                        <!-- 文字颜色 -->
                        <StackPanel Margin="0,0,0,16">
                            <TextBlock Text="文字颜色" Style="{StaticResource FigmaLabelStyle}"/>
                            <Border Background="{DynamicResource AppCardBackgroundBrush}"
                                   BorderBrush="{DynamicResource AppBorderBrush}"
                                   BorderThickness="1"
                                   CornerRadius="6"
                                   Height="36"
                                   Cursor="Hand"
                                   MouseLeftButtonDown="rectTextColor_Click">
                                <Grid>
                                    <Rectangle x:Name="rectTextColor"
                                             Fill="Black"
                                             Margin="8"
                                             RadiusX="3"
                                             RadiusY="3"/>
                                    <TextBlock Text="点击选择"
                                             HorizontalAlignment="Center"
                                             VerticalAlignment="Center"
                                             FontSize="11"
                                             Foreground="{DynamicResource AppSecondaryTextBrush}"
                                             IsHitTestVisible="False"/>
                                </Grid>
                            </Border>
                        </StackPanel>

                        <!-- 背景颜色 -->
                        <StackPanel>
                            <TextBlock Text="背景颜色" Style="{StaticResource FigmaLabelStyle}"/>
                            <Border Background="{DynamicResource AppCardBackgroundBrush}"
                                   BorderBrush="{DynamicResource AppBorderBrush}"
                                   BorderThickness="1"
                                   CornerRadius="6"
                                   Height="36"
                                   Cursor="Hand"
                                   MouseLeftButtonDown="rectBackColor_Click">
                                <Grid>
                                    <Rectangle x:Name="rectBackColor"
                                             Fill="White"
                                             Margin="8"
                                             RadiusX="3"
                                             RadiusY="3"/>
                                    <TextBlock Text="点击选择"
                                             HorizontalAlignment="Center"
                                             VerticalAlignment="Center"
                                             FontSize="11"
                                             Foreground="{DynamicResource AppSecondaryTextBrush}"
                                             IsHitTestVisible="False"/>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- 下部分：预览效果 - 横着占满整个界面 -->
            <Border Grid.Row="2" Style="{StaticResource FigmaCardStyle}" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="预览效果" Style="{StaticResource FigmaLabelStyle}" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,16"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="12"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 左侧预览 -->
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="原始文本" Style="{StaticResource FigmaLabelStyle}" FontSize="12" Margin="0,0,0,8"/>
                            <Border Background="{DynamicResource AppCardBackgroundBrush}"
                                   BorderBrush="{DynamicResource AppBorderBrush}"
                                   BorderThickness="1"
                                   CornerRadius="6"
                                   Padding="12"
                                   MinHeight="150">
                                <TextBox x:Name="txtOriginalPreview"
                                        Text="这是第一行预览文本。&#x0a;这是第二行预览文本，用于展示段前段后间距效果。&#x0a;这是第三行预览文本。"
                                        IsReadOnly="True"
                                        TextWrapping="Wrap"
                                        VerticalScrollBarVisibility="Auto"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        FontSize="12"/>
                            </Border>
                        </StackPanel>

                        <!-- 右侧预览 -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="应用效果" Style="{StaticResource FigmaLabelStyle}" FontSize="12" Margin="0,0,0,8"/>
                            <Border Background="{DynamicResource AppCardBackgroundBrush}"
                                   BorderBrush="{DynamicResource AppBorderBrush}"
                                   BorderThickness="1"
                                   CornerRadius="6"
                                   Padding="12"
                                   MinHeight="150">
                                <TextBox x:Name="txtPreview"
                                        Text="这是第一行预览文本。&#x0a;这是第二行预览文本，用于展示段前段后间距效果。&#x0a;这是第三行预览文本。"
                                        IsReadOnly="True"
                                        TextWrapping="Wrap"
                                        VerticalScrollBarVisibility="Auto"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        FontSize="12"/>
                            </Border>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                <Button x:Name="btnCancel" Content="取消" Style="{StaticResource FigmaDialogCancelButtonStyle}" Click="btnCancel_Click"/>
                <Button x:Name="btnApply" Content="确定" Style="{StaticResource FigmaDialogConfirmButtonStyle}" Click="btnApply_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>