using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using Microsoft.Win32;

namespace 像素喵笔记
{
    /// <summary>
    /// FontSettingsDialog.xaml 的交互逻辑
    /// </summary>
    public partial class FontSettingsDialog : Window
    {
        public FontSettings? SelectedFontSettings { get; private set; }

        public FontSettingsDialog()
        {
            InitializeComponent();
            InitializeControls();
            LoadCurrentSettings();
        }

        public FontSettingsDialog(FontSettings currentSettings) : this()
        {
            if (currentSettings != null)
            {
                ApplySettings(currentSettings);
            }
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitializeControls()
        {
            // 初始化字体列表
            var fontFamilies = Fonts.SystemFontFamilies.OrderBy(f => f.Source).ToList();
            foreach (var fontFamily in fontFamilies)
            {
                cmbFontFamily.Items.Add(fontFamily.Source);
            }

            // 设置默认值
            cmbFontFamily.SelectedItem = "Microsoft YaHei UI";
            cmbFontSize.SelectedIndex = 4; // 默认选择12号字体
            cmbLineSpacing.SelectedIndex = 0; // 单倍行距
            cmbParagraphBefore.SelectedIndex = 0; // 段前间距0
            cmbParagraphAfter.SelectedIndex = 0; // 段后间距0

            // 绑定事件
            cmbFontFamily.SelectionChanged += OnFontSettingChanged;
            cmbFontSize.SelectionChanged += OnFontSettingChanged;
            cmbLineSpacing.SelectionChanged += OnFontSettingChanged;
            cmbParagraphBefore.SelectionChanged += OnFontSettingChanged;
            cmbParagraphAfter.SelectionChanged += OnFontSettingChanged;

            chkBold.Checked += OnFontSettingChanged;
            chkBold.Unchecked += OnFontSettingChanged;
            chkItalic.Checked += OnFontSettingChanged;
            chkItalic.Unchecked += OnFontSettingChanged;
            chkUnderline.Checked += OnFontSettingChanged;
            chkUnderline.Unchecked += OnFontSettingChanged;
            chkStrikethrough.Checked += OnFontSettingChanged;
            chkStrikethrough.Unchecked += OnFontSettingChanged;
            rbLeft.Checked += OnFontSettingChanged;
            rbCenter.Checked += OnFontSettingChanged;
            rbRight.Checked += OnFontSettingChanged;
            rbJustify.Checked += OnFontSettingChanged;
        }

        /// <summary>
        /// 加载当前设置
        /// </summary>
        private void LoadCurrentSettings()
        {
            UpdatePreview();
        }

        /// <summary>
        /// 应用设置
        /// </summary>
        private void ApplySettings(FontSettings settings)
        {
            cmbFontFamily.SelectedItem = settings.FontFamily;

            // 修复字号显示问题 - 确保ComboBox显示正确的字号
            foreach (ComboBoxItem item in cmbFontSize.Items)
            {
                if (item.Content.ToString() == settings.FontSize.ToString())
                {
                    cmbFontSize.SelectedItem = item;
                    break;
                }
            }
            // 如果没有匹配的预设字号，直接设置文本
            if (cmbFontSize.SelectedItem == null)
            {
                cmbFontSize.Text = settings.FontSize.ToString();
            }

            chkBold.IsChecked = settings.IsBold;
            chkItalic.IsChecked = settings.IsItalic;
            chkUnderline.IsChecked = settings.IsUnderline;
            chkStrikethrough.IsChecked = settings.IsStrikethrough;
            rectTextColor.Fill = new SolidColorBrush(settings.TextColor);
            rectBackColor.Fill = new SolidColorBrush(settings.BackgroundColor);

            // 设置对齐方式
            switch (settings.TextAlignment)
            {
                case TextAlignment.Left:
                    rbLeft.IsChecked = true;
                    break;
                case TextAlignment.Center:
                    rbCenter.IsChecked = true;
                    break;
                case TextAlignment.Right:
                    rbRight.IsChecked = true;
                    break;
                case TextAlignment.Justify:
                    rbJustify.IsChecked = true;
                    break;
            }

            // 设置行距
            int lineSpacingIndex = 0; // 默认单倍行距
            if (settings.LineHeight == 1.5)
                lineSpacingIndex = 1;
            else if (settings.LineHeight >= 2.0)
                lineSpacingIndex = 2;
            cmbLineSpacing.SelectedIndex = lineSpacingIndex;

            // 设置段前段后间距
            for (int i = 0; i < cmbParagraphBefore.Items.Count; i++)
            {
                var item = cmbParagraphBefore.Items[i] as ComboBoxItem;
                if (item != null && item.Content.ToString() == settings.ParagraphSpacingBefore.ToString())
                {
                    cmbParagraphBefore.SelectedIndex = i;
                    break;
                }
            }

            for (int i = 0; i < cmbParagraphAfter.Items.Count; i++)
            {
                var item = cmbParagraphAfter.Items[i] as ComboBoxItem;
                if (item != null && item.Content.ToString() == settings.ParagraphSpacingAfter.ToString())
                {
                    cmbParagraphAfter.SelectedIndex = i;
                    break;
                }
            }

            UpdatePreview();
        }

        /// <summary>
        /// 字体设置改变事件
        /// </summary>
        private void OnFontSettingChanged(object sender, RoutedEventArgs e)
        {
            UpdatePreview();
        }

        /// <summary>
        /// 更新预览
        /// </summary>
        private void UpdatePreview()
        {
            if (txtPreview == null) return;

            try
            {
                // 设置字体
                if (cmbFontFamily.SelectedItem != null)
                {
                    txtPreview.FontFamily = new FontFamily(cmbFontFamily.SelectedItem.ToString());
                }

                // 设置字号 - 修复字号获取逻辑
                double fontSize = 12; // 默认字号
                if (cmbFontSize.SelectedItem is ComboBoxItem sizeItem && double.TryParse(sizeItem.Content.ToString(), out double parsedSize))
                {
                    fontSize = parsedSize;
                }
                else if (cmbFontSize.Text != null && double.TryParse(cmbFontSize.Text, out double textSize))
                {
                    fontSize = textSize; // 支持手动输入的字号
                }

                // 设置字号
                txtPreview.FontSize = fontSize;

                // 设置字体样式
                txtPreview.FontWeight = chkBold.IsChecked == true ? FontWeights.Bold : FontWeights.Normal;
                txtPreview.FontStyle = chkItalic.IsChecked == true ? FontStyles.Italic : FontStyles.Normal;

                // 设置文本装饰
                var decorations = new TextDecorationCollection();
                if (chkUnderline.IsChecked == true)
                {
                    decorations.Add(TextDecorations.Underline);
                }
                if (chkStrikethrough.IsChecked == true)
                {
                    decorations.Add(TextDecorations.Strikethrough);
                }
                txtPreview.TextDecorations = decorations;

                // 设置对齐方式
                if (rbLeft.IsChecked == true)
                    txtPreview.TextAlignment = TextAlignment.Left;
                else if (rbCenter.IsChecked == true)
                    txtPreview.TextAlignment = TextAlignment.Center;
                else if (rbRight.IsChecked == true)
                    txtPreview.TextAlignment = TextAlignment.Right;
                else if (rbJustify.IsChecked == true)
                    txtPreview.TextAlignment = TextAlignment.Justify;

                // 准备预览文本
                string previewText = GetPreviewTextWithEffects();
                txtPreview.Text = previewText;

                // 设置段前段后间距
                int paragraphBefore = GetSelectedValue(cmbParagraphBefore);
                int paragraphAfter = GetSelectedValue(cmbParagraphAfter);

                // 设置段前段后间距
                txtPreview.Padding = new Thickness(0, paragraphBefore, 0, paragraphAfter);

                // 设置颜色
                txtPreview.Foreground = rectTextColor.Fill;
                txtPreview.Background = rectBackColor.Fill;
            }
            catch (Exception ex)
            {
                // 忽略预览更新错误
                System.Diagnostics.Debug.WriteLine($"预览更新错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取带特殊效果的预览文本 - 修改为三行文字
        /// </summary>
        private string GetPreviewTextWithEffects()
        {
            // 获取当前选择的字体
            string selectedFont = cmbFontFamily.SelectedItem?.ToString() ?? "微软雅黑";

            // 检查是否为特殊符号字体
            if (IsSymbolFont(selectedFont))
            {
                return GetSymbolPreviewText(selectedFont);
            }
            else
            {
                // 普通字体使用中文预览
                string line1 = "这是第一行预览文本。";
                string line2 = "这是第二行预览文本，用于展示段前段后间距效果。";
                string line3 = "这是第三行预览文本。";
                return line1 + "\n" + line2 + "\n" + line3;
            }
        }

        /// <summary>
        /// 判断是否为符号字体
        /// </summary>
        private bool IsSymbolFont(string fontName)
        {
            var symbolFonts = new[]
            {
                "Webdings", "Wingdings", "Wingdings 2", "Wingdings 3",
                "Symbol", "Marlett", "MS Outlook", "MT Extra"
            };

            return symbolFonts.Any(sf => fontName.Contains(sf, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取符号字体的预览文本
        /// </summary>
        private string GetSymbolPreviewText(string fontName)
        {
            return fontName.ToLower() switch
            {
                var name when name.Contains("webdings") => "abcdefghijk\nlmnopqrstuv\nwxyz123456",
                var name when name.Contains("wingdings") => "abcdefghijk\nlmnopqrstuv\nwxyz123456",
                var name when name.Contains("symbol") => "abcdefghijk\nlmnopqrstuv\nwxyz123456",
                _ => "ABCDEFGHIJK\nLMNOPQRSTUV\nWXYZ1234567"
            };
        }

        /// <summary>
        /// 获取项目符号
        /// </summary>
        private string GetBulletSymbol(int index)
        {
            return index switch
            {
                1 => "●",
                2 => "○",
                3 => "■",
                4 => "□",
                5 => "♦",
                6 => "►",
                7 => "✓",
                _ => ""
            };
        }

        /// <summary>
        /// 获取编号符号
        /// </summary>
        private string GetNumberingSymbol(int index)
        {
            return index switch
            {
                1 => "1.",
                2 => "1)",
                3 => "(1)",
                4 => "①",
                5 => "A.",
                6 => "a.",
                7 => "I.",
                8 => "i.",
                _ => ""
            };
        }

        /// <summary>
        /// 获取ComboBox选中的数值
        /// </summary>
        private int GetSelectedValue(ComboBox comboBox)
        {
            if (comboBox.SelectedItem is ComboBoxItem item && int.TryParse(item.Content.ToString(), out int value))
            {
                return value;
            }
            return 0;
        }



        /// <summary>
        /// 选择文字颜色
        /// </summary>
        private void rectTextColor_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var colorPicker = new ColorPickerDialog();
                var currentColor = ((SolidColorBrush)rectTextColor.Fill).Color;
                colorPicker.SelectedColor = currentColor;
                colorPicker.Owner = this;

                if (colorPicker.ShowDialog() == true)
                {
                    rectTextColor.Fill = new SolidColorBrush(colorPicker.SelectedColor);
                    UpdatePreview();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择文字颜色时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 选择背景颜色
        /// </summary>
        private void rectBackColor_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var colorPicker = new ColorPickerDialog();
                var currentColor = ((SolidColorBrush)rectBackColor.Fill).Color;
                colorPicker.SelectedColor = currentColor;
                colorPicker.Owner = this;

                if (colorPicker.ShowDialog() == true)
                {
                    rectBackColor.Fill = new SolidColorBrush(colorPicker.SelectedColor);
                    UpdatePreview();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择背景颜色时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 应用按钮点击事件
        /// </summary>
        private void btnApply_Click(object sender, RoutedEventArgs e)
        {
            SelectedFontSettings = GetCurrentSettings();
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 获取当前设置 - 修复字号获取逻辑
        /// </summary>
        private FontSettings GetCurrentSettings()
        {
            var settings = new FontSettings();

            settings.FontFamily = cmbFontFamily.SelectedItem?.ToString() ?? "Microsoft YaHei UI";

            // 修复字号获取逻辑
            if (cmbFontSize.SelectedItem is ComboBoxItem sizeItem && double.TryParse(sizeItem.Content.ToString(), out double fontSize))
            {
                settings.FontSize = fontSize;
            }
            else if (cmbFontSize.Text != null && double.TryParse(cmbFontSize.Text, out double textSize))
            {
                settings.FontSize = textSize; // 支持手动输入的字号
            }
            else
            {
                settings.FontSize = 12;
            }

            settings.IsBold = chkBold.IsChecked == true;
            settings.IsItalic = chkItalic.IsChecked == true;
            settings.IsUnderline = chkUnderline.IsChecked == true;
            settings.IsStrikethrough = chkStrikethrough.IsChecked == true;

            settings.TextColor = ((SolidColorBrush)rectTextColor.Fill).Color;
            settings.BackgroundColor = ((SolidColorBrush)rectBackColor.Fill).Color;

            // 获取对齐方式
            if (rbLeft.IsChecked == true)
                settings.TextAlignment = TextAlignment.Left;
            else if (rbCenter.IsChecked == true)
                settings.TextAlignment = TextAlignment.Center;
            else if (rbRight.IsChecked == true)
                settings.TextAlignment = TextAlignment.Right;
            else if (rbJustify.IsChecked == true)
                settings.TextAlignment = TextAlignment.Justify;

            // 获取行距设置
            switch (cmbLineSpacing.SelectedIndex)
            {
                case 0:
                    settings.LineHeight = 1.0; // 单倍行距
                    break;
                case 1:
                    settings.LineHeight = 1.5; // 1.5倍行距
                    break;
                case 2:
                    settings.LineHeight = 2.0; // 双倍行距
                    break;
                default:
                    settings.LineHeight = 1.0; // 默认单倍行距
                    break;
            }

            // 获取段前段后间距
            if (cmbParagraphBefore.SelectedItem != null && int.TryParse(((ComboBoxItem)cmbParagraphBefore.SelectedItem).Content.ToString(), out int paragraphBefore))
            {
                settings.ParagraphSpacingBefore = paragraphBefore;
            }

            if (cmbParagraphAfter.SelectedItem != null && int.TryParse(((ComboBoxItem)cmbParagraphAfter.SelectedItem).Content.ToString(), out int paragraphAfter))
            {
                settings.ParagraphSpacingAfter = paragraphAfter;
            }

            return settings;
        }


    }

    /// <summary>
    /// 字体设置类
    /// </summary>
    public class FontSettings
    {
        public string FontFamily { get; set; } = "Microsoft YaHei UI";
        public double FontSize { get; set; } = 12;
        public bool IsBold { get; set; } = false;
        public bool IsItalic { get; set; } = false;
        public bool IsUnderline { get; set; } = false;
        public bool IsStrikethrough { get; set; } = false;
        public Color TextColor { get; set; } = Colors.Black;
        public Color BackgroundColor { get; set; } = Colors.White;
        public TextAlignment TextAlignment { get; set; } = TextAlignment.Left;
        public double LineHeight { get; set; } = 1.0;
        public double IndentSize { get; set; } = 0;
        public IndentType IndentType { get; set; } = IndentType.FirstLine;
        public int IndentCharCount { get; set; } = 2;
        public int TextSpacingBefore { get; set; } = 0;
        public int TextSpacingAfter { get; set; } = 0;

        // 新增属性
        public int ParagraphSpacingBefore { get; set; } = 0;
        public int ParagraphSpacingAfter { get; set; } = 0;
        public ShadowType ShadowType { get; set; } = ShadowType.None;
        public ReflectionType ReflectionType { get; set; } = ReflectionType.None;
        public PositionType PositionType { get; set; } = PositionType.Normal;
        public BulletType BulletType { get; set; } = BulletType.None;
        public NumberingType NumberingType { get; set; } = NumberingType.None;
    }

    /// <summary>
    /// 缩进类型
    /// </summary>
    public enum IndentType
    {
        FirstLine, // 首行缩进
        Hanging    // 悬挂缩进
    }

    /// <summary>
    /// 阴影类型
    /// </summary>
    public enum ShadowType
    {
        None,      // 无阴影
        Outer,     // 外部阴影
        Inner      // 内部阴影
    }

    /// <summary>
    /// 倒影类型
    /// </summary>
    public enum ReflectionType
    {
        None,      // 无倒影
        Tight,     // 紧密倒影
        Half,      // 半倒影
        Full       // 全倒影
    }

    /// <summary>
    /// 位置类型
    /// </summary>
    public enum PositionType
    {
        Normal,    // 正常
        Superscript, // 上标
        Subscript  // 下标
    }

    /// <summary>
    /// 项目符号类型
    /// </summary>
    public enum BulletType
    {
        None,           // 无
        SolidCircle,    // ● 实心圆
        HollowCircle,   // ○ 空心圆
        SolidSquare,    // ■ 实心方
        HollowSquare,   // □ 空心方
        Diamond,        // ♦ 菱形
        Triangle,       // ► 三角
        CheckMark       // ✓ 对勾
    }

    /// <summary>
    /// 编号类型
    /// </summary>
    public enum NumberingType
    {
        None,           // 无
        Arabic,         // 1. 2. 3.
        ArabicParen,    // 1) 2) 3)
        ArabicParenBoth, // (1) (2) (3)
        CircledArabic,  // ① ② ③
        UpperAlpha,     // A. B. C.
        LowerAlpha,     // a. b. c.
        UpperRoman,     // I. II. III.
        LowerRoman      // i. ii. iii.
    }
}
