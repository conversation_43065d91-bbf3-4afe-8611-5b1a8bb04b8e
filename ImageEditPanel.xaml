<UserControl x:Class="像素喵笔记.ImageEditPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="220" d:DesignWidth="500"
             Background="Transparent">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    
    <!-- 底部滑动编辑面板 -->
    <Border x:Name="EditPanel"
            Background="#F8F9FA"
            BorderBrush="#E8EAED"
            BorderThickness="1"
            CornerRadius="12"
            Padding="24,20,24,24"
            MinWidth="480"
            MinHeight="240"
            Height="240"
            Effect="{StaticResource StandardFigmaShadow}">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="20"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="24"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="8"/>
            </Grid.RowDefinitions>
            
            <!-- 标题和关闭按钮 -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                          Text="调整图片大小" 
                          FontFamily="Microsoft YaHei" 
                          FontSize="14" 
                          FontWeight="SemiBold" 
                          Foreground="#1F2937"
                          VerticalAlignment="Center"/>
                
                <Button Grid.Column="1" 
                       x:Name="CloseButton"
                       Width="24" 
                       Height="24" 
                       Background="Transparent" 
                       BorderThickness="0"
                       Cursor="Hand"
                       Click="CloseButton_Click">
                    <TextBlock Text="✕" 
                              FontSize="12" 
                              Foreground="#6B7280" 
                              HorizontalAlignment="Center" 
                              VerticalAlignment="Center"/>
                </Button>
            </Grid>
            
            <!-- 尺寸信息显示 -->
            <Grid Grid.Row="2">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="24"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="24"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- 当前尺寸 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="当前: " 
                              FontFamily="Microsoft YaHei" 
                              FontSize="12" 
                              Foreground="#6B7280"/>
                    <TextBlock x:Name="CurrentSizeText" 
                              Text="400 × 300" 
                              FontFamily="Microsoft YaHei" 
                              FontSize="12" 
                              FontWeight="Medium" 
                              Foreground="#1F2937"/>
                </StackPanel>
                
                <!-- 原始尺寸 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="原始: "
                              FontFamily="Microsoft YaHei"
                              FontSize="12"
                              Foreground="#6B7280"/>
                    <TextBlock x:Name="OriginalSizeText"
                              Text="800 × 600"
                              FontFamily="Microsoft YaHei"
                              FontSize="12"
                              FontWeight="Medium"
                              Foreground="#6B7280"/>
                </StackPanel>

                <!-- 缩放比例 -->
                <StackPanel Grid.Column="4" Orientation="Horizontal">
                    <TextBlock Text="缩放: "
                              FontFamily="Microsoft YaHei"
                              FontSize="12"
                              Foreground="#6B7280"/>
                    <TextBlock x:Name="ScaleText"
                              Text="50%"
                              FontFamily="Microsoft YaHei"
                              FontSize="12"
                              FontWeight="Medium"
                              Foreground="#3B82F6"/>
                </StackPanel>
            </Grid>
            
            <!-- 滑动条控制 -->
            <Grid Grid.Row="4" MinHeight="40">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="12"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="12"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 最小值标签 -->
                <TextBlock Grid.Column="0"
                          Text="10%"
                          FontFamily="Microsoft YaHei"
                          FontSize="11"
                          Foreground="#9CA3AF"
                          VerticalAlignment="Center"/>

                <!-- 滑动条 -->
                <Slider Grid.Column="2"
                       x:Name="SizeSlider"
                       Minimum="10"
                       Maximum="150"
                       Value="50"
                       TickFrequency="10"
                       IsSnapToTickEnabled="False"
                       ValueChanged="SizeSlider_ValueChanged"
                       Style="{StaticResource FigmaSliderStyle}"
                       Height="24"
                       VerticalAlignment="Center"/>

                <!-- 最大值标签 -->
                <TextBlock Grid.Column="4"
                          Text="150%"
                          FontFamily="Microsoft YaHei"
                          FontSize="11"
                          Foreground="#9CA3AF"
                          VerticalAlignment="Center"/>
            </Grid>
            
        </Grid>
    </Border>
</UserControl>
