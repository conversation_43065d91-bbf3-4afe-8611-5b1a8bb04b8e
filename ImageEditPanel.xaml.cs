using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;

namespace 像素喵笔记
{
    /// <summary>
    /// ImageEditPanel.xaml 的交互逻辑
    /// 底部滑动条样式的图片编辑面板
    /// </summary>
    public partial class ImageEditPanel : UserControl
    {
        #region 私有字段

        private System.Windows.Controls.Image? _targetImage;
        private double _originalWidth;
        private double _originalHeight;
        private double _currentWidth;
        private double _currentHeight;

        #endregion

        #region 公共事件

        /// <summary>
        /// 关闭面板事件
        /// </summary>
        public event EventHandler? PanelClosed;

        /// <summary>
        /// 图片尺寸改变事件
        /// </summary>
        public event EventHandler<ImageSizeChangedEventArgs>? ImageSizeChanged;

        #endregion

        #region 构造函数

        public ImageEditPanel()
        {
            InitializeComponent();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 设置要编辑的图片
        /// </summary>
        public void SetTargetImage(System.Windows.Controls.Image image)
        {
            try
            {
                _targetImage = image;

                // 获取图片的原始尺寸
                if (image.Source is BitmapImage bitmapImage)
                {
                    _originalWidth = bitmapImage.PixelWidth;
                    _originalHeight = bitmapImage.PixelHeight;
                }
                else
                {
                    _originalWidth = image.ActualWidth > 0 ? image.ActualWidth : 400;
                    _originalHeight = image.ActualHeight > 0 ? image.ActualHeight : 300;
                }

                // 获取当前显示尺寸
                _currentWidth = double.IsNaN(image.Width) ? _originalWidth : image.Width;
                _currentHeight = double.IsNaN(image.Height) ? _originalHeight : image.Height;

                // 确保当前尺寸在合理范围内
                const double maxDisplaySize = 800;
                if (_currentWidth > maxDisplaySize || _currentHeight > maxDisplaySize)
                {
                    var scale = Math.Min(maxDisplaySize / _currentWidth, maxDisplaySize / _currentHeight);
                    _currentWidth *= scale;
                    _currentHeight *= scale;

                    // 应用限制后的尺寸到图片
                    image.Width = _currentWidth;
                    image.Height = _currentHeight;

                    System.Diagnostics.Debug.WriteLine($"图片尺寸已限制到合理范围: {_currentWidth:F0}x{_currentHeight:F0}");
                }

                // 更新界面显示
                UpdateDisplay();

                System.Diagnostics.Debug.WriteLine($"图片编辑面板已设置: 原始({_originalWidth}x{_originalHeight}) 当前({_currentWidth}x{_currentHeight})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置目标图片失败: {ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 更新显示信息
        /// </summary>
        private void UpdateDisplay()
        {
            try
            {
                // 更新尺寸显示
                CurrentSizeText.Text = $"{(int)_currentWidth} × {(int)_currentHeight}";
                OriginalSizeText.Text = $"{(int)_originalWidth} × {(int)_originalHeight}";

                // 计算缩放比例
                var scalePercent = (_currentWidth / _originalWidth) * 100;
                ScaleText.Text = $"{scalePercent:F0}%";

                // 更新滑动条值
                SizeSlider.Value = scalePercent;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用新的尺寸到图片
        /// </summary>
        private void ApplyNewSize(double scalePercent)
        {
            try
            {
                if (_targetImage == null) return;

                // 计算新尺寸
                var newWidth = _originalWidth * (scalePercent / 100.0);
                var newHeight = _originalHeight * (scalePercent / 100.0);

                // 添加合理的尺寸限制，防止图片过大或过小
                const double minSize = 20;  // 最小尺寸
                const double maxSize = 1200; // 最大尺寸

                // 限制宽度
                if (newWidth < minSize) newWidth = minSize;
                if (newWidth > maxSize) newWidth = maxSize;

                // 限制高度
                if (newHeight < minSize) newHeight = minSize;
                if (newHeight > maxSize) newHeight = maxSize;

                // 如果尺寸被限制了，重新计算缩放比例
                var actualScaleX = newWidth / _originalWidth * 100.0;
                var actualScaleY = newHeight / _originalHeight * 100.0;
                var actualScale = Math.Min(actualScaleX, actualScaleY);

                // 应用到图片
                _targetImage.Width = newWidth;
                _targetImage.Height = newHeight;

                // 更新当前尺寸
                _currentWidth = newWidth;
                _currentHeight = newHeight;

                // 更新显示
                UpdateDisplay();

                // 触发尺寸改变事件
                ImageSizeChanged?.Invoke(this, new ImageSizeChangedEventArgs
                {
                    NewWidth = newWidth,
                    NewHeight = newHeight,
                    ScalePercent = actualScale
                });

                System.Diagnostics.Debug.WriteLine($"图片尺寸已更新: {newWidth:F0}x{newHeight:F0} ({actualScale:F0}%)");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用新尺寸失败: {ex.Message}");
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 滑动条值改变事件
        /// </summary>
        private void SizeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            try
            {
                var scalePercent = e.NewValue;
                ApplyNewSize(scalePercent);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"滑动条值改变处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                PanelClosed?.Invoke(this, EventArgs.Empty);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭按钮处理失败: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 图片尺寸改变事件参数
    /// </summary>
    public class ImageSizeChangedEventArgs : EventArgs
    {
        public double NewWidth { get; set; }
        public double NewHeight { get; set; }
        public double ScalePercent { get; set; }
    }
}
