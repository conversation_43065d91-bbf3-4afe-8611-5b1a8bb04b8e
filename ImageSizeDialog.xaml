<Window x:Class="像素喵笔记.ImageSizeDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="图片尺寸设置" 
        Height="280" 
        Width="400"
        Style="{StaticResource FigmaDialogStyle}"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="调整图片尺寸" 
                   FontSize="18" 
                   FontWeight="Medium" 
                   Foreground="#202124" 
                   Margin="0,0,0,20"/>

        <!-- 设置区域 -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 宽度设置 -->
            <TextBlock Grid.Row="0" Grid.Column="0" 
                       Text="宽度:" 
                       FontSize="14" 
                       Foreground="#5F6368" 
                       VerticalAlignment="Center" 
                       Margin="0,0,12,0"/>
            
            <TextBox x:Name="widthTextBox" 
                     Grid.Row="0" Grid.Column="1"
                     Style="{StaticResource FigmaTextBoxStyle}"
                     Text="300"
                     Margin="0,0,8,0"/>
            
            <TextBlock Grid.Row="0" Grid.Column="2" 
                       Text="px" 
                       FontSize="14" 
                       Foreground="#5F6368" 
                       VerticalAlignment="Center"/>

            <!-- 高度设置 -->
            <TextBlock Grid.Row="1" Grid.Column="0" 
                       Text="高度:" 
                       FontSize="14" 
                       Foreground="#5F6368" 
                       VerticalAlignment="Center" 
                       Margin="0,12,12,0"/>
            
            <TextBox x:Name="heightTextBox" 
                     Grid.Row="1" Grid.Column="1"
                     Style="{StaticResource FigmaTextBoxStyle}"
                     Text="200"
                     Margin="0,12,8,0"/>
            
            <TextBlock Grid.Row="1" Grid.Column="2" 
                       Text="px" 
                       FontSize="14" 
                       Foreground="#5F6368" 
                       VerticalAlignment="Center"
                       Margin="0,12,0,0"/>

            <!-- 锁定比例 -->
            <CheckBox x:Name="lockAspectRatioCheckBox" 
                      Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="3"
                      Content="锁定宽高比" 
                      FontSize="14" 
                      Foreground="#5F6368" 
                      Margin="0,16,0,0"
                      IsChecked="True"
                      Checked="LockAspectRatio_Changed"
                      Unchecked="LockAspectRatio_Changed"/>

            <!-- 预设尺寸 -->
            <TextBlock Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="3"
                       Text="常用尺寸:" 
                       FontSize="14" 
                       Foreground="#5F6368" 
                       Margin="0,16,0,8"/>
            
            <StackPanel Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="3" 
                        Orientation="Horizontal" 
                        Margin="0,0,0,0">
                <Button Content="小 (200x150)" 
                        Style="{StaticResource FigmaButtonStyle}" 
                        Click="PresetSize_Click" 
                        Tag="200,150" 
                        Margin="0,0,8,0"/>
                <Button Content="中 (400x300)" 
                        Style="{StaticResource FigmaButtonStyle}" 
                        Click="PresetSize_Click" 
                        Tag="400,300" 
                        Margin="0,0,8,0"/>
                <Button Content="大 (600x450)" 
                        Style="{StaticResource FigmaButtonStyle}" 
                        Click="PresetSize_Click" 
                        Tag="600,450"/>
            </StackPanel>
        </Grid>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button x:Name="btnCancel" 
                    Content="取消" 
                    Style="{StaticResource FigmaButtonStyle}" 
                    Click="BtnCancel_Click" 
                    Margin="0,0,12,0" 
                    MinWidth="80"/>
            <Button x:Name="btnOK" 
                    Content="确定" 
                    Style="{StaticResource FigmaPrimaryButtonStyle}" 
                    Click="BtnOK_Click" 
                    MinWidth="80"/>
        </StackPanel>
    </Grid>
</Window>
