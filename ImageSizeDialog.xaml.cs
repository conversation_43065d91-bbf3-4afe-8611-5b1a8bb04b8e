using System;
using System.Windows;
using System.Windows.Controls;

namespace 像素喵笔记
{
    /// <summary>
    /// 图片尺寸设置对话框
    /// </summary>
    public partial class ImageSizeDialog : Window
    {
        #region 私有字段
        
        private double _originalAspectRatio = 1.0;
        private bool _isUpdatingSize = false;
        
        #endregion

        #region 公共属性
        
        /// <summary>
        /// 图片宽度
        /// </summary>
        public int ImageWidth { get; private set; }
        
        /// <summary>
        /// 图片高度
        /// </summary>
        public int ImageHeight { get; private set; }
        
        #endregion

        #region 构造函数
        
        public ImageSizeDialog() : this(300, 200)
        {
        }
        
        public ImageSizeDialog(int width, int height)
        {
            InitializeComponent();
            InitializeDialog(width, height);
        }
        
        #endregion

        #region 初始化方法
        
        /// <summary>
        /// 初始化对话框
        /// </summary>
        private void InitializeDialog(int width, int height)
        {
            ImageWidth = width;
            ImageHeight = height;
            
            widthTextBox.Text = width.ToString();
            heightTextBox.Text = height.ToString();
            
            // 计算宽高比
            _originalAspectRatio = (double)width / height;
            
            // 绑定文本框事件
            widthTextBox.TextChanged += WidthTextBox_TextChanged;
            heightTextBox.TextChanged += HeightTextBox_TextChanged;
            
            // 设置焦点
            widthTextBox.Focus();
            widthTextBox.SelectAll();
        }
        
        #endregion

        #region 事件处理
        
        /// <summary>
        /// 宽度文本框变化事件
        /// </summary>
        private void WidthTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingSize) return;
            
            if (lockAspectRatioCheckBox.IsChecked == true && int.TryParse(widthTextBox.Text, out int width))
            {
                _isUpdatingSize = true;
                var height = (int)(width / _originalAspectRatio);
                heightTextBox.Text = height.ToString();
                _isUpdatingSize = false;
            }
        }
        
        /// <summary>
        /// 高度文本框变化事件
        /// </summary>
        private void HeightTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingSize) return;
            
            if (lockAspectRatioCheckBox.IsChecked == true && int.TryParse(heightTextBox.Text, out int height))
            {
                _isUpdatingSize = true;
                var width = (int)(height * _originalAspectRatio);
                widthTextBox.Text = width.ToString();
                _isUpdatingSize = false;
            }
        }
        
        /// <summary>
        /// 锁定宽高比变化事件
        /// </summary>
        private void LockAspectRatio_Changed(object sender, RoutedEventArgs e)
        {
            if (lockAspectRatioCheckBox.IsChecked == true)
            {
                // 重新计算宽高比
                if (int.TryParse(widthTextBox.Text, out int width) && 
                    int.TryParse(heightTextBox.Text, out int height) && 
                    height > 0)
                {
                    _originalAspectRatio = (double)width / height;
                }
            }
        }
        
        /// <summary>
        /// 预设尺寸按钮点击事件
        /// </summary>
        private void PresetSize_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string sizeStr)
            {
                var parts = sizeStr.Split(',');
                if (parts.Length == 2 && 
                    int.TryParse(parts[0], out int width) && 
                    int.TryParse(parts[1], out int height))
                {
                    _isUpdatingSize = true;
                    widthTextBox.Text = width.ToString();
                    heightTextBox.Text = height.ToString();
                    _originalAspectRatio = (double)width / height;
                    _isUpdatingSize = false;
                }
            }
        }
        
        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 验证输入
                if (!int.TryParse(widthTextBox.Text, out int width) || width <= 0)
                {
                    MessageBox.Show("请输入有效的宽度值（大于0的整数）", "输入错误", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    widthTextBox.Focus();
                    return;
                }
                
                if (!int.TryParse(heightTextBox.Text, out int height) || height <= 0)
                {
                    MessageBox.Show("请输入有效的高度值（大于0的整数）", "输入错误", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    heightTextBox.Focus();
                    return;
                }
                
                // 检查尺寸限制
                if (width > 2000 || height > 2000)
                {
                    var result = MessageBox.Show("图片尺寸较大，可能影响性能。是否继续？", "确认", 
                                               MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result != MessageBoxResult.Yes)
                    {
                        return;
                    }
                }
                
                ImageWidth = width;
                ImageHeight = height;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置尺寸时发生错误: {ex.Message}", "错误", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
        
        #endregion

        #region 键盘事件处理
        
        /// <summary>
        /// 重写键盘事件处理
        /// </summary>
        protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Enter)
            {
                BtnOK_Click(this, new RoutedEventArgs());
                e.Handled = true;
            }
            else if (e.Key == System.Windows.Input.Key.Escape)
            {
                BtnCancel_Click(this, new RoutedEventArgs());
                e.Handled = true;
            }
            else
            {
                base.OnKeyDown(e);
            }
        }
        
        #endregion
    }
}
