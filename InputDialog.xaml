<Window x:Class="像素喵笔记.InputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="输入对话框" Height="180" Width="400"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ResizeMode="NoResize"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        RenderOptions.BitmapScalingMode="HighQuality"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <!-- 主容器 -->
    <Border Background="#F8F9FA" CornerRadius="12" Margin="15">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.15" BlurRadius="16"/>
        </Border.Effect>
        
        <Grid Margin="24">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题 -->
            <TextBlock x:Name="titleTextBlock" Grid.Row="0" 
                       Text="输入对话框" 
                       FontSize="20" 
                       FontWeight="SemiBold" 
                       Foreground="#202124"
                       FontFamily="Segoe UI"
                       Margin="0,0,0,20"/>

            <!-- 提示文本 -->
            <TextBlock x:Name="promptTextBlock" Grid.Row="1" 
                       Text="请输入内容:" 
                       Style="{StaticResource FigmaLabelStyle}"
                       Margin="0,0,0,12"/>

            <!-- 输入框 -->
            <TextBox x:Name="inputTextBox" Grid.Row="2" 
                     Style="{StaticResource FigmaTextBoxStyle}"
                     VerticalAlignment="Top"
                     Margin="0,0,0,16"/>

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="3" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right"
                        Margin="0,8,0,0">
                <Button x:Name="btnCancel" 
                        Content="取消"
                        Style="{StaticResource FigmaCancelButtonStyle}"
                        IsCancel="True"
                        Click="btnCancel_Click"/>
                
                <Button x:Name="btnOK"
                        Content="确定"
                        Style="{StaticResource FigmaDialogButtonStyle}"
                        IsDefault="True"
                        Click="btnOK_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
