using System.Windows;

namespace 像素喵笔记
{
    /// <summary>
    /// InputDialog.xaml 的交互逻辑
    /// </summary>
    public partial class InputDialog : Window
    {
        public string InputText { get; private set; } = string.Empty;

        public InputDialog()
        {
            InitializeComponent();
            Loaded += InputDialog_Loaded;
        }

        public InputDialog(string title, string prompt, string defaultText = "") : this()
        {
            titleTextBlock.Text = title;
            promptTextBlock.Text = prompt;
            inputTextBox.Text = defaultText;
            InputText = defaultText;
        }

        private void InputDialog_Loaded(object sender, RoutedEventArgs e)
        {
            // 设置焦点到输入框并选中所有文本
            inputTextBox.Focus();
            inputTextBox.SelectAll();
        }

        private void btnOK_Click(object sender, RoutedEventArgs e)
        {
            InputText = inputTextBox.Text;
            DialogResult = true;
            Close();
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
