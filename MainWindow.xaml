﻿<Window x:Class="像素喵笔记.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:像素喵笔记"
        mc:Ignorable="d"
        Title="像素喵记事本" Height="765" Width="1200"
        Icon="/Resources/F1.png"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        Closing="Window_Closing" Foreground="Red">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- Figma风格选择项样式 -->
            <Style x:Key="FigmaSelectionStyle" TargetType="ListBoxItem">
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="8,4"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ListBoxItem">
                            <Border x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="4"
                                    Padding="{TemplateBinding Padding}">
                                <ContentPresenter/>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#F1F3F4"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="#E8F0FE"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Figma风格工具提示样式 -->
            <Style TargetType="ToolTip">
                <Setter Property="Background" Value="#1E1E1E"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Padding" Value="6,4"/>
                <Setter Property="FontSize" Value="11"/>
                <Setter Property="FontFamily" Value="Segoe UI"/>
                <Setter Property="FontWeight" Value="Normal"/>
                <Setter Property="HasDropShadow" Value="False"/>
                <Setter Property="Placement" Value="Bottom"/>
                <Setter Property="HorizontalOffset" Value="0"/>
                <Setter Property="VerticalOffset" Value="8"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ToolTip">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="6"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 小三角指示器 -->
                                <Path Grid.Row="0" Data="M0,6 L6,0 L12,6 Z" Fill="#1E1E1E"
                                      HorizontalAlignment="Center" VerticalAlignment="Bottom"/>

                                <!-- 主体内容 -->
                                <Border Grid.Row="1" Background="#1E1E1E"
                                        CornerRadius="4"
                                        Padding="8,4"
                                        MinHeight="20">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1"
                                                        Opacity="0.45" BlurRadius="12"/>
                                    </Border.Effect>
                                    <ContentPresenter Content="{TemplateBinding Content}"
                                                    HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </Border>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- 应用Figma风格滚动条样式 -->
            <Style TargetType="ScrollBar" BasedOn="{StaticResource FigmaScrollBarStyle}"/>
        </ResourceDictionary>
    </Window.Resources>

    <!-- 主窗口容器，带圆角和阴影 -->
    <Border Background="{DynamicResource AppBackgroundBrush}" CornerRadius="12" Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.45" BlurRadius="12"/>
        </Border.Effect>

        <Grid x:Name="MainGrid">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 上半部分：主内容区域 -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <!-- 树状控件列：固定宽度280，可以通过Visibility控制显示/隐藏 -->
                    <ColumnDefinition x:Name="treeColumn" Width="0"/>
                    <!-- 文本编辑器列：占据剩余空间 -->
                    <ColumnDefinition x:Name="contentColumn" Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：树状控件容器 -->
                <Border x:Name="treeContainer" Grid.Column="0" Background="{DynamicResource AppCardBackgroundBrush}" Visibility="Collapsed"
                        BorderThickness="0,0,1,0" BorderBrush="#E6E6E6"
                        CornerRadius="8,0,0,8" Width="280"
                        ClipToBounds="True">
                    <!-- 移除阴影效果，避免覆盖文本编辑器 -->

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 标题区域 - 可拖动 -->
                        <Border Grid.Row="0" Background="#FAFAFA" Padding="15,10" CornerRadius="8,0,0,0"
                                MouseLeftButtonDown="TitleArea_MouseLeftButtonDown"
                                MouseMove="TitleArea_MouseMove"
                                MouseLeftButtonUp="TitleArea_MouseLeftButtonUp"
                                Cursor="Hand">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <!-- 猫头像SVG图标 -->
                                <Viewbox Width="40" Height="40" Margin="0,0,8,0">
                                    <Canvas Width="24" Height="24">


                                        <!-- 左眼 -->
                                        <Ellipse Fill="#333333" Width="2" Height="3" Canvas.Left="8" Canvas.Top="9"/>

                                        <!-- 右眼 -->
                                        <Ellipse Fill="#333333" Width="2" Height="3" Canvas.Left="14" Canvas.Top="9"/>

                                        <!-- 鼻子 -->
                                        <Polygon Fill="#333333" Points="12,12 11,14 13,14"/>

                                        <!-- 嘴巴 -->
                                        <Path Stroke="#333333" StrokeThickness="1" Fill="Transparent"
      Data="M10,15 Q12,16 14,15"/>

                                        <!-- 胡须 -->
                                        <Line Stroke="#333333" StrokeThickness="1" X1="6" Y1="11" X2="3" Y2="10"/>
                                        <Line Stroke="#333333" StrokeThickness="1" X1="6" Y1="13" X2="3" Y2="13"/>
                                        <Line Stroke="#333333" StrokeThickness="1" X1="18" Y1="11" X2="21" Y2="10"/>
                                        <Line Stroke="#333333" StrokeThickness="1" X1="18" Y1="13" X2="21" Y2="13"/>
                                    </Canvas>
                                </Viewbox>

                                <!-- 标题文字 -->
                                <TextBlock Text="像素喵笔记本" FontSize="16" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- 树状列表 -->
                        <TreeView x:Name="notesTreeView" Grid.Row="1" Margin="5" BorderThickness="0"
                                  MouseRightButtonUp="notesTreeView_MouseRightButtonUp"
                                  SelectedItemChanged="notesTreeView_SelectedItemChanged"
                                  AllowDrop="True"
                                  PreviewMouseLeftButtonDown="notesTreeView_PreviewMouseLeftButtonDown"
                                  MouseLeftButtonUp="notesTreeView_MouseLeftButtonUp"
                                  PreviewMouseMove="notesTreeView_PreviewMouseMove"
                                  PreviewDragOver="notesTreeView_PreviewDragOver"
                                  Drop="notesTreeView_Drop"
                                  VirtualizingPanel.IsVirtualizing="True"
                                  VirtualizingPanel.VirtualizationMode="Recycling">
                        </TreeView>
                    </Grid>
                </Border>

                <!-- 右侧：文本编辑器和工具栏 -->
                <Grid x:Name="mainContentGrid" Grid.Column="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 顶部：工具栏（包含List按钮和其他功能按钮） -->
                    <Border x:Name="topToolbar" Grid.Row="0" Height="50" Background="#FAFAFA" CornerRadius="8,8,0,0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                        <Grid>
                            <!-- 左侧工具按钮 -->
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0,0,0" HorizontalAlignment="Left">
                                <!-- 第一组：导航按钮 -->
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <!-- 设置按钮 -->
                                    <Button x:Name="btnSettings" Style="{StaticResource FigmaListButtonStyle}"
                                        ToolTip="软件设置" Click="BtnSettings_Click"
                                        PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                        Margin="0,0,8,0"
                                        ToolTipService.Placement="Bottom"
                                        ToolTipService.HorizontalOffset="-20"
                                        ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <!-- 设置图标 -->
                                            <TextBlock Text="⚙" FontSize="16" FontFamily="Segoe UI Emoji"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                                        </Grid>
                                    </Button>

                                    <!-- 文件夹按钮（图2效果） -->
                                    <Button x:Name="btnToggleTree" Style="{StaticResource FigmaListButtonStyle}"
                                        ToolTip="显示/隐藏笔记列表" Click="BtnToggleTree_Click"
                                        PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                        Margin="0,0,8,0"
                                        ToolTipService.Placement="Bottom"
                                        ToolTipService.HorizontalOffset="-39"
                                        ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <!-- 文件夹图标 -->
                                            <TextBlock Text="◫" FontSize="16" FontFamily="Segoe UI Emoji"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                                        </Grid>
                                    </Button>

                                    <!-- 收藏夹按钮（图2效果） -->
                                    <Button x:Name="btnFavorites" Style="{StaticResource FigmaListButtonStyle}"
                                        ToolTip="收藏夹" Click="BtnFavorites_Click"
                                        PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                        ToolTipService.Placement="Bottom"
                                        ToolTipService.HorizontalOffset="-9"
                                        ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <!-- 星星图标 -->
                                            <TextBlock x:Name="favoritesIcon" Text="☆" FontSize="16" FontFamily="Segoe UI"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                                        </Grid>
                                    </Button>
                                </StackPanel>

                                <!-- 分隔线 -->
                                <Border Width="1" Height="20" Background="#E0E0E0" Margin="12,0,12,0"
                                    Opacity="0.6" VerticalAlignment="Center">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Setter Property="Background">
                                                <Setter.Value>
                                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                        <GradientStop Color="Transparent" Offset="0"/>
                                                        <GradientStop Color="#E0E0E0" Offset="0.2"/>
                                                        <GradientStop Color="#E0E0E0" Offset="0.8"/>
                                                        <GradientStop Color="Transparent" Offset="1"/>
                                                    </LinearGradientBrush>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Border.Style>
                                </Border>

                                <!-- 第二组：功能按钮 -->
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                    <!-- 撤销按钮 -->
                                    <Button x:Name="btnUndo" Style="{StaticResource FigmaListButtonStyle}" Click="BtnUndo_Click"
                                        PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                        Margin="0,0,2,0"
                                        ToolTip="撤销 (Ctrl+Z)"
                                        ToolTipService.Placement="Bottom"
                                        ToolTipService.HorizontalOffset="-24"
                                        ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <TextBlock Text="↶" FontSize="16" FontFamily="Segoe UI"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                                        </Grid>
                                    </Button>

                                    <!-- 重做按钮 -->
                                    <Button x:Name="btnRedo" Style="{StaticResource FigmaListButtonStyle}" Click="BtnRedo_Click"
                                        PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                        Margin="0,0,8,0"
                                        ToolTip="重做 (Ctrl+Y)"
                                        ToolTipService.Placement="Bottom"
                                        ToolTipService.HorizontalOffset="-24"
                                        ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <TextBlock Text="↷" FontSize="16" FontFamily="Segoe UI"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                                        </Grid>
                                    </Button>




                                    <!-- 字体操作按钮 -->
                                    <Button x:Name="btnFontSettings" Style="{StaticResource FigmaListButtonStyle}" Click="btnFontSettings_Click"
                                        PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                        Margin="0,0,2,0"
                                        ToolTip="字体设置 (Ctrl+Shift+F)"
                                        ToolTipService.Placement="Bottom"
                                        ToolTipService.HorizontalOffset="-51"
                                        ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <TextBlock Text="" FontSize="16" FontFamily="Segoe MDL2 Assets"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                                        </Grid>
                                    </Button>





                                    <!-- 插入符号按钮 -->
                                    <Button x:Name="btnInsertSymbol" Style="{StaticResource FigmaListButtonStyle}" Click="BtnInsertSymbol_Click"
                                        PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                        Margin="0,0,2,0"
                                        ToolTip="插入符号"
                                        ToolTipService.Placement="Bottom"
                                        ToolTipService.HorizontalOffset="-14"
                                        ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <TextBlock Text="Ω" FontSize="16" FontFamily="Segoe UI"
                                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                                  Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"/>
                                        </Grid>
                                    </Button>

                                    <!-- 插入编号按钮 -->
                                    <Button x:Name="btnInsertNumbering" Style="{StaticResource FigmaListButtonStyle}" Click="BtnInsertNumbering_Click"
                                            PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                            Margin="0,0,2,0"
                                            ToolTip="插入编号"
                                            ToolTipService.Placement="Bottom"
                                            ToolTipService.HorizontalOffset="-14"
                                            ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <TextBlock Text="🔢" FontSize="14" FontFamily="Segoe UI Emoji"
                                                      Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                                      RenderOptions.BitmapScalingMode="HighQuality" Margin="-2,0,-1,-1"
                                                      />
                                        </Grid>
                                    </Button>
                                </StackPanel>

                                <!-- 第三组：媒体功能按钮 -->
                                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="8,0,0,0">
                                    <!-- 导入文件按钮 -->
                                    <Button x:Name="btnImportFiles" Style="{StaticResource FigmaListButtonStyle}" Click="BtnImportFiles_Click"
                                        PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                        Margin="0,0,2,0"
                                        ToolTip="导入文件 (Ctrl+O)"
                                        ToolTipService.Placement="Bottom"
                                        ToolTipService.HorizontalOffset="-34"
                                        ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <TextBlock Text="📁" FontSize="14" FontFamily="Segoe UI Emoji"
                                                  Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                                  RenderOptions.BitmapScalingMode="HighQuality" Margin="-3,0,-1,0"
                                                  />
                                        </Grid>
                                    </Button>



                                    <!-- 插入表格按钮 -->
                                    <Button x:Name="btnInsertTable" Style="{StaticResource FigmaListButtonStyle}" Click="BtnInsertTable_Click"
                                        PreviewMouseLeftButtonDown="Button_PreviewMouseLeftButtonDown"
                                        Margin="0,0,2,0"
                                        ToolTip="插入表格 (Ctrl+Shift+T)"
                                        ToolTipService.Placement="Bottom"
                                        ToolTipService.HorizontalOffset="-49"
                                        ToolTipService.VerticalOffset="5">
                                        <Grid>
                                            <TextBlock Text="📊" FontSize="14" FontFamily="Segoe UI Emoji"
                                                  Foreground="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                                  RenderOptions.BitmapScalingMode="HighQuality" Margin="-3,0,-1,-1"
                                                  />
                                        </Grid>
                                    </Button>
                                </StackPanel>




                            </StackPanel>

                            <!-- 右侧窗口控制按钮 -->
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0,0,15,0">
                                <!-- 最小化按钮 -->
                                <Button x:Name="btnMinimize" ToolTip="最小化" Click="BtnMinimize_Click" Width="32" Height="32"
                                    Background="Transparent" BorderThickness="0" Cursor="Hand"
                                    VerticalAlignment="Center">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Background" Value="Transparent"/>
                                            <Setter Property="BorderThickness" Value="0"/>
                                            <Setter Property="Foreground" Value="#5F6368"/>
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}" BorderThickness="0">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Foreground" Value="#0B8CE8"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                    <Rectangle Width="12" Height="1" Fill="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}" VerticalAlignment="Center"/>
                                </Button>

                                <!-- 还原/最大化按钮 -->
                                <Button x:Name="btnMaximize" ToolTip="最大化" Click="BtnMaximize_Click" Width="32" Height="32"
                                    Background="Transparent" BorderThickness="0" Cursor="Hand"
                                    VerticalAlignment="Center">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Background" Value="Transparent"/>
                                            <Setter Property="BorderThickness" Value="0"/>
                                            <Setter Property="Foreground" Value="#5F6368"/>
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}" BorderThickness="0">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Foreground" Value="#0B8CE8"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                    <Rectangle Width="10" Height="10" Stroke="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=Foreground}"
                                          StrokeThickness="1" Fill="Transparent"/>
                                </Button>

                                <!-- 关闭按钮 -->
                                <Button x:Name="btnClose" Content="✕" ToolTip="关闭" Click="BtnClose_Click"
                                   VerticalAlignment="Center"
                                   Style="{StaticResource FigmaDialogCloseButtonStyle}"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- 内容区域 -->
                    <Grid Grid.Row="1" x:Name="mainContentArea">
                        <!-- 顶部横线连接 -->
                        <Border Height="1" Background="#E6E6E6" VerticalAlignment="Top" Margin="0,0,0,0"/>

                        <!-- 内容容器 -->
                        <Grid Margin="0,1,0,0">

                            <!-- 收藏夹弹出界面 -->
                            <Border x:Name="favoritesPopup" Background="White"
                            BorderBrush="#DADCE0" BorderThickness="1"
                            CornerRadius="8" Margin="15,10,0,0"
                            Width="300" Height="200"
                            HorizontalAlignment="Left" VerticalAlignment="Top"
                            Visibility="Collapsed" Panel.ZIndex="1000">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2"
                                            Opacity="0.3" BlurRadius="8"/>
                                </Border.Effect>
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <!-- 标题栏 -->
                                    <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8,8,0,0" Padding="12,8">
                                        <Grid>
                                            <TextBlock Text="收藏夹" FontSize="16" FontWeight="SemiBold"
                                             Foreground="#202124" VerticalAlignment="Center" FontFamily="Segoe UI"/>
                                            <Button x:Name="btnCloseFavorites" Click="BtnCloseFavorites_Click"
                                            Style="{StaticResource FigmaDialogCloseButtonStyle}"
                                            Content="✕"
                                            HorizontalAlignment="Right"/>
                                        </Grid>
                                    </Border>

                                    <!-- 收藏夹列表 -->
                                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                                        HorizontalScrollBarVisibility="Disabled">
                                        <ListBox x:Name="favoritesList" BorderThickness="0"
                                       Background="Transparent" Margin="8"
                                       SelectionChanged="FavoritesList_SelectionChanged"
                                       ItemContainerStyle="{StaticResource FigmaSelectionStyle}">
                                            <ListBox.ItemTemplate>
                                                <DataTemplate>
                                                    <Grid Margin="4">
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <TextBlock Grid.Column="0" Text="⭐" FontSize="12"
                                                         Margin="0,0,6,0" VerticalAlignment="Center"/>
                                                        <TextBlock Grid.Column="1" Text="{Binding Name}"
                                                         FontSize="12" Foreground="#202124"
                                                         VerticalAlignment="Center" TextTrimming="CharacterEllipsis"/>
                                                        <Button Grid.Column="2" Content="×" FontSize="10"
                                                      Width="20" Height="20" Background="Transparent"
                                                      BorderThickness="0" Foreground="#9AA0A6"
                                                      Click="RemoveFavorite_Click" Tag="{Binding}"
                                                      ToolTip="移除收藏">
                                                            <Button.Style>
                                                                <Style TargetType="Button">
                                                                    <Setter Property="Template">
                                                                        <Setter.Value>
                                                                            <ControlTemplate TargetType="Button">
                                                                                <Border x:Name="border" Background="Transparent"
                                                                                CornerRadius="4">
                                                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                                            VerticalAlignment="Center"/>
                                                                                </Border>
                                                                                <ControlTemplate.Triggers>
                                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                                        <Setter TargetName="border" Property="Background" Value="#FF2323"/>
                                                                                        <Setter Property="Foreground" Value="White"/>
                                                                                    </Trigger>
                                                                                </ControlTemplate.Triggers>
                                                                            </ControlTemplate>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Style>
                                                            </Button.Style>
                                                        </Button>
                                                    </Grid>
                                                </DataTemplate>
                                            </ListBox.ItemTemplate>
                                        </ListBox>
                                    </ScrollViewer>
                                </Grid>
                            </Border>

                            <!-- 文本编辑器 - 用于孙节点（页面） -->
                            <Grid x:Name="editorContainer" Visibility="Collapsed">
                                <!-- 原生RichTextBox编辑器 -->
                                <RichTextBox x:Name="richTextEditor"
                                           Margin="0"
                                           Background="{DynamicResource AppBackgroundBrush}"
                                           Foreground="{DynamicResource AppForegroundBrush}"
                                           BorderThickness="0"
                                           FontFamily="Microsoft YaHei, Webdings, Wingdings, Symbol, Arial Unicode MS"
                                           FontSize="14"
                                           AcceptsReturn="True"
                                           AcceptsTab="True"
                                           AllowDrop="True"
                                           IsDocumentEnabled="True"
                                           SpellCheck.IsEnabled="True"
                                           TextChanged="RichTextEditor_TextChanged"
                                           SelectionChanged="RichTextEditor_SelectionChanged"
                                           PreviewDragOver="RichTextEditor_PreviewDragOver"
                                           Drop="RichTextEditor_Drop"
                                           PreviewKeyDown="RichTextEditor_PreviewKeyDown"
                                           CaretBrush="{DynamicResource AppForegroundBrush}"
                                           SelectionBrush="#4299E1">

                                    <!-- 自定义右键菜单 - 应用Figma风格 -->
                                    <RichTextBox.ContextMenu>
                                        <ContextMenu x:Name="richTextContextMenu"
                                                   Background="{DynamicResource AppCardBackgroundBrush}"
                                                   BorderBrush="{DynamicResource AppBorderBrush}"
                                                   Style="{StaticResource FigmaContextMenuStyle}">
                                            <MenuItem Header="撤销(_U)" Command="ApplicationCommands.Undo" InputGestureText="Ctrl+Z" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <MenuItem Header="重做(_R)" Command="ApplicationCommands.Redo" InputGestureText="Ctrl+Y" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <Separator Style="{StaticResource FigmaSeparatorStyle}"/>
                                            <MenuItem Header="剪切(_T)" Command="ApplicationCommands.Cut" InputGestureText="Ctrl+X" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <MenuItem Header="复制(_C)" Command="ApplicationCommands.Copy" InputGestureText="Ctrl+C" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <MenuItem Header="粘贴(_P)" Command="ApplicationCommands.Paste" InputGestureText="Ctrl+V" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <MenuItem Header="删除(_D)" Command="ApplicationCommands.Delete" InputGestureText="Del" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <Separator Style="{StaticResource FigmaSeparatorStyle}"/>
                                            <MenuItem Header="全选(_A)" Command="ApplicationCommands.SelectAll" InputGestureText="Ctrl+A" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <Separator Style="{StaticResource FigmaSeparatorStyle}"/>
                                            <MenuItem Header="插入图片(_I)" Click="ContextMenu_InsertImage_Click" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <MenuItem Header="插入表格(_T)" Click="ContextMenu_InsertTable_Click" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <MenuItem Header="插入符号(_S)" Click="ContextMenu_InsertSymbol_Click" Style="{StaticResource FigmaMenuItemStyle}"/>
                                            <Separator Style="{StaticResource FigmaSeparatorStyle}"/>
                                            <MenuItem Header="字体设置(_F)" Click="ContextMenu_FontSettings_Click" Style="{StaticResource FigmaMenuItemStyle}"/>
                                        </ContextMenu>
                                    </RichTextBox.ContextMenu>
                                </RichTextBox>

                                <!-- 漂浮卡片Canvas容器 -->
                                <Canvas x:Name="floatingCardCanvas" ClipToBounds="False" IsHitTestVisible="True"/>
                            </Grid>

                            <!-- 媒体瀑布流 - 用于父节点和子节点 -->
                            <ScrollViewer x:Name="mediaScrollViewer" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled" Visibility="Collapsed">
                                <Grid x:Name="mediaContainer" Background="#FAFAFA" Margin="10">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>

                                    <!-- 标题区域 -->
                                    <StackPanel Grid.Row="0" Margin="0,0,0,20">
                                        <TextBlock x:Name="mediaTitleText" Text="媒体文件" FontSize="24" FontWeight="Bold"
                                         Foreground="#333333" Margin="0,0,0,5"/>
                                        <TextBlock x:Name="mediaSubtitleText" Text="选择的节点下的所有图片和媒体文件"
                                         FontSize="14" Foreground="#666666"/>
                                    </StackPanel>

                                    <!-- 瀑布流容器 - 启用虚拟化 -->
                                    <WrapPanel x:Name="mediaWrapPanel" Grid.Row="1" Orientation="Horizontal"
                                              VirtualizingPanel.IsVirtualizing="True"
                                              VirtualizingPanel.VirtualizationMode="Recycling"/>
                                </Grid>
                            </ScrollViewer>

                            <!-- 🔧 删除绘板功能，专注于代码页和文本页 -->

                            <!-- 代码编辑器容器 - 用于代码页节点 -->
                            <Grid x:Name="codeEditorContainer" Visibility="Collapsed">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 工具栏 -->
                                <Border Grid.Row="0" Background="#FAFAFA" BorderThickness="0,0,0,1" BorderBrush="#E0E0E0" Padding="15,10">
                                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                        <!-- 语言选择 -->
                                        <TextBlock Text="语言:" VerticalAlignment="Center" Margin="0,0,8,0" FontWeight="Medium" FontSize="13" Foreground="#333"/>
                                        <ComboBox x:Name="languageComboBox" Width="120" Margin="0,0,20,0" SelectionChanged="LanguageComboBox_SelectionChanged"
                                         Style="{StaticResource FigmaComboBoxStyle}">
                                            <ComboBoxItem Content="Python" Tag="Python"/>
                                            <ComboBoxItem Content="HTML" Tag="HTML"/>
                                        </ComboBox>

                                        <!-- 功能按钮 -->
                                        <Button x:Name="btnRunPython" Content="▶ 运行代码"
                                       Style="{StaticResource FigmaButtonStyle}"
                                       Margin="0,0,10,0" Click="BtnRunPython_Click"
                                       Visibility="Collapsed"/>
                                        <Button x:Name="btnPreviewHtml" Content="🌐 浏览器预览"
                                       Style="{StaticResource FigmaButtonStyle}"
                                       Click="BtnPreviewHtml_Click"
                                       Visibility="Collapsed"/>
                                    </StackPanel>
                                </Border>

                                <!-- 代码编辑器 -->
                                <Border Grid.Row="1" Background="White" BorderThickness="1" BorderBrush="#E0E0E0" Margin="15,10" CornerRadius="8">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.05" BlurRadius="4"/>
                                    </Border.Effect>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 行号区域 -->
                                        <Border Grid.Column="0" Background="#F8F9FA" BorderThickness="0,0,1,0" BorderBrush="#E0E0E0">
                                            <ScrollViewer x:Name="lineNumberScrollViewer"
                                                VerticalScrollBarVisibility="Hidden"
                                                HorizontalScrollBarVisibility="Hidden"
                                                CanContentScroll="True">
                                                <TextBlock x:Name="lineNumbersTextBlock"
                                                 FontFamily="Consolas, Monaco, 'Courier New', monospace"
                                                 FontSize="13"
                                                 Foreground="#9AA0A6"
                                                 Padding="8,10,4,10"
                                                 Text="1"
                                                 TextAlignment="Right"/>
                                            </ScrollViewer>
                                        </Border>

                                        <!-- 代码输入区域 -->
                                        <RichTextBox x:Name="codeRichTextBox" Grid.Column="1"
                                           AcceptsReturn="True"
                                           AcceptsTab="True"
                                           VerticalScrollBarVisibility="Auto"
                                           HorizontalScrollBarVisibility="Auto"
                                           FontFamily="Consolas, Monaco, 'Courier New', monospace"
                                           FontSize="13"
                                           Padding="12,10"
                                           BorderThickness="0"
                                           Background="Transparent"
                                           TextChanged="CodeRichTextBox_TextChanged"
                                           ScrollViewer.ScrollChanged="CodeRichTextBox_ScrollChanged">
                                            <RichTextBox.Document>
                                                <FlowDocument>
                                                    <Paragraph></Paragraph>
                                                </FlowDocument>
                                            </RichTextBox.Document>
                                        </RichTextBox>
                                    </Grid>
                                </Border>

                                <!-- 状态栏 -->
                                <Border Grid.Row="2" Background="#F8F9FA" BorderThickness="0,1,0,0" BorderBrush="#E0E0E0" Padding="15,8" Margin="15,0,15,15">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock x:Name="statusLineCount" Text="行数: 1" FontSize="12" Foreground="#666" Margin="0,0,20,0"/>
                                        <TextBlock x:Name="statusCharCount" Text="字符数: 0" FontSize="12" Foreground="#666" Margin="0,0,20,0"/>
                                        <TextBlock x:Name="statusLanguage" Text="语言: Python" FontSize="12" Foreground="#666"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <!-- 欢迎页面 - 默认显示 -->
                            <Grid x:Name="welcomeContainer" Background="#FAFAFA">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <Image Source="/Resources/F1.png" Width="64" Height="64" Margin="0,0,0,20" Opacity="0.6"/>
                                    <TextBlock Text="像素喵笔记" FontSize="28" FontWeight="Bold"
                                     Foreground="#333333" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="点击左上角按钮显示笔记列表" FontSize="16"
                                     Foreground="#666666" HorizontalAlignment="Center"/>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                                        <Border Background="#E8E8E8" CornerRadius="4" Padding="8,4" Margin="0,0,10,0">
                                            <TextBlock Text="📁 选择集合或笔记本查看媒体文件" FontSize="12" Foreground="#666666"/>
                                        </Border>
                                        <Border Background="#E8E8E8" CornerRadius="4" Padding="8,4" Margin="0,0,10,0">
                                            <TextBlock Text="📝 选择页面进行文本编辑" FontSize="12" Foreground="#666666"/>
                                        </Border>
                                        <!-- 🔧 删除绘板提示 -->
                                    </StackPanel>
                                </StackPanel>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>

            <!-- 下半部分：状态栏 -->
            <Border Grid.Row="1" Height="25" Background="White" CornerRadius="0,0,8,8">
                <StatusBar Background="Transparent">
                    <StatusBarItem x:Name="statusBarItem">
                        <TextBlock Text="准备就绪"/>
                    </StatusBarItem>
                    <Separator/>
                    <StatusBarItem x:Name="wordCountItem">
                        <TextBlock Text="字数: 0"/>
                    </StatusBarItem>
                    <Separator/>
                    <StatusBarItem x:Name="imageCountItem">
                        <TextBlock Text="图片数: 0"/>
                    </StatusBarItem>
                </StatusBar>
            </Border>
        </Grid>
    </Border>
</Window>
