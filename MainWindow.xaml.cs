using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.ComponentModel;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Data;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.Win32;
using System.Text;
using System.IO;
using System.IO;
using System.Threading;

namespace 像素喵笔记
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private TreeNodeManager _treeManager;
        private bool _isDragging = false;
        private BaseNode? _draggedNode = null;
        private bool _isTreeExpanded = false; // 树状控件是否展开
        private CodePageNode? _currentCodePageNode = null; // 当前编辑的代码页节点
        private SyntaxHighlighter? _syntaxHighlighter; // 高性能语法高亮器
        private readonly SaveManager _saveManager;
        private bool _isLoading = false;
        private TreeNodeData? _currentSelectedNode;

        // 实时保存相关字段
        private bool _hasPendingChanges = false;

        // 🔧 删除绘板功能，专注于代码页和文本页

        public MainWindow()
        {
            InitializeComponent();

            // 加载应用程序设置
            AppSettings.LoadFromFile();



            // 应用主题设置
            var theme = AppSettings.CurrentTheme == "Dark" ? AppTheme.Dark : AppTheme.Light;
            ThemeManager.ApplyTheme(theme);

            // 启用高DPI支持和清晰渲染（优化性能）
            RenderOptions.SetBitmapScalingMode(this, BitmapScalingMode.Linear);
            TextOptions.SetTextFormattingMode(this, TextFormattingMode.Ideal);

            // 初始化保存管理器
            _saveManager = SaveManager.Instance;
            TextOptions.SetTextRenderingMode(this, TextRenderingMode.ClearType);
            UseLayoutRounding = true;
            SnapsToDevicePixels = true;

            // 初始化树状节点管理器
            _treeManager = new TreeNodeManager();

            // 绑定事件
            _treeManager.NodeSelected += OnNodeSelected;
            _treeManager.NodeCreated += OnNodeCreated;
            _treeManager.NodeDeleted += OnNodeDeleted;
            _treeManager.NodeRenamed += OnNodeRenamed;
            _treeManager.NodeMoved += OnNodeMoved;

            // 初始化自动保存
            InitializeAutoSave();

            // 添加保存快捷键 Ctrl+S
            var saveCommand = new RoutedCommand();
            saveCommand.InputGestures.Add(new KeyGesture(Key.S, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(saveCommand, (s, e) => TestSave()));

            // 初始化界面
            Loaded += (s, e) =>
            {
                InitializeInterface();
                // 启用加载保存的数据
                LoadSavedData();
            };
        }

        /// <summary>
        /// 初始化界面
        /// </summary>
        private void InitializeInterface()
        {
            // 设置窗口标题
            this.Title = "像素喵记事本";

            // 初始化树状视图
            InitializeTreeView();

            // 不再添加默认示例节点，启动时显示空树
            // AddSampleTreeNodes();

            // 初始化树状控件为收起状态
            InitializeTreeContainer();

            // 设置默认工具栏状态（未选择任何节点）
            UpdateToolbarVisibility(null);

            // 绑定RichTextBox编辑器事件
            // 事件已在XAML中绑定

            // 绑定键盘快捷键
            InitializeKeyboardShortcuts();

            // 绑定文本编辑器右键菜单事件
            InitializeEditorContextMenu();

            // 默认显示欢迎页面
            ShowWelcomeScreen();
        }

        /// <summary>
        /// 显示欢迎屏幕
        /// </summary>
        private void ShowWelcomeScreen()
        {
            try
            {
                // 隐藏所有内容区域
                editorContainer.Visibility = Visibility.Collapsed;
                mediaScrollViewer.Visibility = Visibility.Collapsed;
                codeEditorContainer.Visibility = Visibility.Collapsed;

                // 显示欢迎页面
                welcomeContainer.Visibility = Visibility.Visible;

                System.Diagnostics.Debug.WriteLine("显示欢迎屏幕");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示欢迎屏幕失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化树状控件容器
        /// </summary>
        private void InitializeTreeContainer()
        {
            // 确保树状控件初始为隐藏状态
            if (treeContainer != null)
            {
                treeContainer.Visibility = Visibility.Collapsed;
            }

            // 设置初始圆角状态（树状控件关闭时左上角和右上角都有圆角）
            UpdateTopToolbarCornerRadius(true);
        }

        /// <summary>
        /// 初始化树状视图
        /// </summary>
        private void InitializeTreeView()
        {
            // 绑定数据源
            notesTreeView.ItemsSource = _treeManager.RootNodes;

            // 设置项目模板
            var template = CreateTreeViewItemTemplate();
            notesTreeView.ItemTemplate = template;

            // 设置TreeView的样式
            var style = CreateTreeViewItemStyle();
            notesTreeView.ItemContainerStyle = style;
        }

        /// <summary>
        /// 创建TreeViewItem样式
        /// </summary>
        private Style CreateTreeViewItemStyle()
        {
            var style = new Style(typeof(TreeViewItem));

            // 绑定IsSelected和IsExpanded属性
            style.Setters.Add(new Setter(TreeViewItem.IsSelectedProperty,
                new System.Windows.Data.Binding("IsSelected") { Mode = System.Windows.Data.BindingMode.TwoWay }));
            style.Setters.Add(new Setter(TreeViewItem.IsExpandedProperty,
                new System.Windows.Data.Binding("IsExpanded") { Mode = System.Windows.Data.BindingMode.TwoWay }));

            // 创建自定义的ControlTemplate来完全控制外观
            var template = CreateTreeViewItemControlTemplate();
            style.Setters.Add(new Setter(TreeViewItem.TemplateProperty, template));

            return style;
        }

        /// <summary>
        /// 创建TreeViewItem的ControlTemplate
        /// </summary>
        private ControlTemplate CreateTreeViewItemControlTemplate()
        {
            var template = new ControlTemplate(typeof(TreeViewItem));

            // 创建主容器 - 透明背景，不占用整行
            var mainContainer = new FrameworkElementFactory(typeof(StackPanel));
            mainContainer.SetValue(StackPanel.OrientationProperty, Orientation.Vertical);

            // 创建头部容器
            var headerContainer = new FrameworkElementFactory(typeof(Grid));
            headerContainer.SetValue(FrameworkElement.MarginProperty, new Thickness(0, 0, 0, 0)); // 减少上下间距

            // 定义列
            var column1 = new FrameworkElementFactory(typeof(ColumnDefinition));
            column1.SetValue(ColumnDefinition.WidthProperty, GridLength.Auto); // 三角形列
            var column2 = new FrameworkElementFactory(typeof(ColumnDefinition));
            column2.SetValue(ColumnDefinition.WidthProperty, GridLength.Auto); // 内容列，自适应宽度

            headerContainer.AppendChild(column1);
            headerContainer.AppendChild(column2);

            // 创建三角形展开按钮
            var expanderBorder = new FrameworkElementFactory(typeof(Border));
            expanderBorder.Name = "ExpanderBorder";
            expanderBorder.SetValue(Grid.ColumnProperty, 0);
            expanderBorder.SetValue(FrameworkElement.WidthProperty, 16.0);
            expanderBorder.SetValue(FrameworkElement.HeightProperty, 16.0);
            expanderBorder.SetValue(FrameworkElement.MarginProperty, new Thickness(0, 0, 4, 0));
            expanderBorder.SetValue(Border.BackgroundProperty, Brushes.Transparent);
            expanderBorder.SetValue(FrameworkElement.HorizontalAlignmentProperty, HorizontalAlignment.Center);
            expanderBorder.SetValue(FrameworkElement.VerticalAlignmentProperty, VerticalAlignment.Center);

            // 创建Figma风格的展开图标 - 使用文字图标
            var triangleText = new FrameworkElementFactory(typeof(TextBlock));
            triangleText.Name = "ExpanderText";
            triangleText.SetValue(TextBlock.TextProperty, "📁"); // 默认显示文件夹图标
            triangleText.SetValue(TextBlock.FontSizeProperty, 12.0);
            triangleText.SetValue(TextBlock.FontWeightProperty, FontWeights.Normal);
            triangleText.SetValue(TextBlock.ForegroundProperty, new SolidColorBrush(Color.FromRgb(50, 50, 50)));
            triangleText.SetValue(FrameworkElement.HorizontalAlignmentProperty, HorizontalAlignment.Center);
            triangleText.SetValue(FrameworkElement.VerticalAlignmentProperty, VerticalAlignment.Center);

            expanderBorder.AppendChild(triangleText);

            // 创建可点击的展开按钮（透明覆盖层）
            var expanderButton = new FrameworkElementFactory(typeof(System.Windows.Controls.Primitives.ToggleButton));
            expanderButton.Name = "Expander";
            expanderButton.SetValue(Grid.ColumnProperty, 0);
            expanderButton.SetValue(System.Windows.Controls.Primitives.ToggleButton.IsCheckedProperty,
                new System.Windows.Data.Binding("IsExpanded") { RelativeSource = new RelativeSource(RelativeSourceMode.TemplatedParent) });
            expanderButton.SetValue(System.Windows.Controls.Primitives.ToggleButton.BackgroundProperty, Brushes.Transparent);
            expanderButton.SetValue(System.Windows.Controls.Primitives.ToggleButton.BorderThicknessProperty, new Thickness(0));
            expanderButton.SetValue(FrameworkElement.WidthProperty, 16.0);
            expanderButton.SetValue(FrameworkElement.HeightProperty, 16.0);
            expanderButton.SetValue(FrameworkElement.MarginProperty, new Thickness(0, 0, 4, 0));
            expanderButton.SetValue(Control.TemplateProperty, CreateExpanderButtonTemplate());

            // 创建内容区域的边框（只包围文字部分）
            var contentBorder = new FrameworkElementFactory(typeof(Border));
            contentBorder.Name = "ContentBorder";
            contentBorder.SetValue(Grid.ColumnProperty, 1);
            contentBorder.SetValue(Border.BackgroundProperty, Brushes.Transparent);
            contentBorder.SetValue(Border.CornerRadiusProperty, new CornerRadius(4)); // 圆角
            contentBorder.SetValue(FrameworkElement.HorizontalAlignmentProperty, HorizontalAlignment.Left); // 左对齐，不占满整行
            contentBorder.SetValue(FrameworkElement.VerticalAlignmentProperty, VerticalAlignment.Center);
            contentBorder.SetValue(FrameworkElement.MarginProperty, new Thickness(0, 0, 0, 0)); // 去除边距

            // 创建内容呈现器
            var contentPresenter = new FrameworkElementFactory(typeof(ContentPresenter));
            contentPresenter.Name = "PART_Header";
            contentPresenter.SetValue(ContentPresenter.ContentSourceProperty, "Header");
            contentPresenter.SetValue(FrameworkElement.VerticalAlignmentProperty, VerticalAlignment.Center);
            contentPresenter.SetValue(FrameworkElement.MarginProperty, new Thickness(6, 3, 6, 3)); // 减少内边距，让节点更紧凑
            contentPresenter.SetValue(TextElement.FontSizeProperty, 14.0); // 增大字体
            contentPresenter.SetValue(TextElement.FontWeightProperty, FontWeights.Medium); // 中等字重

            contentBorder.AppendChild(contentPresenter);

            headerContainer.AppendChild(expanderBorder);
            headerContainer.AppendChild(expanderButton);
            headerContainer.AppendChild(contentBorder);

            // 创建子项容器
            var itemsPresenter = new FrameworkElementFactory(typeof(ItemsPresenter));
            itemsPresenter.Name = "ItemsHost";
            itemsPresenter.SetValue(FrameworkElement.MarginProperty, new Thickness(16, 0, 0, 0)); // 缩进子项
            itemsPresenter.SetValue(UIElement.VisibilityProperty, Visibility.Collapsed); // 默认隐藏子项

            mainContainer.AppendChild(headerContainer);
            mainContainer.AppendChild(itemsPresenter);

            template.VisualTree = mainContainer;

            // 添加触发器来控制选中状态的背景色（支持主题切换）
            var selectedTrigger = new Trigger();
            selectedTrigger.Property = TreeViewItem.IsSelectedProperty;
            selectedTrigger.Value = true;
            // 使用动态资源来支持主题切换
            var selectedBrush = ThemeManager.CurrentTheme == AppTheme.Dark
                ? new SolidColorBrush(Color.FromRgb(0x4A, 0x55, 0x68))
                : new SolidColorBrush(Color.FromRgb(240, 240, 240));
            selectedTrigger.Setters.Add(new Setter(Border.BackgroundProperty, selectedBrush, "ContentBorder"));

            // 添加触发器来控制展开图标（无动画）
            var expandedTrigger = new Trigger();
            expandedTrigger.Property = TreeViewItem.IsExpandedProperty;
            expandedTrigger.Value = true;
            // 展开时显示打开的文件夹图标
            expandedTrigger.Setters.Add(new Setter(TextBlock.TextProperty, "📂", "ExpanderText"));

            // 展开状态触发器 - 简单显示/隐藏子项
            expandedTrigger.Setters.Add(new Setter(UIElement.VisibilityProperty, Visibility.Visible, "ItemsHost"));

            // 添加折叠状态触发器
            var collapsedTrigger = new Trigger();
            collapsedTrigger.Property = TreeViewItem.IsExpandedProperty;
            collapsedTrigger.Value = false;
            // 折叠时显示关闭的文件夹图标
            collapsedTrigger.Setters.Add(new Setter(TextBlock.TextProperty, "📁", "ExpanderText"));



            // 添加触发器来控制展开按钮的可见性
            var hasItemsTrigger = new Trigger();
            hasItemsTrigger.Property = TreeViewItem.HasItemsProperty;
            hasItemsTrigger.Value = false;
            hasItemsTrigger.Setters.Add(new Setter(UIElement.VisibilityProperty, Visibility.Hidden, "ExpanderBorder"));
            hasItemsTrigger.Setters.Add(new Setter(UIElement.VisibilityProperty, Visibility.Hidden, "Expander"));

            template.Triggers.Add(selectedTrigger);
            template.Triggers.Add(expandedTrigger);
            template.Triggers.Add(collapsedTrigger);
            template.Triggers.Add(hasItemsTrigger);

            return template;
        }

        /// <summary>
        /// 创建展开按钮的模板（透明按钮）
        /// </summary>
        private ControlTemplate CreateExpanderButtonTemplate()
        {
            var template = new ControlTemplate(typeof(System.Windows.Controls.Primitives.ToggleButton));

            var border = new FrameworkElementFactory(typeof(Border));
            border.SetValue(Border.BackgroundProperty, Brushes.Transparent);
            border.SetValue(Border.BorderThicknessProperty, new Thickness(0));

            template.VisualTree = border;
            return template;
        }

        /// <summary>
        /// 创建树状视图项目模板
        /// </summary>
        private HierarchicalDataTemplate CreateTreeViewItemTemplate()
        {
            var template = new HierarchicalDataTemplate(typeof(BaseNode));
            template.ItemsSource = new System.Windows.Data.Binding("Children");

            // 创建显示内容
            var stackPanel = new FrameworkElementFactory(typeof(StackPanel));
            stackPanel.SetValue(StackPanel.OrientationProperty, Orientation.Horizontal);

            // 图标
            var iconText = new FrameworkElementFactory(typeof(TextBlock));
            iconText.SetValue(FrameworkElement.VerticalAlignmentProperty, VerticalAlignment.Center);
            iconText.SetValue(FrameworkElement.MarginProperty, new Thickness(0, 0, 6, 0));
            iconText.SetValue(TextBlock.FontSizeProperty, 14.0);

            // 根据节点类型和展开状态设置不同图标
            var iconMultiBinding = new MultiBinding();
            iconMultiBinding.Converter = new NodeTypeToIconConverter();
            iconMultiBinding.Bindings.Add(new System.Windows.Data.Binding("NodeType"));
            iconMultiBinding.Bindings.Add(new System.Windows.Data.Binding("IsExpanded"));
            iconText.SetBinding(TextBlock.TextProperty, iconMultiBinding);

            // 文本容器 - 支持内联编辑
            var textContainer = new FrameworkElementFactory(typeof(Grid));

            // 显示文本
            var textBlock = new FrameworkElementFactory(typeof(TextBlock));
            textBlock.Name = "DisplayText";
            textBlock.SetBinding(TextBlock.TextProperty, new System.Windows.Data.Binding("Name"));
            textBlock.SetValue(TextBlock.VerticalAlignmentProperty, VerticalAlignment.Center);
            textBlock.SetValue(UIElement.VisibilityProperty, Visibility.Visible);

            // 根据节点状态设置样式
            var styleBinding = new System.Windows.Data.Binding("State");
            styleBinding.Converter = new NodeStateToStyleConverter();
            textBlock.SetBinding(TextBlock.ForegroundProperty, styleBinding);

            // 编辑文本框
            var editBox = new FrameworkElementFactory(typeof(TextBox));
            editBox.Name = "EditBox";
            editBox.SetBinding(TextBox.TextProperty, new System.Windows.Data.Binding("Name"));
            editBox.SetValue(TextBox.VerticalAlignmentProperty, VerticalAlignment.Center);
            editBox.SetValue(TextBox.BorderThicknessProperty, new Thickness(1));
            editBox.SetValue(TextBox.BorderBrushProperty, new SolidColorBrush(Color.FromRgb(11, 140, 232)));
            editBox.SetValue(TextBox.BackgroundProperty, Brushes.White);
            editBox.SetValue(UIElement.VisibilityProperty, Visibility.Collapsed);
            editBox.SetValue(TextBox.FontSizeProperty, 12.0);
            editBox.SetValue(TextBox.PaddingProperty, new Thickness(4, 2, 4, 2));

            textContainer.AppendChild(textBlock);
            textContainer.AppendChild(editBox);

            stackPanel.AppendChild(iconText);
            stackPanel.AppendChild(textContainer);
            template.VisualTree = stackPanel;

            return template;
        }

        /// <summary>
        /// 添加示例树节点 - 已禁用，程序启动时显示空树
        /// </summary>
        private void AddSampleTreeNodes()
        {
            // 不再创建默认节点，让用户从空白开始
            System.Diagnostics.Debug.WriteLine("程序启动时不创建默认节点，显示空树状结构");
        }

        /// <summary>
        /// 初始化键盘快捷键
        /// </summary>
        private void InitializeKeyboardShortcuts()
        {
            // Ctrl+B - 创建笔记本
            var createNotebookCommand = new RoutedCommand();
            createNotebookCommand.InputGestures.Add(new KeyGesture(Key.B, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(createNotebookCommand, (s, e) => _treeManager.CreateNotebook()));

            // Ctrl+M - 创建集合
            var createCollectionCommand = new RoutedCommand();
            createCollectionCommand.InputGestures.Add(new KeyGesture(Key.M, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(createCollectionCommand, (s, e) => _treeManager.CreateCollection()));

            // Ctrl+N - 创建页面
            var createPageCommand = new RoutedCommand();
            createPageCommand.InputGestures.Add(new KeyGesture(Key.N, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(createPageCommand, (s, e) => _treeManager.CreatePage()));

            // F2 - 重命名
            var renameCommand = new RoutedCommand();
            renameCommand.InputGestures.Add(new KeyGesture(Key.F2));
            CommandBindings.Add(new CommandBinding(renameCommand, (s, e) => StartRenaming()));

            // Delete - 删除
            var deleteCommand = new RoutedCommand();
            deleteCommand.InputGestures.Add(new KeyGesture(Key.Delete));
            CommandBindings.Add(new CommandBinding(deleteCommand, (s, e) => DeleteSelectedNode()));

            // Ctrl+X - 剪切
            var cutCommand = new RoutedCommand();
            cutCommand.InputGestures.Add(new KeyGesture(Key.X, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(cutCommand, (s, e) => HandleCutCommand()));

            // Ctrl+C - 复制
            var copyCommand = new RoutedCommand();
            copyCommand.InputGestures.Add(new KeyGesture(Key.C, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(copyCommand, (s, e) => HandleCopyCommand()));

            // Ctrl+V - 粘贴
            var pasteCommand = new RoutedCommand();
            pasteCommand.InputGestures.Add(new KeyGesture(Key.V, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(pasteCommand, (s, e) => HandlePasteCommand()));

            // Ctrl+Z - 撤销
            var undoCommand = new RoutedCommand();
            undoCommand.InputGestures.Add(new KeyGesture(Key.Z, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(undoCommand, (s, e) => BtnUndo_Click(s, e)));

            // Ctrl+Y - 重做
            var redoCommand = new RoutedCommand();
            redoCommand.InputGestures.Add(new KeyGesture(Key.Y, ModifierKeys.Control));
            CommandBindings.Add(new CommandBinding(redoCommand, (s, e) => BtnRedo_Click(s, e)));



            // Ctrl+Shift+F - 字体设置 (修改为Ctrl+Shift+F，因为F2已被重命名使用)
            var fontSettingsCommand = new RoutedCommand();
            fontSettingsCommand.InputGestures.Add(new KeyGesture(Key.F, ModifierKeys.Control | ModifierKeys.Shift));
            CommandBindings.Add(new CommandBinding(fontSettingsCommand, (s, e) => btnFontSettings_Click(s, e)));





            // Alt+Enter - 显示节点属性
            var showPropertiesCommand = new RoutedCommand();
            showPropertiesCommand.InputGestures.Add(new KeyGesture(Key.Enter, ModifierKeys.Alt));
            CommandBindings.Add(new CommandBinding(showPropertiesCommand, (s, e) => {
                if (_treeManager.SelectedNode != null)
                {
                    ShowNodeProperties(_treeManager.SelectedNode);
                }
            }));
        }

        /// <summary>
        /// 初始化文本编辑器右键菜单
        /// </summary>
        private void InitializeEditorContextMenu()
        {
            // 新的高级富文本编辑器有自己的右键菜单处理
            // 这里可以添加额外的初始化逻辑
        }





        /// <summary>
        /// 创建文本编辑器右键菜单 - 已禁用，使用新的富文本编辑器
        /// </summary>
        private ContextMenu? CreateTextEditorContextMenu()
        {
            // 新的高级富文本编辑器有自己的右键菜单
            return null;
        }



        // ========== 界面事件处理程序 ==========

        #region 标题区域拖动功能

        private bool _isDraggingTitle = false;
        private Point _titleDragStartPoint;

        /// <summary>
        /// 标题区域鼠标按下事件
        /// </summary>
        private void TitleArea_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border)
            {
                _isDraggingTitle = true;
                _titleDragStartPoint = e.GetPosition(this);
                border.CaptureMouse();
            }
        }

        /// <summary>
        /// 标题区域鼠标移动事件
        /// </summary>
        private void TitleArea_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isDraggingTitle && e.LeftButton == MouseButtonState.Pressed)
            {
                Point currentPosition = e.GetPosition(this);
                Vector offset = currentPosition - _titleDragStartPoint;

                // 移动窗口
                this.Left += offset.X;
                this.Top += offset.Y;
            }
        }

        /// <summary>
        /// 标题区域鼠标抬起事件
        /// </summary>
        private void TitleArea_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (sender is Border border)
            {
                _isDraggingTitle = false;
                border.ReleaseMouseCapture();
            }
        }

        #endregion

        /// <summary>
        /// RichTextBox文本变化事件
        /// </summary>
        private void RichTextEditor_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 🔧 优化：使用防抖机制，避免频繁保存
            if (!_isLoading)
            {
                _hasPendingChanges = true;
                // 不再立即保存，改为在节点切换时保存
            }

            // 更新状态栏统计信息
            if (_treeManager?.SelectedNode != null && !_isLoading)
            {
                UpdateStatusBarForNode(_treeManager.SelectedNode);
            }

            // 确保所有图片都有正确的右键菜单
            if (!_isLoading)
            {
                EnsureAllImagesHaveContextMenu();
            }
        }

        /// <summary>
        /// RichTextBox选择变化事件
        /// </summary>
        private void RichTextEditor_SelectionChanged(object sender, RoutedEventArgs e)
        {
            // 更新工具栏状态，反映当前选择的格式
            UpdateToolbarState();
        }

        /// <summary>
        /// RichTextBox拖拽预览事件
        /// </summary>
        private void RichTextEditor_PreviewDragOver(object sender, DragEventArgs e)
        {
            // 检查是否为文件拖拽
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                e.Effects = DragDropEffects.Copy;
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
            e.Handled = true;
        }

        /// <summary>
        /// RichTextBox文件拖放事件
        /// </summary>
        private void RichTextEditor_Drop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                foreach (string file in files)
                {
                    if (IsImageFile(Path.GetExtension(file)))
                    {
                        InsertImageToRichTextBox(file);
                    }
                }
            }
        }

        /// <summary>
        /// RichTextBox按键预览事件
        /// </summary>
        private void RichTextEditor_PreviewKeyDown(object sender, KeyEventArgs e)
        {
            // 处理快捷键
            if (Keyboard.Modifiers == ModifierKeys.Control)
            {
                switch (e.Key)
                {
                    case Key.B: // 粗体
                        ToggleBold();
                        e.Handled = true;
                        break;
                    case Key.I: // 斜体
                        ToggleItalic();
                        e.Handled = true;
                        break;
                    case Key.U: // 下划线
                        ToggleUnderline();
                        e.Handled = true;
                        break;
                }
            }
        }

        #region 右键菜单事件处理

        /// <summary>
        /// 右键菜单 - 插入图片
        /// </summary>
        private void ContextMenu_InsertImage_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new Microsoft.Win32.OpenFileDialog
            {
                Title = "选择图片文件",
                Filter = "图片文件|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff;*.webp|所有文件|*.*",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                InsertImageToRichTextBox(openFileDialog.FileName);
            }
        }

        /// <summary>
        /// 右键菜单 - 插入表格
        /// </summary>
        private void ContextMenu_InsertTable_Click(object sender, RoutedEventArgs e)
        {
            InsertTableToRichTextBox(3, 3);
        }

        /// <summary>
        /// 右键菜单 - 插入符号
        /// </summary>
        private void ContextMenu_InsertSymbol_Click(object sender, RoutedEventArgs e)
        {
            BtnInsertSymbol_Click(sender, e);
        }

        /// <summary>
        /// 右键菜单 - 字体设置
        /// </summary>
        private void ContextMenu_FontSettings_Click(object sender, RoutedEventArgs e)
        {
            btnFontSettings_Click(sender, e);
        }

        #endregion

        /// <summary>
        /// 刷新所有相关节点的瀑布流显示
        /// </summary>
        private void RefreshAllRelatedWaterfallViews(string deletedFilePath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始刷新所有相关节点的瀑布流，删除的文件: {deletedFilePath}");

                // 获取当前选中的节点
                var currentNode = GetCurrentSelectedNode();
                if (currentNode == null)
                {
                    System.Diagnostics.Debug.WriteLine("当前没有选中的节点");
                    return;
                }

                // 刷新当前节点的瀑布流
                RefreshCurrentNodeWaterfall(currentNode);

                // 刷新父节点的瀑布流
                RefreshParentNodeWaterfall(currentNode);

                // 刷新子节点的瀑布流
                RefreshChildNodesWaterfall(currentNode);

                System.Diagnostics.Debug.WriteLine("所有相关节点的瀑布流刷新完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新所有相关节点瀑布流失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新当前节点的瀑布流
        /// </summary>
        private void RefreshCurrentNodeWaterfall(TreeNodeData currentNode)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"刷新当前节点瀑布流: {currentNode.Name}");

                // 如果当前是瀑布流视图，强制重新扫描文件夹并刷新
                if (IsWaterfallViewActive())
                {
                    System.Diagnostics.Debug.WriteLine("当前是瀑布流视图，强制重新加载");
                    ForceRefreshWaterfallView(currentNode);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新当前节点瀑布流失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新父节点的瀑布流
        /// </summary>
        private void RefreshParentNodeWaterfall(TreeNodeData currentNode)
        {
            try
            {
                var parentNode = currentNode.Parent;
                if (parentNode != null)
                {
                    System.Diagnostics.Debug.WriteLine($"刷新父节点瀑布流: {parentNode.Name}");

                    // 如果父节点有瀑布流视图，刷新它
                    RefreshNodeWaterfallView(parentNode);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新父节点瀑布流失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新子节点的瀑布流
        /// </summary>
        private void RefreshChildNodesWaterfall(TreeNodeData currentNode)
        {
            try
            {
                foreach (var childNode in currentNode.Children)
                {
                    System.Diagnostics.Debug.WriteLine($"刷新子节点瀑布流: {childNode.Name}");

                    // 刷新子节点的瀑布流
                    RefreshNodeWaterfallView(childNode);

                    // 递归刷新孙节点
                    RefreshChildNodesWaterfall(childNode);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新子节点瀑布流失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新指定节点的瀑布流视图
        /// </summary>
        private void RefreshNodeWaterfallView(TreeNodeData node)
        {
            try
            {
                // 这里可以根据需要实现具体的节点瀑布流刷新逻辑
                // 目前先记录日志，表示该节点的瀑布流需要刷新
                System.Diagnostics.Debug.WriteLine($"标记节点 {node.Name} 的瀑布流需要刷新");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新节点瀑布流失败: {node.Name}, {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前选中的节点
        /// </summary>
        private TreeNodeData? GetCurrentSelectedNode()
        {
            try
            {
                var selectedItem = notesTreeView.SelectedItem as TreeNodeData;
                return selectedItem;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取当前选中节点失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 检查当前是否是瀑布流视图
        /// </summary>
        private bool IsWaterfallViewActive()
        {
            try
            {
                return mediaScrollViewer.Visibility == Visibility.Visible;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 强制刷新瀑布流视图 - 重新扫描文件夹
        /// </summary>
        private void ForceRefreshWaterfallView(TreeNodeData nodeData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"强制刷新瀑布流视图: {nodeData.Name}");

                // 使用现有的刷新机制
                RefreshWaterfallAfterMediaDelete();

                System.Diagnostics.Debug.WriteLine($"瀑布流视图强制刷新完成: {nodeData.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"强制刷新瀑布流视图失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 媒体删除后刷新瀑布流
        /// </summary>
        private void RefreshWaterfallAfterMediaDelete()
        {
            try
            {
                // 检查当前是否正在显示瀑布流界面
                if (mediaScrollViewer.Visibility == Visibility.Visible)
                {
                    var selectedNode = _treeManager?.SelectedNode;
                    if (selectedNode != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"媒体删除后刷新瀑布流: {selectedNode.Name}");

                        // 如果当前选中的是父节点或子节点，直接刷新
                        if (selectedNode.NodeType == NodeType.Notebook || selectedNode.NodeType == NodeType.Collection)
                        {
                            // 延迟刷新，确保文件删除完成
                            Dispatcher.BeginInvoke(new Action(() =>
                            {
                                LoadMediaWaterfallContent(selectedNode);
                            }), System.Windows.Threading.DispatcherPriority.Background);
                        }
                        // 如果当前选中的是孙节点，找到其父节点来刷新瀑布流
                        else if (selectedNode.NodeType == NodeType.Page || selectedNode.NodeType == NodeType.CodePage)
                        {
                            var parentNode = selectedNode.Parent;
                            while (parentNode != null)
                            {
                                if (parentNode.NodeType == NodeType.Notebook || parentNode.NodeType == NodeType.Collection)
                                {
                                    System.Diagnostics.Debug.WriteLine($"通过父节点刷新瀑布流: {parentNode.Name}");

                                    // 延迟刷新，确保文件删除完成
                                    Dispatcher.BeginInvoke(new Action(() =>
                                    {
                                        LoadMediaWaterfallContent(parentNode);
                                    }), System.Windows.Threading.DispatcherPriority.Background);

                                    break;
                                }
                                parentNode = parentNode.Parent;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"媒体删除后刷新瀑布流失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗口关闭事件
        /// </summary>
        private async void Window_Closing(object sender, CancelEventArgs e)
        {
            try
            {
                // 🔧 删除绘板保存逻辑

                // 清理事件处理器
                if (_treeManager != null)
                {
                    _treeManager.NodeSelected -= OnNodeSelected;
                    _treeManager.NodeCreated -= OnNodeCreated;
                    _treeManager.NodeDeleted -= OnNodeDeleted;
                    _treeManager.NodeRenamed -= OnNodeRenamed;
                    _treeManager.NodeMoved -= OnNodeMoved;
                }

                // RichTextBox事件在XAML中绑定，无需手动清理

                // 清理浮动卡片Canvas中的所有元素
                if (floatingCardCanvas != null)
                {
                    foreach (UIElement element in floatingCardCanvas.Children)
                    {
                        if (element is FloatingCard card)
                        {
                            // FloatingCard会在其内部清理自己的事件
                        }
                    }
                    floatingCardCanvas.Children.Clear();
                }
            }
            catch (Exception ex)
            {
                // 静默处理清理过程中的错误
                System.Diagnostics.Debug.WriteLine($"窗口关闭清理时发生错误: {ex.Message}");
            }
        }


        /// <summary>
        /// 字体设置按钮点击事件
        /// </summary>
        private void btnFontSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取当前选择的字体设置
                var currentSettings = GetCurrentFontSettingsFromRichTextBox();

                // 打开字体设置对话框
                var fontDialog = new FontSettingsDialog(currentSettings);
                fontDialog.Owner = this;

                if (fontDialog.ShowDialog() == true && fontDialog.SelectedFontSettings != null)
                {
                    // 应用字体设置到RichTextBox
                    ApplyFontSettingsToRichTextBox(fontDialog.SelectedFontSettings);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开字体设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从RichTextBox获取当前字体设置
        /// </summary>
        private FontSettings GetCurrentFontSettingsFromRichTextBox()
        {
            var settings = new FontSettings
            {
                FontFamily = "Microsoft YaHei UI",
                FontSize = 14,
                TextColor = System.Windows.Media.Colors.Black
            };

            try
            {
                if (richTextEditor?.Selection != null && !richTextEditor.Selection.IsEmpty)
                {
                    // 获取选择区域的字体设置
                    var fontFamily = richTextEditor.Selection.GetPropertyValue(TextElement.FontFamilyProperty);
                    var fontSize = richTextEditor.Selection.GetPropertyValue(TextElement.FontSizeProperty);
                    var foreground = richTextEditor.Selection.GetPropertyValue(TextElement.ForegroundProperty);

                    if (fontFamily != DependencyProperty.UnsetValue && fontFamily is FontFamily family)
                    {
                        settings.FontFamily = family.Source;
                    }

                    if (fontSize != DependencyProperty.UnsetValue && fontSize is double size)
                    {
                        settings.FontSize = size;
                    }

                    if (foreground != DependencyProperty.UnsetValue && foreground is SolidColorBrush brush)
                    {
                        settings.TextColor = brush.Color;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取字体设置失败: {ex.Message}");
            }

            return settings;
        }

        /// <summary>
        /// 将字体设置应用到RichTextBox
        /// </summary>
        private void ApplyFontSettingsToRichTextBox(FontSettings settings)
        {
            try
            {
                if (richTextEditor?.Selection != null)
                {
                    // 创建字体族，支持特殊字体回退
                    var fontFamily = CreateFontFamilyWithFallback(settings.FontFamily);

                    // 应用字体族
                    richTextEditor.Selection.ApplyPropertyValue(TextElement.FontFamilyProperty, fontFamily);

                    // 应用字体大小
                    richTextEditor.Selection.ApplyPropertyValue(TextElement.FontSizeProperty, settings.FontSize);

                    // 应用字体颜色
                    richTextEditor.Selection.ApplyPropertyValue(TextElement.ForegroundProperty, new SolidColorBrush(settings.TextColor));

                    System.Diagnostics.Debug.WriteLine($"字体设置已应用: {settings.FontFamily}, {settings.FontSize}pt, {settings.TextColor}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用字体设置失败: {ex.Message}");
                MessageBox.Show($"应用字体设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 创建支持特殊字体回退的FontFamily
        /// </summary>
        private FontFamily CreateFontFamilyWithFallback(string primaryFont)
        {
            try
            {
                // 特殊字体需要特殊处理
                if (IsSpecialFont(primaryFont))
                {
                    // 对于特殊字体，只使用该字体，不使用回退
                    return new FontFamily(primaryFont);
                }
                else
                {
                    // 对于普通字体，使用回退机制
                    return new FontFamily($"{primaryFont}, Microsoft YaHei, Webdings, Wingdings, Symbol, Arial Unicode MS");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建字体族失败: {ex.Message}");
                return new FontFamily("Microsoft YaHei");
            }
        }

        /// <summary>
        /// 判断是否为特殊字体
        /// </summary>
        private bool IsSpecialFont(string fontName)
        {
            var specialFonts = new[] { "Webdings", "Wingdings", "Wingdings 2", "Wingdings 3", "Symbol", "Marlett" };
            return specialFonts.Any(f => string.Equals(f, fontName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取当前字体设置 - 兼容性方法
        /// </summary>
        public FontSettings GetCurrentFontSettings(object? selection = null)
        {
            return GetCurrentFontSettingsFromRichTextBox();
        }







        /// <summary>
        /// 导入文件按钮点击事件
        /// </summary>
        private void BtnImportFiles_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择要导入的图片文件",
                    Filter = "图片文件|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.tiff;*.webp|" +
                            "所有文件|*.*",
                    FilterIndex = 1,
                    Multiselect = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    foreach (var fileName in openFileDialog.FileNames)
                    {
                        // 只支持图片文件
                        var extension = Path.GetExtension(fileName).ToLower();

                        if (IsImageFile(extension))
                        {
                            InsertImageToRichTextBox(fileName);
                        }
                        else
                        {
                            MessageBox.Show($"只支持图片文件，不支持的文件格式: {extension}", "文件格式错误",
                                MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 判断是否为图片文件
        /// </summary>
        private bool IsImageFile(string extension)
        {
            var imageExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp" };
            return imageExtensions.Contains(extension);
        }

        // 🔧 删除音频和视频文件检测方法



        /// <summary>
        /// 插入表格按钮点击事件
        /// </summary>
        private void BtnInsertTable_Click(object sender, RoutedEventArgs e)
        {
            // 调用RichTextBox的插入表格功能
            InsertTableToRichTextBox(3, 3);
        }




        /// <summary>
        /// 插入符号按钮点击事件
        /// </summary>
        private void BtnInsertSymbol_Click(object sender, RoutedEventArgs e)
        {
            var symbolDialog = new SymbolDialog();
            symbolDialog.Owner = this;
            symbolDialog.KeepOpen = true; // 设置为保持窗口打开

            // 添加符号选择事件处理
            symbolDialog.OnSymbolSelected += (symbol) => {
                // 使用RichTextBox插入符号
                InsertTextToRichTextBox(symbol);
            };

            // 显示对话框
            symbolDialog.ShowDialog();
        }

        /// <summary>
        /// 撤销按钮点击事件 - 已禁用，使用新的富文本编辑器
        /// </summary>
        private void BtnUndo_Click(object sender, RoutedEventArgs e)
        {
            // 新的高级富文本编辑器有自己的撤销/重做功能
            MessageBox.Show("请使用 Ctrl+Z 进行撤销操作", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 重做按钮点击事件 - 已禁用，使用新的富文本编辑器
        /// </summary>
        private void BtnRedo_Click(object sender, RoutedEventArgs e)
        {
            // 新的高级富文本编辑器有自己的撤销/重做功能
            MessageBox.Show("请使用 Ctrl+Y 进行重做操作", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 插入编号按钮点击事件
        /// </summary>
        private void BtnInsertNumbering_Click(object sender, RoutedEventArgs e)
        {
            var numberingDialog = new NumberingDialog();
            numberingDialog.Owner = this;

            if (numberingDialog.ShowDialog() == true)
            {
                string selectedStyle = "";
                string styleType = "";

                if (!string.IsNullOrEmpty(numberingDialog.SelectedNumberingStyle))
                {
                    selectedStyle = numberingDialog.SelectedNumberingStyle;
                    styleType = "numbering";
                }
                else if (!string.IsNullOrEmpty(numberingDialog.SelectedBulletStyle))
                {
                    selectedStyle = numberingDialog.SelectedBulletStyle;
                    styleType = "bullet";
                }
                else if (!string.IsNullOrEmpty(numberingDialog.SelectedMultiLevelStyle))
                {
                    selectedStyle = numberingDialog.SelectedMultiLevelStyle;
                    styleType = "multilevel";
                }

                if (!string.IsNullOrEmpty(selectedStyle))
                {
                    // 使用RichTextBox插入编号列表
                    InsertListToRichTextBox(styleType == "ordered");
                }
            }
        }

        /// <summary>
        /// 应用编号到选中的文本 - 已禁用，使用新的富文本编辑器
        /// </summary>
        private void ApplyNumberingToSelection(string style, string styleType)
        {
            // 新的高级富文本编辑器有自己的编号功能
            // 这个方法已经不再使用
        }

        // 编号相关的辅助方法已移除，使用新的高级富文本编辑器

        // 更多编号相关的辅助方法已移除，使用新的高级富文本编辑器

        /// <summary>
        /// 更新状态栏信息 - 已禁用，使用新的富文本编辑器
        /// </summary>
        private void UpdateStatusBar()
        {
            // 新的高级富文本编辑器有自己的状态栏管理
            // 这个方法已经不再使用
        }

        /// <summary>
        /// 树状列表右键点击事件
        /// </summary>
        private void notesTreeView_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            var treeView = sender as TreeView;
            if (treeView == null) return;

            // 获取点击的节点
            var hitTest = VisualTreeHelper.HitTest(treeView, e.GetPosition(treeView));
            var treeViewItem = FindParent<TreeViewItem>(hitTest?.VisualHit);

            BaseNode? clickedNode = null;
            if (treeViewItem != null)
            {
                clickedNode = treeViewItem.DataContext as BaseNode;

                // 右键选中节点
                if (clickedNode != null)
                {
                    // 设置TreeView的选中项
                    treeViewItem.IsSelected = true;
                    treeViewItem.Focus();

                    // 更新管理器的选中节点
                    _treeManager.SelectedNode = clickedNode;

                    // 加载节点内容
                    LoadNodeContent(clickedNode);
                    UpdateStatusBarForNode(clickedNode);
                }
            }

            // 创建右键菜单
            var contextMenu = CreateContextMenu(clickedNode);
            if (contextMenu != null)
            {
                contextMenu.PlacementTarget = treeView;
                contextMenu.IsOpen = true;
            }
        }

        /// <summary>
        /// 树状列表选中项变化事件 - 简化处理，避免重复选择
        /// </summary>
        private void notesTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            // 防止在拖拽过程中触发选择变化
            if (_isDragging) return;

            // 直接更新选中节点，不触发额外事件
            if (e.NewValue is BaseNode selectedNode)
            {
                // 清除旧选择状态
                if (e.OldValue is BaseNode oldNode)
                {
                    oldNode.IsSelected = false;
                }

                // 设置新选择状态
                selectedNode.IsSelected = true;
                _treeManager.SelectedNode = selectedNode;

                // 立即加载内容
                LoadNodeContent(selectedNode);
                UpdateStatusBarForNode(selectedNode);
            }
        }

        /// <summary>
        /// 树状列表鼠标按下事件
        /// </summary>
        private void notesTreeView_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            var treeView = sender as TreeView;
            if (treeView == null) return;

            var hitTest = VisualTreeHelper.HitTest(treeView, e.GetPosition(treeView));
            var treeViewItem = FindParent<TreeViewItem>(hitTest?.VisualHit);

            if (treeViewItem != null && treeViewItem.DataContext is BaseNode node)
            {
                // 检查是否点击的是展开/折叠按钮
                var toggleButton = FindParent<System.Windows.Controls.Primitives.ToggleButton>(hitTest?.VisualHit);
                if (toggleButton != null)
                {
                    // 如果点击的是展开/折叠按钮，不设置拖拽节点
                    return;
                }

                // 确保节点被选中，无论层级如何
                if (!node.IsSelected)
                {
                    // 清除现有选择
                    if (_treeManager.SelectedNode != null)
                        _treeManager.SelectedNode.IsSelected = false;

                    // 设置新选择
                    node.IsSelected = true;
                    _treeManager.SelectedNode = node;

                    // 加载节点内容
                    LoadNodeContent(node);
                    UpdateStatusBarForNode(node);
                }

                // 设置拖拽节点
                _draggedNode = node;
                _isDragging = false;
            }
        }

        /// <summary>
        /// 树状列表鼠标抬起事件 - 删除冗余选择逻辑，由SelectedItemChanged统一处理
        /// </summary>
        private void notesTreeView_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            // 只处理拖拽结束逻辑，选择逻辑由SelectedItemChanged处理
            if (_isDragging)
            {
                _isDragging = false;
            }
        }

        /// <summary>
        /// 树状列表鼠标移动事件
        /// </summary>
        private void notesTreeView_PreviewMouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && _draggedNode != null && !_isDragging)
            {
                _isDragging = true;
                _draggedNode.State = NodeState.Dragging;

                var dragData = new DataObject("BaseNode", _draggedNode);
                DragDrop.DoDragDrop(notesTreeView, dragData, DragDropEffects.Move);

                _draggedNode.State = NodeState.Normal;
                _isDragging = false;
                _draggedNode = null;
            }
        }

        /// <summary>
        /// 树状列表拖拽悬停事件
        /// </summary>
        private void notesTreeView_PreviewDragOver(object sender, DragEventArgs e)
        {
            e.Effects = DragDropEffects.None;

            if (e.Data.GetDataPresent("BaseNode"))
            {
                var draggedNode = e.Data.GetData("BaseNode") as BaseNode;
                if (draggedNode == null) return;

                var treeView = sender as TreeView;
                if (treeView == null) return;

                var hitTest = VisualTreeHelper.HitTest(treeView, e.GetPosition(treeView));
                var treeViewItem = FindParent<TreeViewItem>(hitTest?.VisualHit);

                if (treeViewItem != null && treeViewItem.DataContext is BaseNode targetNode)
                {
                    if (CanDropNode(draggedNode, targetNode))
                    {
                        e.Effects = DragDropEffects.Move;
                    }
                }
                else
                {
                    // 拖拽到空白区域，检查是否可以放到根级别
                    if (draggedNode.CanMoveTo(null))
                    {
                        e.Effects = DragDropEffects.Move;
                    }
                }
            }

            e.Handled = true;
        }

        /// <summary>
        /// 树状列表拖拽放置事件
        /// </summary>
        private void notesTreeView_Drop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("BaseNode"))
            {
                var draggedNode = e.Data.GetData("BaseNode") as BaseNode;
                if (draggedNode == null) return;

                var treeView = sender as TreeView;
                if (treeView == null) return;

                var hitTest = VisualTreeHelper.HitTest(treeView, e.GetPosition(treeView));
                var treeViewItem = FindParent<TreeViewItem>(hitTest?.VisualHit);

                BaseNode? targetNode = null;
                if (treeViewItem != null)
                {
                    targetNode = treeViewItem.DataContext as BaseNode;
                }

                if (CanDropNode(draggedNode, targetNode))
                {
                    _treeManager.MoveNode(draggedNode, targetNode);
                    // 拖动完成后进行一次刷新，确保界面状态正确
                    RefreshTreeView();
                }
            }

            e.Handled = true;
        }











        // ========== 树状节点管理辅助方法 ==========



        /// <summary>
        /// 创建右键菜单
        /// </summary>
        private ContextMenu? CreateContextMenu(BaseNode? node)
        {
            var contextMenu = new ContextMenu();

            // 应用Figma风格样式
            if (TryFindResource("FigmaContextMenuStyle") is Style contextMenuStyle)
            {
                contextMenu.Style = contextMenuStyle;
            }

            if (node == null)
            {
                // 空白区域右键菜单
                contextMenu.Items.Add(CreateFigmaMenuItem("创建笔记本", "📓", () => _treeManager.CreateNotebook()));
                contextMenu.Items.Add(CreateFigmaMenuItem("粘贴", "📋", () => _treeManager.PasteNode(), "Ctrl+V"));
                contextMenu.Items.Add(CreateFigmaSeparator());
                contextMenu.Items.Add(CreateFigmaMenuItem("展开全部", "📂", () => _treeManager.ExpandAll()));
                contextMenu.Items.Add(CreateFigmaMenuItem("折叠全部", "📁", () => _treeManager.CollapseAll()));
                contextMenu.Items.Add(CreateFigmaMenuItem("刷新", "🔄", () => RefreshTreeView()));
            }
            else
            {
                // 节点右键菜单
                switch (node.NodeType)
                {
                    case NodeType.Notebook:
                        contextMenu.Items.Add(CreateFigmaMenuItem("创建集合", "📁", () => _treeManager.CreateCollection(node as NotebookNode)));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("剪切", "✂️", () => _treeManager.CutNode(node), "Ctrl+X"));
                        contextMenu.Items.Add(CreateFigmaMenuItem("复制", "📄", () => _treeManager.CopyNode(node), "Ctrl+C"));
                        contextMenu.Items.Add(CreateFigmaMenuItem("粘贴", "📋", () => _treeManager.PasteNode(node), "Ctrl+V"));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("加入收藏夹", "⭐", () => AddToFavorites(node)));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("重命名", "✏️", () => StartRenaming(node), "F2"));
                        contextMenu.Items.Add(CreateFigmaMenuItem("删除", "🗑️", () => _treeManager.DeleteNode(node), "Delete"));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("导出笔记本", "📤", () => ExportNotebook(node as NotebookNode)));
                        contextMenu.Items.Add(CreateFigmaMenuItem("属性", "⚙️", () => ShowNodeProperties(node), "Alt+Enter"));
                        break;

                    case NodeType.Collection:
                        contextMenu.Items.Add(CreateFigmaMenuItem("创建页面", "📝", () => _treeManager.CreatePage(node as CollectionNode)));
                        contextMenu.Items.Add(CreateFigmaMenuItem("创建代码页", "💻", () => _treeManager.CreateCodePage(node as CollectionNode)));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("剪切", "✂️", () => _treeManager.CutNode(node), "Ctrl+X"));
                        contextMenu.Items.Add(CreateFigmaMenuItem("复制", "📄", () => _treeManager.CopyNode(node), "Ctrl+C"));
                        contextMenu.Items.Add(CreateFigmaMenuItem("粘贴", "📋", () => _treeManager.PasteNode(node), "Ctrl+V"));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("加入收藏夹", "⭐", () => AddToFavorites(node)));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("重命名", "✏️", () => StartRenaming(node), "F2"));
                        contextMenu.Items.Add(CreateFigmaMenuItem("删除", "🗑️", () => _treeManager.DeleteNode(node), "Delete"));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("移动到其他笔记本", "📦", () => MoveToOtherNotebook(node)));
                        contextMenu.Items.Add(CreateFigmaMenuItem("导出集合", "📤", () => ExportCollection(node as CollectionNode)));
                        contextMenu.Items.Add(CreateFigmaMenuItem("属性", "⚙️", () => ShowNodeProperties(node), "Alt+Enter"));
                        break;

                    case NodeType.Page:
                        contextMenu.Items.Add(CreateFigmaMenuItem("剪切", "✂️", () => _treeManager.CutNode(node), "Ctrl+X"));
                        contextMenu.Items.Add(CreateFigmaMenuItem("复制", "📄", () => _treeManager.CopyNode(node), "Ctrl+C"));
                        contextMenu.Items.Add(CreateFigmaMenuItem("粘贴", "📋", () => _treeManager.PasteNode(node), "Ctrl+V"));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("加入收藏夹", "⭐", () => AddToFavorites(node)));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("重命名", "✏️", () => StartRenaming(node), "F2"));
                        contextMenu.Items.Add(CreateFigmaMenuItem("删除", "🗑️", () => _treeManager.DeleteNode(node), "Delete"));
                        contextMenu.Items.Add(CreateFigmaSeparator());
                        contextMenu.Items.Add(CreateFigmaMenuItem("移动到其他集合", "📦", () => MoveToOtherCollection(node)));
                        contextMenu.Items.Add(CreateFigmaMenuItem("复制到其他集合", "📋", () => CopyToOtherCollection(node)));
                        contextMenu.Items.Add(CreateFigmaMenuItem("导出页面", "📤", () => ExportPage(node as PageNode)));
                        contextMenu.Items.Add(CreateFigmaMenuItem("属性", "⚙️", () => ShowNodeProperties(node), "Alt+Enter"));
                        break;

                    // 🔧 删除绘板节点类型
                }
            }

            return contextMenu;
        }



        /// <summary>
        /// 创建Figma风格的菜单项
        /// </summary>
        private MenuItem CreateFigmaMenuItem(string text, string icon, Action action, string? shortcut = null)
        {
            var menuItem = new MenuItem();

            // 应用Figma样式
            if (TryFindResource("FigmaMenuItemStyle") is Style menuItemStyle)
            {
                menuItem.Style = menuItemStyle;
            }

            // 创建内容容器
            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 图标
            var iconText = new TextBlock
            {
                Text = icon,
                FontSize = 14,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center,
                Width = 20
            };

            // 文本
            var textBlock = new TextBlock
            {
                Text = text,
                FontSize = 13,
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(iconText);
            stackPanel.Children.Add(textBlock);

            // 快捷键
            if (!string.IsNullOrEmpty(shortcut))
            {
                var shortcutText = new TextBlock
                {
                    Text = shortcut,
                    FontSize = 11,
                    Foreground = new SolidColorBrush(Color.FromRgb(165, 165, 165)),
                    HorizontalAlignment = HorizontalAlignment.Right,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(20, 0, 0, 0)
                };

                // 使用Grid来实现左右布局
                var grid = new Grid();
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

                Grid.SetColumn(stackPanel, 0);
                Grid.SetColumn(shortcutText, 1);

                grid.Children.Add(stackPanel);
                grid.Children.Add(shortcutText);

                menuItem.Header = grid;
            }
            else
            {
                menuItem.Header = stackPanel;
            }

            menuItem.Click += (s, e) => action?.Invoke();
            return menuItem;
        }

        /// <summary>
        /// 创建Figma风格的分隔符
        /// </summary>
        private Separator CreateFigmaSeparator()
        {
            var separator = new Separator();
            if (TryFindResource("FigmaSeparatorStyle") is Style separatorStyle)
            {
                separator.Style = separatorStyle;
            }
            return separator;
        }



        /// <summary>
        /// 获取工具栏中的所有按钮
        /// </summary>
        private IEnumerable<UIElement> GetToolBarButtons()
        {
            var toolBar = FindChild<ToolBar>(this);
            if (toolBar != null)
            {
                foreach (var item in toolBar.Items)
                {
                    if (item is UIElement element)
                        yield return element;
                }
            }
        }

        /// <summary>
        /// 查找子控件
        /// </summary>
        private T? FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T result)
                    return result;

                var childOfChild = FindChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }



        /// <summary>
        /// 创建菜单项（保留兼容性）
        /// </summary>
        private MenuItem CreateMenuItem(string header, Action action)
        {
            var menuItem = new MenuItem { Header = header };
            menuItem.Click += (s, e) => action?.Invoke();
            return menuItem;
        }



        /// <summary>
        /// 查找父级控件
        /// </summary>
        private T? FindParent<T>(DependencyObject? child) where T : DependencyObject
        {
            if (child == null) return null;

            var parent = VisualTreeHelper.GetParent(child);
            if (parent is T parentT)
                return parentT;

            return FindParent<T>(parent);
        }

        /// <summary>
        /// 检查是否可以拖放节点
        /// </summary>
        private bool CanDropNode(BaseNode draggedNode, BaseNode? targetNode)
        {
            if (draggedNode == targetNode) return false;
            if (targetNode != null && !targetNode.CanAddChild(draggedNode.NodeType)) return false;
            if (!draggedNode.CanMoveTo(targetNode)) return false;

            // 检查是否会造成循环引用
            var current = targetNode;
            while (current != null)
            {
                if (current == draggedNode) return false;
                current = current.Parent;
            }

            return true;
        }

        /// <summary>
        /// 加载节点内容
        /// </summary>
        private void LoadNodeContent(BaseNode node)
        {
            try
            {
                // 隐藏所有内容区域
                editorContainer.Visibility = Visibility.Collapsed;
                mediaScrollViewer.Visibility = Visibility.Collapsed;
                codeEditorContainer.Visibility = Visibility.Collapsed;
                welcomeContainer.Visibility = Visibility.Collapsed;

                // 根据节点类型控制工具栏可见性
                UpdateToolbarVisibility(node.NodeType);

                switch (node.NodeType)
                {
                    case NodeType.Page:
                        // 孙节点（页面）- 显示文本编辑器
                        editorContainer.Visibility = Visibility.Visible;

                        if (node is PageNode pageNode)
                        {
                            LoadPageContent(pageNode);

                            // 订阅所有实时保存相关事件
                            SubscribeToRealtimeSaveEvents();
                        }
                        break;

                    // 🔧 删除绘板节点处理

                    case NodeType.CodePage:
                        // 孙节点（代码页）- 显示代码编辑器
                        codeEditorContainer.Visibility = Visibility.Visible;

                        if (node is CodePageNode codePageNode)
                        {
                            LoadCodePageContent(codePageNode);
                        }
                        break;

                    case NodeType.Notebook:
                    case NodeType.Collection:
                        // 父节点和子节点 - 显示媒体瀑布流
                        mediaScrollViewer.Visibility = Visibility.Visible;
                        LoadMediaWaterfallContent(node);
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载节点内容失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 出错时显示欢迎页面
                editorContainer.Visibility = Visibility.Collapsed;
                mediaScrollViewer.Visibility = Visibility.Collapsed;
                codeEditorContainer.Visibility = Visibility.Collapsed;
                welcomeContainer.Visibility = Visibility.Visible;

                // 出错时隐藏所有工具栏
                UpdateToolbarVisibility(null);
            }
        }

        /// <summary>
        /// 根据节点类型更新工具栏可见性
        /// </summary>
        private void UpdateToolbarVisibility(NodeType? nodeType)
        {
            try
            {
                // 默认隐藏所有编辑工具
                btnFontSettings.Visibility = Visibility.Collapsed;
                btnInsertSymbol.Visibility = Visibility.Collapsed;
                btnInsertNumbering.Visibility = Visibility.Collapsed;
                btnInsertTable.Visibility = Visibility.Collapsed;
                btnImportFiles.Visibility = Visibility.Collapsed;
                btnUndo.Visibility = Visibility.Collapsed;
                btnRedo.Visibility = Visibility.Collapsed;

                // 始终显示的基础工具
                btnToggleTree.Visibility = Visibility.Visible;
                btnFavorites.Visibility = Visibility.Visible;

                switch (nodeType)
                {
                    case NodeType.Page:
                        // 文档页面：显示所有文本编辑工具
                        btnFontSettings.Visibility = Visibility.Visible;

                        btnInsertSymbol.Visibility = Visibility.Visible;
                        btnInsertNumbering.Visibility = Visibility.Visible;
                        btnInsertTable.Visibility = Visibility.Visible;
                        btnImportFiles.Visibility = Visibility.Visible;
                        btnUndo.Visibility = Visibility.Visible;
                        btnRedo.Visibility = Visibility.Visible;
                        break;

                    // 🔧 删除绘板工具栏处理

                    case NodeType.CodePage:
                        // 代码页面：隐藏大部分文字编辑工具，保留网页插入和基础功能

                        btnImportFiles.Visibility = Visibility.Visible; // 导入代码文件
                        btnUndo.Visibility = Visibility.Visible;
                        btnRedo.Visibility = Visibility.Visible;
                        break;

                    case NodeType.Notebook:
                    case NodeType.Collection:
                        // 父节点和子节点：只显示基础导航工具
                        // 其他工具已在默认情况下隐藏
                        break;

                    case null:
                    default:
                        // 未选择节点或未知类型：隐藏所有编辑工具
                        break;
                }

                System.Diagnostics.Debug.WriteLine($"工具栏可见性已更新，节点类型: {nodeType}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新工具栏可见性失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载页面内容 - 简化版本
        /// </summary>
        private async void LoadPageContent(PageNode pageNode)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== 开始加载页面内容: {pageNode.Name} ===");

                // 如果节点有关联的TreeNodeData，加载内容
                if (pageNode.Tag is TreeNodeData nodeData)
                {
                    var (content, nodeType) = await _saveManager.LoadNodeContentAsync(nodeData);
                    if (content is string contentStr && !string.IsNullOrEmpty(contentStr) && nodeType == "Document")
                    {
                        try
                        {
                            // FlowDocument变量已移除，直接使用LoadContentToRichTextBox

                            // 检查是否是RTF内容
                            if (contentStr.StartsWith("{\\rtf"))
                            {
                                System.Diagnostics.Debug.WriteLine("检测到RTF内容，直接加载");

                                // 使用LoadContentToRichTextBox方法加载RTF内容
                                LoadContentToRichTextBox(contentStr);

                                // 处理图片路径
                                ProcessImagePathsInRichTextBox(nodeData);
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("检测到XAML内容，解析加载");

                                // 加载内容到RichTextBox
                                LoadContentToRichTextBox(contentStr);
                            }
                            System.Diagnostics.Debug.WriteLine($"页面内容加载完成: {pageNode.Name}");
                            return;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"内容解析失败: {ex.Message}");
                        }
                    }
                }

                // 如果所有方法都失败，使用默认方式加载
                System.Diagnostics.Debug.WriteLine("使用默认方式加载页面");
                LoadContentToRichTextBox("开始编写您的内容...");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载页面内容失败: {ex.Message}");
                MessageBox.Show($"加载页面内容失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        /// <summary>
        /// 简单直接地处理文档中的图片标记
        /// </summary>
        private void ProcessImageMarkersInDocument(FlowDocument document, TreeNodeData nodeData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始处理图片标记 ===");

                var nodeFolder = Path.GetDirectoryName(_saveManager.GetDocumentPath(nodeData));
                var processedCount = 0;

                // 遍历所有段落
                foreach (var block in document.Blocks.ToList())
                {
                    if (block is Paragraph paragraph)
                    {
                        // 遍历所有内联元素
                        foreach (var inline in paragraph.Inlines.ToList())
                        {
                            if (inline is Run run && run.Text.Contains("<img "))
                            {
                                var text = run.Text;
                                System.Diagnostics.Debug.WriteLine($"找到包含图片标记的文本: {text}");

                                // 使用正则表达式查找所有图片标记
                                var pattern = @"<img\s+src=""([^""]+)""\s+width=""([^""]+)""\s+height=""([^""]+)""\s+id=""([^""]+)""\s*/>";
                                var matches = System.Text.RegularExpressions.Regex.Matches(text, pattern);

                                if (matches.Count > 0)
                                {
                                    // 从后往前替换，避免索引问题
                                    var newInlines = new List<Inline>();
                                    var lastIndex = 0;

                                    foreach (System.Text.RegularExpressions.Match match in matches)
                                    {
                                        // 添加标记前的文本
                                        if (match.Index > lastIndex)
                                        {
                                            var beforeText = text.Substring(lastIndex, match.Index - lastIndex);
                                            if (!string.IsNullOrEmpty(beforeText))
                                            {
                                                newInlines.Add(new Run(beforeText));
                                            }
                                        }

                                        // 解析图片属性
                                        var src = match.Groups[1].Value;
                                        var width = double.Parse(match.Groups[2].Value);
                                        var height = double.Parse(match.Groups[3].Value);
                                        var id = match.Groups[4].Value;

                                        System.Diagnostics.Debug.WriteLine($"解析图片: src={src}, width={width}, height={height}");

                                        // 构建绝对路径
                                        var absolutePath = Path.IsPathRooted(src) ? src : Path.Combine(nodeFolder, src);

                                        // 检查文件是否存在
                                        if (File.Exists(absolutePath))
                                        {
                                            // 创建图片控件
                                            var imageControl = new ResizableImageControl();
                                            imageControl.LoadImageFile(absolutePath);
                                            imageControl.Name = id;
                                            imageControl.Width = width;
                                            imageControl.Height = height;

                                            var container = new InlineUIContainer(imageControl);
                                            newInlines.Add(container);

                                            processedCount++;
                                            System.Diagnostics.Debug.WriteLine($"✅ 成功创建图片控件: {absolutePath}");
                                        }
                                        else
                                        {
                                            // 尝试在附件文件夹中查找
                                            var fileName = Path.GetFileName(absolutePath);
                                            var attachmentPath = Path.Combine(nodeFolder, "附件", fileName);

                                            if (File.Exists(attachmentPath))
                                            {
                                                var imageControl = new ResizableImageControl();
                                                imageControl.LoadImageFile(attachmentPath);
                                                imageControl.Name = id;
                                                imageControl.Width = width;
                                                imageControl.Height = height;

                                                var container = new InlineUIContainer(imageControl);
                                                newInlines.Add(container);

                                                processedCount++;
                                                System.Diagnostics.Debug.WriteLine($"✅ 在附件文件夹找到图片: {attachmentPath}");
                                            }
                                            else
                                            {
                                                // 文件不存在，保留原始标记
                                                newInlines.Add(new Run(match.Value));
                                                System.Diagnostics.Debug.WriteLine($"❌ 图片文件不存在，保留标记: {absolutePath}");
                                            }
                                        }

                                        lastIndex = match.Index + match.Length;
                                    }

                                    // 添加最后剩余的文本
                                    if (lastIndex < text.Length)
                                    {
                                        var remainingText = text.Substring(lastIndex);
                                        if (!string.IsNullOrEmpty(remainingText))
                                        {
                                            newInlines.Add(new Run(remainingText));
                                        }
                                    }

                                    // 替换原来的Run
                                    paragraph.Inlines.Remove(run);

                                    // 添加新的内联元素
                                    foreach (var newInline in newInlines)
                                    {
                                        paragraph.Inlines.Add(newInline);
                                    }
                                }
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"=== 图片标记处理完成，共处理 {processedCount} 个图片 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理图片标记失败: {ex.Message}");
            }
        }

        // 🔧 删除LoadDrawingBoardContent方法

        /// <summary>
        /// 加载代码页内容
        /// </summary>
        private void LoadCodePageContent(CodePageNode codePageNode)
        {
            try
            {
                // 设置当前代码页节点
                _currentCodePageNode = codePageNode;

                // 初始化语法高亮器
                InitializeSyntaxHighlighter();

                // 加载代码内容
                LoadCodeIntoRichTextBox(codePageNode.Code ?? "");

                // 设置语言选择
                foreach (ComboBoxItem item in languageComboBox.Items)
                {
                    if (item.Tag?.ToString() == codePageNode.Language)
                    {
                        languageComboBox.SelectedItem = item;
                        break;
                    }
                }

                // 如果没有找到匹配的语言，默认选择第一个
                if (languageComboBox.SelectedItem == null && languageComboBox.Items.Count > 0)
                {
                    languageComboBox.SelectedIndex = 0;
                }

                // 根据语言类型显示相应的预览按钮
                UpdateCodeEditorUI();

                // 设置焦点到代码编辑器
                codeRichTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载代码页内容失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载媒体瀑布流内容
        /// </summary>
        private async void LoadMediaWaterfallContent(BaseNode node)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始加载瀑布流内容: {node.Name}");

                // 清空媒体容器
                mediaWrapPanel.Children.Clear();

                // 更新标题
                mediaTitleText.Text = $"{node.Name} 的媒体文件";

                // 收集所有子孙节点中的图片
                var imageInfos = await CollectImagesFromNodeAsync(node);

                if (imageInfos.Count == 0)
                {
                    // 确保容器为空，然后只添加一个空状态卡片
                    mediaWrapPanel.Children.Clear();

                    // 显示空状态
                    var emptyState = CreateEmptyMediaState(node);
                    mediaWrapPanel.Children.Add(emptyState);

                    // 更新副标题显示为空状态
                    mediaSubtitleText.Text = $"{node.Name} 下暂无媒体文件";

                    System.Diagnostics.Debug.WriteLine("已添加空状态卡片");
                }
                else
                {
                    // 更新副标题显示实际数量
                    mediaSubtitleText.Text = $"显示 {node.Name} 下的 {imageInfos.Count} 个媒体文件";

                    // 在瀑布流中显示图片
                    await DisplayImagesInWaterfallAsync(imageInfos);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载瀑布流内容失败: {ex.Message}");
                MessageBox.Show($"加载媒体内容失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 图片信息类
        /// </summary>
        private class ImageInfo
        {
            public string FilePath { get; set; } = "";
            public string SourceNodeName { get; set; } = "";
            public BaseNode SourceNode { get; set; } = null!;
            public string Title { get; set; } = "";
        }

        /// <summary>
        /// 异步收集节点中的所有图片
        /// </summary>
        private async Task<List<ImageInfo>> CollectImagesFromNodeAsync(BaseNode node)
        {
            var imageInfos = new List<ImageInfo>();

            try
            {
                await Task.Run(() => CollectImagesRecursive(node, imageInfos));

                // 去重：根据文件路径去除重复的图片
                var uniqueImageInfos = imageInfos
                    .GroupBy(img => img.FilePath)
                    .Select(group => group.First())
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"去重前: {imageInfos.Count} 张图片，去重后: {uniqueImageInfos.Count} 张图片");

                return uniqueImageInfos;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"收集图片失败: {ex.Message}");
            }

            return imageInfos;
        }

        /// <summary>
        /// 递归收集图片
        /// </summary>
        private void CollectImagesRecursive(BaseNode node, List<ImageInfo> imageInfos)
        {
            try
            {
                // 检查是否是任何类型的页面节点
                bool isPageNode = node.NodeType == NodeType.Page ||
                                  node.NodeType == NodeType.CodePage;

                if (isPageNode && node.Tag is TreeNodeData nodeData)
                {
                    System.Diagnostics.Debug.WriteLine($"检查页面节点附件: {node.Name}, 类型: {node.NodeType}");

                    // 获取附件文件夹路径
                    var attachmentPath = GetNodeAttachmentPath(nodeData);

                    if (Directory.Exists(attachmentPath))
                    {
                        System.Diagnostics.Debug.WriteLine($"附件文件夹存在: {attachmentPath}");

                        // 获取所有图片文件
                        var imageFiles = Directory.GetFiles(attachmentPath, "*.*")
                            .Where(file => IsImageFile(Path.GetExtension(file).ToLower()))
                            .ToList();

                        System.Diagnostics.Debug.WriteLine($"找到 {imageFiles.Count} 张图片");

                        // 添加到结果列表
                        foreach (var imageFile in imageFiles)
                        {
                            imageInfos.Add(new ImageInfo
                            {
                                FilePath = imageFile,
                                SourceNodeName = node.Name,
                                SourceNode = node,
                                Title = Path.GetFileNameWithoutExtension(imageFile)
                            });
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"附件文件夹不存在: {attachmentPath}");
                    }
                }

                // 递归处理子节点
                foreach (var child in node.Children)
                {
                    CollectImagesRecursive(child, imageInfos);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"递归收集图片失败: {node.Name}, {ex.Message}");
            }
        }

        /// <summary>
        /// 从节点收集媒体文件
        /// </summary>
        private List<MediaFileInfo> CollectMediaFilesFromNode(BaseNode node)
        {
            var mediaFiles = new List<MediaFileInfo>();

            // 递归收集所有子页面中的媒体文件
            CollectMediaFilesRecursive(node, mediaFiles);

            return mediaFiles;
        }

        /// <summary>
        /// 获取节点附件路径
        /// </summary>
        private string GetNodeAttachmentPath(TreeNodeData nodeData)
        {
            try
            {
                var nodePath = GetNodePathForAttachment(nodeData);
                return Path.Combine(nodePath, "附件");
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// 获取节点路径（用于附件）
        /// </summary>
        private string GetNodePathForAttachment(TreeNodeData nodeData)
        {
            try
            {
                // 使用基础目录
                string basePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SAVE");

                // 构建从根到当前节点的完整路径
                var pathParts = new List<string>();
                var current = nodeData;

                // 向上遍历到根节点，收集所有节点名称
                while (current != null)
                {
                    pathParts.Insert(0, current.Name);
                    current = current.Parent;
                }

                // 构建完整路径
                string fullPath = basePath;
                foreach (var part in pathParts)
                {
                    fullPath = Path.Combine(fullPath, part);
                }

                System.Diagnostics.Debug.WriteLine($"节点路径: {fullPath}");
                return fullPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取节点路径失败: {ex.Message}");
                return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SAVE");
            }
        }



        /// <summary>
        /// 在瀑布流中显示图片
        /// </summary>
        private async Task DisplayImagesInWaterfallAsync(List<ImageInfo> imageInfos)
        {
            try
            {
                // 清空现有内容，确保不会重复添加
                mediaWrapPanel.Children.Clear();

                // 跟踪已添加的文件路径，避免重复显示
                var addedFilePaths = new HashSet<string>();

                foreach (var imageInfo in imageInfos)
                {
                    // 检查是否已添加过相同路径的图片
                    if (addedFilePaths.Contains(imageInfo.FilePath))
                    {
                        System.Diagnostics.Debug.WriteLine($"跳过重复图片: {imageInfo.FilePath}");
                        continue;
                    }

                    var imageCard = await CreateImageCardAsync(imageInfo);
                    if (imageCard != null)
                    {
                        mediaWrapPanel.Children.Add(imageCard);
                        addedFilePaths.Add(imageInfo.FilePath);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"实际显示图片数量: {addedFilePaths.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示瀑布流图片失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 创建图片卡片
        /// </summary>
        private Task<FrameworkElement?> CreateImageCardAsync(ImageInfo imageInfo)
        {
            try
            {
                var card = new Border
                {
                    Background = Brushes.White,
                    CornerRadius = new CornerRadius(8),
                    Margin = new Thickness(5),
                    Padding = new Thickness(8),
                    Effect = new DropShadowEffect
                    {
                        Color = Colors.Gray,
                        Direction = 315,
                        ShadowDepth = 2,
                        Opacity = 0.3
                    },
                    Cursor = Cursors.Hand,
                    Width = 200
                };

                var stackPanel = new StackPanel();

                // 图片
                var image = new Image
                {
                    Source = new BitmapImage(new Uri(imageInfo.FilePath)),
                    Stretch = Stretch.UniformToFill,
                    Height = 150,
                    HorizontalAlignment = HorizontalAlignment.Center
                };

                // 标题
                var titleText = new TextBlock
                {
                    Text = imageInfo.Title,
                    FontWeight = FontWeights.SemiBold,
                    FontSize = 12,
                    Margin = new Thickness(0, 8, 0, 4),
                    TextTrimming = TextTrimming.CharacterEllipsis
                };

                // 来源节点
                var sourceText = new TextBlock
                {
                    Text = $"来自: {imageInfo.SourceNodeName}",
                    FontSize = 10,
                    Foreground = Brushes.Gray,
                    TextTrimming = TextTrimming.CharacterEllipsis
                };

                stackPanel.Children.Add(image);
                stackPanel.Children.Add(titleText);
                stackPanel.Children.Add(sourceText);
                card.Child = stackPanel;

                // 添加双击检测变量
                DateTime lastClickTime = DateTime.MinValue;
                const double DoubleClickInterval = 500; // 双击间隔毫秒

                // 点击事件：跳转到对应页面
                card.MouseLeftButtonDown += (s, e) =>
                {
                    try
                    {
                        var currentTime = DateTime.Now;
                        var timeSinceLastClick = lastClickTime == DateTime.MinValue ?
                            double.MaxValue : (currentTime - lastClickTime).TotalMilliseconds;

                        if (timeSinceLastClick <= DoubleClickInterval && lastClickTime != DateTime.MinValue)
                        {
                            // 双击事件 - 仅跳转到页面，不触发其他操作
                            System.Diagnostics.Debug.WriteLine($"双击媒体卡片，跳转到页面: {imageInfo.SourceNodeName}");

                            _treeManager.SelectedNode = imageInfo.SourceNode;
                            LoadNodeContent(imageInfo.SourceNode);
                            UpdateStatusBarForNode(imageInfo.SourceNode);

                            // 重置点击时间，避免影响下次双击检测
                            lastClickTime = DateTime.MinValue;
                            e.Handled = true;
                        }
                        else
                        {
                            // 单击事件 - 记录时间但不执行操作
                            lastClickTime = currentTime;

                            // 延迟执行单击操作，如果在双击间隔内没有第二次点击才执行
                            Task.Delay((int)DoubleClickInterval).ContinueWith(_ =>
                            {
                                Dispatcher.Invoke(() =>
                                {
                                    var finalTime = DateTime.Now;
                                    var finalInterval = (finalTime - lastClickTime).TotalMilliseconds;

                                    // 如果时间间隔接近双击间隔，说明是单击
                                    if (Math.Abs(finalInterval - DoubleClickInterval) < 50)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"单击媒体卡片，跳转到页面: {imageInfo.SourceNodeName}");

                                        _treeManager.SelectedNode = imageInfo.SourceNode;
                                        LoadNodeContent(imageInfo.SourceNode);
                                        UpdateStatusBarForNode(imageInfo.SourceNode);
                                    }
                                });
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"媒体卡片点击事件处理失败: {ex.Message}");
                    }
                };

                // 添加Figma风格右键菜单
                var contextMenu = new ContextMenu();

                // 应用Figma风格样式
                if (TryFindResource("FigmaContextMenuStyle") is Style contextMenuStyle)
                {
                    contextMenu.Style = contextMenuStyle;
                }

                // 查看原图
                var viewOriginalItem = CreateFigmaMenuItem("查看原图", "🖼️", () =>
                {
                    try
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = imageInfo.FilePath,
                            UseShellExecute = true
                        });
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"无法打开图片: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                });
                contextMenu.Items.Add(viewOriginalItem);

                // 在文件夹中显示
                var showInFolderItem = CreateFigmaMenuItem("在文件夹中显示", "📁", () =>
                {
                    try
                    {
                        System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{imageInfo.FilePath}\"");
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"无法打开文件夹: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                });
                contextMenu.Items.Add(showInFolderItem);

                // 分隔符
                contextMenu.Items.Add(CreateFigmaSeparator());

                // 跳转到源页面
                var gotoSourceItem = CreateFigmaMenuItem($"跳转到 \"{imageInfo.SourceNodeName}\"", "🔗", () =>
                {
                    try
                    {
                        _treeManager.SelectedNode = imageInfo.SourceNode;
                        LoadNodeContent(imageInfo.SourceNode);
                        UpdateStatusBarForNode(imageInfo.SourceNode);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"跳转到源页面失败: {ex.Message}");
                    }
                });
                contextMenu.Items.Add(gotoSourceItem);

                card.ContextMenu = contextMenu;

                return Task.FromResult<FrameworkElement?>(card);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建图片卡片失败: {imageInfo.FilePath}, {ex.Message}");
                return Task.FromResult<FrameworkElement?>(null);
            }
        }

        /// <summary>
        /// 递归收集媒体文件
        /// </summary>
        private void CollectMediaFilesRecursive(BaseNode node, List<MediaFileInfo> mediaFiles)
        {
            // 如果是页面节点，检查其内容中的媒体文件
            if (node is PageNode pageNode)
            {
                // 这里应该解析页面内容，提取图片和媒体文件
                // 暂时添加一些示例数据
                if (!string.IsNullOrEmpty(pageNode.Content))
                {
                    // 模拟从页面内容中提取的媒体文件
                    mediaFiles.Add(new MediaFileInfo
                    {
                        FileName = $"示例图片_{pageNode.Name}.jpg",
                        FilePath = "/Resources/F1.png", // 使用现有的图标作为示例
                        FileType = MediaFileType.Image,
                        SourcePage = pageNode.Name,
                        FileSize = "128 KB",
                        DateCreated = DateTime.Now.AddDays(-Random.Shared.Next(1, 30))
                    });
                }
            }

            // 递归处理子节点
            foreach (var child in node.Children)
            {
                CollectMediaFilesRecursive(child, mediaFiles);
            }
        }

        /// <summary>
        /// 创建媒体项目控件
        /// </summary>
        private FrameworkElement CreateMediaItem(MediaFileInfo mediaFile)
        {
            var container = new Border
            {
                Width = 180,
                Height = 180,
                Margin = new Thickness(10),
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(230, 230, 230)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Cursor = Cursors.Hand
            };

            // 添加阴影效果
            container.Effect = new System.Windows.Media.Effects.DropShadowEffect
            {
                Color = Colors.Gray,
                Direction = 315,
                ShadowDepth = 2,
                Opacity = 0.3,
                BlurRadius = 4
            };

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // 图片预览区域
            var imageArea = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(248, 248, 248)),
                CornerRadius = new CornerRadius(8, 8, 0, 0),
                Margin = new Thickness(0, 0, 0, 0)
            };

            var image = new Image
            {
                Source = new BitmapImage(new Uri(mediaFile.FilePath, UriKind.RelativeOrAbsolute)),
                Stretch = Stretch.UniformToFill,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            imageArea.Child = image;
            Grid.SetRow(imageArea, 0);

            // 信息区域
            var infoArea = new StackPanel
            {
                Margin = new Thickness(8),
                Background = Brushes.Transparent
            };

            var fileName = new TextBlock
            {
                Text = mediaFile.FileName,
                FontSize = 12,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(51, 51, 51)),
                TextTrimming = TextTrimming.CharacterEllipsis,
                Margin = new Thickness(0, 0, 0, 2)
            };

            var fileInfo = new TextBlock
            {
                Text = $"{mediaFile.FileSize} • {mediaFile.SourcePage}",
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                TextTrimming = TextTrimming.CharacterEllipsis
            };

            infoArea.Children.Add(fileName);
            infoArea.Children.Add(fileInfo);
            Grid.SetRow(infoArea, 1);

            grid.Children.Add(imageArea);
            grid.Children.Add(infoArea);
            container.Child = grid;

            // 添加鼠标悬停效果
            container.MouseEnter += (s, e) =>
            {
                container.Background = new SolidColorBrush(Color.FromRgb(248, 248, 248));
                container.BorderBrush = new SolidColorBrush(Color.FromRgb(200, 200, 200));
            };

            container.MouseLeave += (s, e) =>
            {
                container.Background = Brushes.White;
                container.BorderBrush = new SolidColorBrush(Color.FromRgb(230, 230, 230));
            };

            // 添加点击事件
            container.MouseLeftButtonUp += (s, e) =>
            {
                // 这里可以添加打开媒体文件的逻辑
                MessageBox.Show($"打开文件: {mediaFile.FileName}\n来源页面: {mediaFile.SourcePage}", "媒体文件", MessageBoxButton.OK, MessageBoxImage.Information);
            };

            return container;
        }

        /// <summary>
        /// 创建空状态显示
        /// </summary>
        private FrameworkElement CreateEmptyMediaState(BaseNode node)
        {
            var container = new Border
            {
                Width = 400,
                Height = 200,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Background = new SolidColorBrush(Color.FromRgb(250, 250, 250)),
                BorderBrush = new SolidColorBrush(Color.FromRgb(230, 230, 230)),
                BorderThickness = new Thickness(2),
                CornerRadius = new CornerRadius(8)
            };

            var stackPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var icon = new TextBlock
            {
                Text = "📁",
                FontSize = 48,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            var title = new TextBlock
            {
                Text = "暂无媒体文件",
                FontSize = 18,
                FontWeight = FontWeights.Medium,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 5)
            };

            var subtitle = new TextBlock
            {
                Text = $"{node.Name} 下的页面中还没有图片或媒体文件",
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153)),
                HorizontalAlignment = HorizontalAlignment.Center,
                TextWrapping = TextWrapping.Wrap
            };

            stackPanel.Children.Add(icon);
            stackPanel.Children.Add(title);
            stackPanel.Children.Add(subtitle);
            container.Child = stackPanel;

            return container;
        }

        /// <summary>
        /// 媒体文件信息类
        /// </summary>
        public class MediaFileInfo
        {
            public string FileName { get; set; } = "";
            public string FilePath { get; set; } = "";
            public MediaFileType FileType { get; set; }
            public string SourcePage { get; set; } = "";
            public string FileSize { get; set; } = "";
            public DateTime DateCreated { get; set; }
        }



        /// <summary>
        /// 更新节点状态栏
        /// </summary>
        private void UpdateStatusBarForNode(BaseNode node)
        {
            var stats = node.GetStatistics();

            switch (node.NodeType)
            {
                case NodeType.Page:
                    statusBarItem.Content = $"页面: {node.Name}";
                    wordCountItem.Content = $"字符数: {stats.WordCount}";
                    imageCountItem.Content = $"图片数: {stats.ImageCount}";
                    break;

                case NodeType.Collection:
                    statusBarItem.Content = $"集合: {node.Name}";
                    wordCountItem.Content = $"总字符数: {stats.WordCount}";
                    imageCountItem.Content = $"页面数: {stats.PageCount}";
                    break;

                case NodeType.Notebook:
                    statusBarItem.Content = $"笔记本: {node.Name}";
                    wordCountItem.Content = $"总字符数: {stats.WordCount}";
                    imageCountItem.Content = $"集合数: {stats.CollectionCount}";
                    break;
            }
        }

        /// <summary>
        /// 开始重命名 - 使用内联编辑模式
        /// </summary>
        private void StartRenaming(BaseNode? node = null)
        {
            try
            {
                node ??= _treeManager.SelectedNode;
                if (node == null)
                {
                    System.Diagnostics.Debug.WriteLine("StartRenaming: 没有选中的节点");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"StartRenaming: 开始重命名节点 {node.Name}");

                // 找到包含此节点的TreeViewItem
                var treeViewItem = FindTreeViewItem(notesTreeView, node);
                if (treeViewItem == null)
                {
                    MessageBox.Show("找不到节点对应的UI元素", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 找到显示文本和编辑框
                var displayText = FindVisualChild<TextBlock>(treeViewItem, "DisplayText");
                var editBox = FindVisualChild<TextBox>(treeViewItem, "EditBox");

                if (displayText != null && editBox != null)
                {
                    // 设置节点状态为编辑中
                    node.State = NodeState.Editing;

                    // 隐藏显示文本，显示编辑框
                    displayText.Visibility = Visibility.Collapsed;
                    editBox.Visibility = Visibility.Visible;

                    // 设置焦点并全选
                    editBox.Focus();
                    editBox.SelectAll();

                    // 注册失去焦点事件和按键事件
                    editBox.LostFocus += (s, e) => FinishRenaming(node, editBox, displayText);
                    editBox.KeyDown += (s, e) =>
                    {
                        if (e.Key == Key.Enter)
                        {
                            FinishRenaming(node, editBox, displayText);
                            e.Handled = true;
                        }
                        else if (e.Key == Key.Escape)
                        {
                            // 恢复原名，取消编辑
                            editBox.Text = node.Name;
                            CancelRenaming(node, displayText, editBox);
                            e.Handled = true;
                        }
                    };
                }
                else
                {
                    // 回退到对话框模式
                    var dialog = new RenameDialog(node.Name);
                    dialog.Owner = this;

                    if (dialog.ShowDialog() == true && !string.IsNullOrWhiteSpace(dialog.NewName))
                    {
                        _treeManager.RenameNode(node, dialog.NewName);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"StartRenaming 异常: {ex.Message}");
                MessageBox.Show($"重命名失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 完成重命名
        /// </summary>
        private void FinishRenaming(BaseNode node, TextBox editBox, TextBlock displayText)
        {
            try
            {
                // 移除事件处理器，避免多次触发
                editBox.LostFocus -= (s, e) => FinishRenaming(node, editBox, displayText);
                editBox.KeyDown -= (s, e) => {};

                var newName = editBox.Text.Trim();
                if (!string.IsNullOrWhiteSpace(newName) && newName != node.Name)
                {
                    _treeManager.RenameNode(node, newName);
                }

                // 恢复显示状态
                displayText.Visibility = Visibility.Visible;
                editBox.Visibility = Visibility.Collapsed;

                // 恢复节点状态
                node.State = node.IsSelected ? NodeState.Selected : NodeState.Normal;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"FinishRenaming 异常: {ex.Message}");
                MessageBox.Show($"重命名完成失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消重命名
        /// </summary>
        private void CancelRenaming(BaseNode node, TextBlock displayText, TextBox editBox)
        {
            displayText.Visibility = Visibility.Visible;
            editBox.Visibility = Visibility.Collapsed;

            // 恢复节点状态
            node.State = node.IsSelected ? NodeState.Selected : NodeState.Normal;
        }

        // 收藏夹相关字段
        private readonly List<BaseNode> _favoriteNodes = new();

        /// <summary>
        /// 收藏夹按钮点击事件
        /// </summary>
        private void BtnFavorites_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (favoritesPopup.Visibility == Visibility.Visible)
                {
                    favoritesPopup.Visibility = Visibility.Collapsed;
                    // 切换为空心星
                    favoritesIcon.Text = "☆";
                }
                else
                {
                    RefreshFavoritesList();
                    favoritesPopup.Visibility = Visibility.Visible;
                    // 切换为实心星
                    favoritesIcon.Text = "★";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开收藏夹失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 关闭收藏夹弹窗
        /// </summary>
        private void BtnCloseFavorites_Click(object sender, RoutedEventArgs e)
        {
            favoritesPopup.Visibility = Visibility.Collapsed;
            // 切换为空心星
            favoritesIcon.Text = "☆";
        }

        /// <summary>
        /// 添加到收藏夹
        /// </summary>
        private void AddToFavorites(BaseNode? node = null)
        {
            node ??= _treeManager.SelectedNode;
            if (node == null) return;

            if (!_favoriteNodes.Contains(node))
            {
                _favoriteNodes.Add(node);
                MessageBox.Show($"已将 '{node.Name}' 添加到收藏夹", "收藏夹", MessageBoxButton.OK, MessageBoxImage.Information);
                RefreshFavoritesList();
            }
            else
            {
                MessageBox.Show($"'{node.Name}' 已在收藏夹中", "收藏夹", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 刷新收藏夹列表
        /// </summary>
        private void RefreshFavoritesList()
        {
            try
            {
                favoritesList.ItemsSource = null;
                favoritesList.ItemsSource = _favoriteNodes;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新收藏夹列表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 收藏夹列表选择改变事件
        /// </summary>
        private void FavoritesList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                if (favoritesList.SelectedItem is BaseNode selectedNode)
                {
                    // 跳转到选中的节点
                    _treeManager.SelectedNode = selectedNode;
                    LoadNodeContent(selectedNode);

                    // 关闭收藏夹弹窗
                    favoritesPopup.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"收藏夹选择失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除收藏夹项目
        /// </summary>
        private void RemoveFavorite_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is BaseNode nodeToRemove)
                {
                    _favoriteNodes.Remove(nodeToRemove);
                    RefreshFavoritesList();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"移除收藏失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除选中的节点
        /// </summary>
        private void DeleteSelectedNode()
        {
            if (_treeManager.SelectedNode != null)
            {
                _treeManager.DeleteNode(_treeManager.SelectedNode);
            }
        }

        /// <summary>
        /// 剪切选中的节点
        /// </summary>
        private void CutSelectedNode()
        {
            if (_treeManager.SelectedNode != null)
            {
                _treeManager.CutNode(_treeManager.SelectedNode);
            }
        }

        /// <summary>
        /// 处理复制命令
        /// </summary>
        private void HandleCopyCommand()
        {
            // 新的高级富文本编辑器有自己的复制功能
            // 这里只处理节点复制
            CopySelectedNode();
        }

        /// <summary>
        /// 处理剪切命令
        /// </summary>
        private void HandleCutCommand()
        {
            // 新的高级富文本编辑器有自己的剪切功能
            // 这里只处理节点剪切
            CutSelectedNode();
        }

        /// <summary>
        /// 处理粘贴命令
        /// </summary>
        private void HandlePasteCommand()
        {
            // 检查焦点是否在富文本编辑器中
            if (IsRichTextEditorFocused())
            {
                // HTML编辑器内置粘贴功能，无需特殊处理
                // htmlRichTextEditor 的粘贴功能由浏览器自动处理
            }
            else
            {
                // 否则处理节点粘贴
                PasteNode();
            }
        }

        /// <summary>
        /// 检查焦点是否在富文本编辑器中
        /// </summary>
        private bool IsRichTextEditorFocused()
        {
            var focusedElement = Keyboard.FocusedElement;
            if (focusedElement == null) return false;

            // 检查焦点元素是否是富文本编辑器或其子元素
            var parent = focusedElement as DependencyObject;
            while (parent != null)
            {
                if (parent == richTextEditor)
                {
                    return true;
                }
                parent = VisualTreeHelper.GetParent(parent) ?? LogicalTreeHelper.GetParent(parent);
            }

            return false;
        }

        /// <summary>
        /// 粘贴清理后的内容 - 已禁用，使用新的富文本编辑器
        /// </summary>
        private void PasteCleanContent(TextSelection selection)
        {
            // 新的高级富文本编辑器有自己的粘贴功能
            // 这个方法已经不再使用
        }

        /// <summary>
        /// 清理粘贴文本，移除可能导致嵌套问题的字符
        /// </summary>
        private string CleanPasteText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // 移除可能的控制字符和格式字符
            text = text.Replace("\r\n", "\n").Replace("\r", "\n");

            // 移除可能的表格分隔符
            text = text.Replace("\t", " ");

            // 限制长度，避免过长的内容
            if (text.Length > 10000)
            {
                text = text.Substring(0, 10000);
            }

            return text;
        }

        /// <summary>
        /// 复制选中的节点
        /// </summary>
        private void CopySelectedNode()
        {
            if (_treeManager.SelectedNode != null)
            {
                _treeManager.CopyNode(_treeManager.SelectedNode);
            }
        }

        /// <summary>
        /// 粘贴节点
        /// </summary>
        private void PasteNode()
        {
            _treeManager.PasteNode();
        }

        /// <summary>
        /// 刷新树状视图
        /// </summary>
        private void RefreshTreeView()
        {
            // 重新绑定数据源
            notesTreeView.ItemsSource = null;
            notesTreeView.ItemsSource = _treeManager.RootNodes;
        }

        /// <summary>
        /// 树状控件折叠/展开按钮点击事件
        /// </summary>
        private void BtnToggleTree_Click(object sender, RoutedEventArgs e)
        {
            ToggleTreeContainer();
        }

        /// <summary>
        /// 标题栏拖动事件
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // 双击切换最大化/还原
                BtnMaximize_Click(sender, e);
            }
            else
            {
                // 单击拖动窗口
                this.DragMove();
            }
        }

        /// <summary>
        /// 最小化按钮点击事件
        /// </summary>
        private void BtnMinimize_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// 最大化/还原按钮点击事件
        /// </summary>
        private void BtnMaximize_Click(object sender, RoutedEventArgs e)
        {
            if (WindowState == WindowState.Maximized)
            {
                WindowState = WindowState.Normal;
                if (btnMaximize != null)
                    btnMaximize.ToolTip = "最大化";
            }
            else
            {
                WindowState = WindowState.Maximized;
                if (btnMaximize != null)
                    btnMaximize.ToolTip = "还原";
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 按钮预览鼠标左键按下事件 - 阻止事件冒泡到标题栏
        /// </summary>
        private void Button_PreviewMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 不阻止按钮的正常点击事件，让按钮功能正常工作
            // 这个事件主要是为了标识按钮区域，实际的拖动阻止在按钮的Click事件中处理
        }

        /// <summary>
        /// 切换树状控件的显示/隐藏状态
        /// </summary>
        private void ToggleTreeContainer()
        {
            if (treeContainer == null) return;

            _isTreeExpanded = !_isTreeExpanded;

            if (_isTreeExpanded)
            {
                // 展开：显示树状控件并分配Grid列空间
                treeColumn.Width = new GridLength(280, GridUnitType.Pixel);
                treeContainer.Visibility = Visibility.Visible;
                UpdateTopToolbarCornerRadius(false);
            }
            else
            {
                // 收起：隐藏树状控件并收回Grid列空间
                treeColumn.Width = new GridLength(0, GridUnitType.Pixel);
                treeContainer.Visibility = Visibility.Collapsed;
                UpdateTopToolbarCornerRadius(true);
            }
        }

        /// <summary>
        /// 更新顶部工具栏的圆角设置
        /// </summary>
        /// <param name="isTreeClosed">树状控件是否关闭</param>
        private void UpdateTopToolbarCornerRadius(bool isTreeClosed)
        {
            if (topToolbar != null)
            {
                if (isTreeClosed)
                {
                    // 树状控件关闭时，左上角和右上角都有圆角
                    topToolbar.CornerRadius = new CornerRadius(8, 8, 0, 0);
                }
                else
                {
                    // 树状控件展开时，只有右上角有圆角
                    topToolbar.CornerRadius = new CornerRadius(0, 8, 0, 0);
                }
            }
        }

        // 节点事件处理程序
        private async void OnNodeSelected(object? sender, NodeEventArgs e)
        {
            try
            {
                // 保存之前选中的节点
                if (_currentSelectedNode != null && !_isLoading)
                {
                    // 🔧 删除绘板保存逻辑
                    if (_hasPendingChanges)
                    {
                        UpdateSaveStatus("保存中...");
                        await SaveCurrentNodeAsync();
                        UpdateSaveStatus("已保存");
                    }
                    _hasPendingChanges = false;
                }

                // 加载选中节点的内容
                if (e.Node != null)
                {
                    // 先创建或获取TreeNodeData，确保_currentSelectedNode在LoadNodeContent之前设置
                    if (e.Node.Tag is TreeNodeData existingData)
                    {
                        _currentSelectedNode = existingData;
                    }
                    else
                    {
                        var nodeData = new TreeNodeData
                        {
                            Id = Guid.NewGuid().ToString(),
                            Name = e.Node.Name
                        };
                        e.Node.Tag = nodeData;
                        _currentSelectedNode = nodeData;
                    }

                    // 现在加载节点内容，此时_currentSelectedNode已经正确设置
                    LoadNodeContent(e.Node);

                    // RichTextBox不需要设置节点数据，在插入媒体文件时直接使用_currentSelectedNode
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"节点选择处理失败: {ex.Message}");
                UpdateSaveStatus("保存失败");
            }
        }

        private async void OnNodeCreated(object? sender, NodeEventArgs e)
        {
            try
            {
                // 节点创建事件处理
                RefreshTreeView();

                // 立即创建对应的文件夹结构
                if (e.Node != null)
                {
                    // 创建或获取TreeNodeData
                    TreeNodeData nodeData;
                    if (e.Node.Tag is TreeNodeData existingData)
                    {
                        nodeData = existingData;
                    }
                    else
                    {
                        // 创建新的TreeNodeData并关联到节点
                        nodeData = CreateTreeNodeDataFromUINode(e.Node);
                        e.Node.Tag = nodeData;
                    }

                    // 立即创建文件夹
                    await _saveManager.CreateNodeFolderAsync(nodeData);
                    UpdateSaveStatus($"已创建: {nodeData.Name}");

                    System.Diagnostics.Debug.WriteLine($"节点创建完成: {nodeData.Name} (类型: {e.Node.NodeType})");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"节点创建处理失败: {ex.Message}");
                UpdateSaveStatus("创建失败");
            }
        }

        private void OnNodeDeleted(object? sender, NodeEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"=== 开始删除节点: {e.Node?.Name} ===");

                if (e.Node?.Tag is TreeNodeData nodeData)
                {
                    // 立即释放媒体文件引用（在UI线程上快速执行）
                    ReleaseMediaReferencesQuick(e.Node);

                    // 立即刷新界面（先移除节点显示）
                    RefreshTreeView();
                    RefreshWaterfallAfterDelete();

                    // 异步删除物理文件（避免阻塞UI）
                    _ = Task.Run(() =>
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine("开始异步删除物理文件");
                            bool success = _saveManager.DeleteNode(nodeData);

                            if (success)
                            {
                                System.Diagnostics.Debug.WriteLine($"节点物理文件删除成功: {e.Node.Name}");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"节点物理文件删除失败: {e.Node.Name}");
                            }
                        }
                        catch (Exception deleteEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"异步删除物理文件失败: {deleteEx.Message}");
                        }
                    });

                    System.Diagnostics.Debug.WriteLine($"节点删除处理完成（物理删除异步进行）: {e.Node.Name}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("节点删除失败：节点数据为空");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"节点删除处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放媒体文件引用 - 强化版，确保文件句柄被释放
        /// </summary>
        private void ReleaseMediaReferences(BaseNode? nodeToDelete)
        {
            try
            {
                if (nodeToDelete == null) return;

                System.Diagnostics.Debug.WriteLine($"=== 开始释放节点媒体引用: {nodeToDelete.Name} ===");

                // 递归释放所有子节点的媒体引用
                foreach (var child in nodeToDelete.Children.ToList())
                {
                    ReleaseMediaReferences(child);
                }

                // 🔧 使用核心逻辑，避免代码重复
                ReleaseMediaReferencesCore(nodeToDelete, isQuickMode: false);

                // 清理媒体管理器中的引用（简化版）
                if (nodeToDelete.Tag is TreeNodeData nodeData)
                {
                    try
                    {
                        var nodePath = _saveManager.GetNodePath(nodeData);
                        var attachmentsPath = Path.Combine(nodePath, "附件");

                        if (Directory.Exists(attachmentsPath))
                        {
                            var mediaFiles = Directory.GetFiles(attachmentsPath, "*", SearchOption.AllDirectories);
                            System.Diagnostics.Debug.WriteLine($"准备清理 {mediaFiles.Length} 个媒体文件引用");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"清理媒体管理器引用失败: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"=== 媒体引用释放完成: {nodeToDelete.Name} ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"释放媒体引用失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 快速释放媒体文件引用 - 优化删除性能，复用通用逻辑
        /// </summary>
        private void ReleaseMediaReferencesQuick(BaseNode? nodeToDelete)
        {
            try
            {
                if (nodeToDelete == null) return;

                System.Diagnostics.Debug.WriteLine($"=== 快速释放节点媒体引用: {nodeToDelete.Name} ===");

                // 🔧 复用通用的媒体引用释放逻辑
                ReleaseMediaReferencesCore(nodeToDelete, isQuickMode: true);

                System.Diagnostics.Debug.WriteLine($"=== 快速媒体引用释放完成: {nodeToDelete.Name} ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"快速释放媒体引用失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 核心媒体引用释放逻辑 - 避免代码重复
        /// </summary>
        private void ReleaseMediaReferencesCore(BaseNode nodeToDelete, bool isQuickMode = false)
        {
            // 如果当前选中的是要删除的节点，切换到其他节点
            if (_treeManager.SelectedNode == nodeToDelete)
            {
                var newSelection = FindSafeNode(nodeToDelete);
                _treeManager.SelectedNode = newSelection;
                System.Diagnostics.Debug.WriteLine($"已切换到安全节点: {newSelection?.Name ?? "null"}");
            }

            // 清空瀑布流中的所有图片引用
            if (mediaWrapPanel != null)
            {
                if (!isQuickMode)
                {
                    System.Diagnostics.Debug.WriteLine($"清空瀑布流，当前有 {mediaWrapPanel.Children.Count} 个元素");
                }

                foreach (UIElement child in mediaWrapPanel.Children)
                {
                    if (child is Border border && border.Child is Image image)
                    {
                        image.Source = null; // 释放图片资源
                        if (!isQuickMode)
                        {
                            System.Diagnostics.Debug.WriteLine("释放了一个图片资源");
                        }
                    }
                }
                mediaWrapPanel.Children.Clear();

                if (!isQuickMode)
                {
                    System.Diagnostics.Debug.WriteLine("瀑布流清空完成");
                }
            }

            // 清空RichTextBox编辑器内容
            if (richTextEditor != null)
            {
                try
                {
                    richTextEditor.Document = new FlowDocument(new Paragraph(new Run("开始编写您的内容...")));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"清空编辑器失败: {ex.Message}");
                }
            }

            // 垃圾回收处理
            if (isQuickMode)
            {
                // 异步执行垃圾回收，避免阻塞UI
                _ = Task.Run(() =>
                {
                    try
                    {
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                        System.Diagnostics.Debug.WriteLine("异步垃圾回收完成");
                    }
                    catch (Exception gcEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"异步垃圾回收失败: {gcEx.Message}");
                    }
                });
            }
            else
            {
                // 同步垃圾回收
                System.Diagnostics.Debug.WriteLine("执行强制垃圾回收");
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                Thread.Sleep(300);
            }
        }

        /// <summary>
        /// 找到安全的节点进行切换
        /// </summary>
        private BaseNode? FindSafeNode(BaseNode nodeToDelete)
        {
            try
            {
                // 尝试选择父节点
                if (nodeToDelete.Parent != null)
                {
                    return nodeToDelete.Parent;
                }

                // 尝试选择兄弟节点
                if (nodeToDelete.Parent != null)
                {
                    var siblings = nodeToDelete.Parent.Children.Where(n => n != nodeToDelete).ToList();
                    if (siblings.Count > 0)
                    {
                        return siblings.First();
                    }
                }

                // 尝试选择其他根节点
                var otherRoots = _treeManager.RootNodes.Where(n => n != nodeToDelete).ToList();
                if (otherRoots.Count > 0)
                {
                    return otherRoots.First();
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查找安全节点失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取节点文件夹路径 - 使用SaveManager统一路径
        /// </summary>
        private string GetNodeFolderPath(TreeNodeData nodeData)
        {
            try
            {
                // 🔧 修复：使用SaveManager的统一路径方法，确保路径一致性
                var nodePath = _saveManager.GetNodePath(nodeData);
                System.Diagnostics.Debug.WriteLine($"节点文件夹路径: {nodePath}");
                return nodePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取节点文件夹路径失败: {ex.Message}");
                return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SAVE");
            }
        }


        private async void OnNodeRenamed(object? sender, NodeRenamedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始处理节点重命名: {e.OldName} -> {e.NewName}");

                // 重命名文件系统中的文件夹
                if (e.Node?.Tag is TreeNodeData nodeData)
                {
                    // 更新TreeNodeData的名称
                    nodeData.Name = e.NewName;

                    // 重命名物理文件夹
                    bool renameSuccess = await _saveManager.RenameNodeFolderAsync(nodeData, e.OldName, e.NewName);

                    if (renameSuccess)
                    {
                        System.Diagnostics.Debug.WriteLine($"文件夹重命名成功: {e.OldName} -> {e.NewName}");

                        // 如果有子节点，更新子节点的路径引用
                        if (nodeData.Children.Count > 0)
                        {
                            await _saveManager.UpdateChildNodePathsAsync(nodeData);
                            System.Diagnostics.Debug.WriteLine($"已更新子节点路径: {nodeData.Children.Count} 个子节点");
                        }

                        UpdateSaveStatus($"已重命名: {e.NewName}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"文件夹重命名失败: {e.OldName} -> {e.NewName}");
                        UpdateSaveStatus($"重命名失败: {e.NewName}");

                        // 可以考虑回滚节点名称，但这里先记录错误
                        MessageBox.Show($"文件夹重命名失败，但节点名称已更新。请检查文件系统权限。", "警告",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("节点没有关联的TreeNodeData，跳过文件系统操作");
                }

                System.Diagnostics.Debug.WriteLine($"节点重命名处理完成: {e.NewName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理节点重命名失败: {ex.Message}");
                MessageBox.Show($"重命名处理失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnNodeMoved(object? sender, NodeEventArgs e)
        {
            // 节点移动事件处理
            RefreshTreeView();
        }







        /// <summary>
        /// 删除后刷新瀑布流
        /// </summary>
        private void RefreshWaterfallAfterDelete()
        {
            try
            {
                // 检查是否还有任何节点存在
                var hasAnyNodes = _treeManager.RootNodes.Count > 0;

                if (!hasAnyNodes)
                {
                    // 如果没有任何节点了，隐藏所有内容区域，显示欢迎页面
                    System.Diagnostics.Debug.WriteLine("所有节点已删除，显示欢迎页面");

                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        // 隐藏所有内容区域
                        editorContainer.Visibility = Visibility.Collapsed;
                        codeEditorContainer.Visibility = Visibility.Collapsed;
                        mediaScrollViewer.Visibility = Visibility.Collapsed;

                        // 显示欢迎页面
                        ShowWelcomeScreen();
                    }), System.Windows.Threading.DispatcherPriority.Background);

                    return;
                }

                // 检查当前是否正在显示瀑布流界面
                if (mediaScrollViewer.Visibility == Visibility.Visible)
                {
                    var selectedNode = _treeManager.SelectedNode;

                    if (selectedNode != null)
                    {
                        // 只有当前选中的是父节点（笔记本）或子节点（集合）时才刷新瀑布流
                        if (selectedNode.NodeType == NodeType.Notebook || selectedNode.NodeType == NodeType.Collection)
                        {
                            System.Diagnostics.Debug.WriteLine($"节点删除后立即刷新瀑布流: {selectedNode.Name}");

                            // 延迟一小段时间再刷新，确保文件删除完成
                            Dispatcher.BeginInvoke(new Action(() =>
                            {
                                LoadMediaWaterfallContent(selectedNode);
                            }), System.Windows.Threading.DispatcherPriority.Background);
                        }
                    }
                    else
                    {
                        // 如果没有选中节点（可能当前选中的节点被删除了），清空瀑布流
                        System.Diagnostics.Debug.WriteLine("当前选中节点为空，清空瀑布流");

                        Dispatcher.BeginInvoke(new Action(() =>
                        {
                            mediaWrapPanel.Children.Clear();
                            mediaTitleText.Text = "媒体文件";
                            mediaSubtitleText.Text = "请选择一个节点查看其媒体文件";
                        }), System.Windows.Threading.DispatcherPriority.Background);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除后刷新瀑布流失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否需要刷新瀑布流
        /// </summary>
        private void RefreshWaterfallIfNeeded()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 检查是否需要刷新瀑布流 ===");

                // 检查当前是否正在显示瀑布流界面
                if (mediaScrollViewer.Visibility == Visibility.Visible)
                {
                    System.Diagnostics.Debug.WriteLine("瀑布流界面当前可见");

                    var selectedNode = _treeManager.SelectedNode;
                    if (selectedNode != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"当前选中节点: {selectedNode.Name}, 类型: {selectedNode.NodeType}");

                        // 如果当前选中的是父节点或子节点，直接刷新瀑布流
                        if (selectedNode.NodeType == NodeType.Notebook || selectedNode.NodeType == NodeType.Collection)
                        {
                            System.Diagnostics.Debug.WriteLine($"直接刷新瀑布流: {selectedNode.Name}");
                            LoadMediaWaterfallContent(selectedNode);
                        }
                        // 如果当前选中的是孙节点（页面），需要找到其父节点来刷新瀑布流
                        else if (selectedNode.NodeType == NodeType.Page || selectedNode.NodeType == NodeType.CodePage)
                        {
                            // 找到最近的父节点或子节点来刷新瀑布流
                            var parentNode = selectedNode.Parent;
                            while (parentNode != null)
                            {
                                if (parentNode.NodeType == NodeType.Notebook || parentNode.NodeType == NodeType.Collection)
                                {
                                    System.Diagnostics.Debug.WriteLine($"通过父节点刷新瀑布流: {parentNode.Name}");

                                    // 延迟刷新，确保图片已经保存
                                    Dispatcher.BeginInvoke(new Action(() =>
                                    {
                                        LoadMediaWaterfallContent(parentNode);
                                    }), System.Windows.Threading.DispatcherPriority.Background);

                                    break;
                                }
                                parentNode = parentNode.Parent;
                            }
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("当前没有选中节点");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("瀑布流界面当前不可见，跳过刷新");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新瀑布流失败: {ex.Message}");
            }
        }









        private void ShowNodeProperties(BaseNode node)
        {
            try
            {
                var propertiesDialog = new NodePropertiesDialog(node, (parentNode) => {
                    // 当用户点击父节点时，跳转到该节点
                    _treeManager.SelectedNode = parentNode;

                    // 在树状视图中选中该节点
                    SelectNodeInTreeView(parentNode);

                    // 加载节点内容
                    LoadNodeContent(parentNode);
                    UpdateStatusBarForNode(parentNode);
                });

                propertiesDialog.Owner = this;
                propertiesDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示节点属性失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 在树状视图中选中指定节点
        /// </summary>
        private void SelectNodeInTreeView(BaseNode node)
        {
            try
            {
                // 展开到目标节点的路径
                var pathToNode = new List<BaseNode>();
                var current = node;
                while (current != null)
                {
                    pathToNode.Insert(0, current);
                    current = current.Parent;
                }

                // 展开路径上的所有节点
                foreach (var pathNode in pathToNode.Take(pathToNode.Count - 1))
                {
                    pathNode.IsExpanded = true;
                }

                // 选中目标节点
                node.IsSelected = true;

                // 刷新树状视图
                notesTreeView.UpdateLayout();

                // 滚动到选中的节点
                var treeViewItem = FindTreeViewItem(notesTreeView, node);
                if (treeViewItem != null)
                {
                    treeViewItem.BringIntoView();
                    treeViewItem.Focus();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"选中树状节点失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 查找TreeViewItem
        /// </summary>
        private TreeViewItem? FindTreeViewItem(ItemsControl container, BaseNode node)
        {
            if (container == null || node == null) return null;

            // 处理顶层容器
            for (int i = 0; i < container.Items.Count; i++)
            {
                var item = container.ItemContainerGenerator.ContainerFromIndex(i) as TreeViewItem;
                if (item == null) continue;

                // 检查当前项
                if (item.DataContext == node)
                {
                    return item;
                }

                // 检查子项
                item.UpdateLayout(); // 确保所有子项都已生成
                var childItem = FindTreeViewItem(item, node);
                if (childItem != null)
                {
                    return childItem;
                }
            }

            return null;
        }

        /// <summary>
        /// 在可视化树中查找指定名称的子控件
        /// </summary>
        private T? FindVisualChild<T>(DependencyObject parent, string name) where T : FrameworkElement
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T element && element.Name == name)
                {
                    return element;
                }

                var result = FindVisualChild<T>(child, name);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }













        #region 占位符方法（待实现）

        /// <summary>
        /// 导出笔记本
        /// </summary>
        private void ExportNotebook(NotebookNode? notebook)
        {
            MessageBox.Show("导出功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 导出集合
        /// </summary>
        private void ExportCollection(CollectionNode? collection)
        {
            MessageBox.Show("导出功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 导出页面
        /// </summary>
        private void ExportPage(PageNode? page)
        {
            MessageBox.Show("导出功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // 🔧 删除ExportDrawingBoard方法

        /// <summary>
        /// 移动到其他笔记本
        /// </summary>
        private void MoveToOtherNotebook(BaseNode node)
        {
            MessageBox.Show("移动功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 移动到其他集合
        /// </summary>
        private void MoveToOtherCollection(BaseNode node)
        {
            MessageBox.Show("移动功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 复制到其他集合
        /// </summary>
        private void CopyToOtherCollection(BaseNode node)
        {
            MessageBox.Show("复制功能开发中", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        #endregion

        #region 代码编辑器事件处理

        /// <summary>
        /// 语言选择变化事件
        /// </summary>
        private void LanguageComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_currentCodePageNode != null && languageComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                _currentCodePageNode.Language = selectedItem.Tag?.ToString() ?? "Python";
                UpdateCodeEditorUI();
            }
        }

        /// <summary>
        /// 代码富文本变化事件
        /// </summary>
        private void CodeRichTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_currentCodePageNode != null)
            {
                _currentCodePageNode.Code = GetPlainTextFromRichTextBox();
            }

            // 更新行号和状态栏
            UpdateLineNumbers();
            UpdateCodeStatusBar();

            // 通知语法高亮器文本已变化
            _syntaxHighlighter?.RequestHighlight();
        }

        /// <summary>
        /// 代码编辑器滚动事件
        /// </summary>
        private void CodeRichTextBox_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            // 同步行号区域的滚动
            SyncLineNumberScroll();
        }

        /// <summary>
        /// Python运行按钮点击事件
        /// </summary>
        private void BtnRunPython_Click(object sender, RoutedEventArgs e)
        {
            if (_currentCodePageNode == null) return;

            try
            {
                string code = GetPlainTextFromRichTextBox();

                if (string.IsNullOrWhiteSpace(code))
                {
                    MessageBox.Show("请输入Python代码", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 基础的Python代码运行模拟
                RunPythonCodeSimulation(code);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"运行错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// HTML预览按钮点击事件
        /// </summary>
        private void BtnPreviewHtml_Click(object sender, RoutedEventArgs e)
        {
            if (_currentCodePageNode == null) return;

            try
            {
                string code = GetPlainTextFromRichTextBox();
                string language = _currentCodePageNode.Language;

                if (language == "HTML")
                {
                    PreviewHtmlInBrowser(code);
                }
                else
                {
                    MessageBox.Show("请选择HTML语言类型", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"预览错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        /// <summary>
        /// 更新代码编辑器UI
        /// </summary>
        private void UpdateCodeEditorUI()
        {
            if (_currentCodePageNode == null) return;

            string language = _currentCodePageNode.Language;

            // 根据语言类型显示/隐藏相应的按钮
            btnRunPython.Visibility = language == "Python" ? Visibility.Visible : Visibility.Collapsed;
            btnPreviewHtml.Visibility = language == "HTML" ? Visibility.Visible : Visibility.Collapsed;

            // 更新语法高亮器的语言设置
            _syntaxHighlighter?.SetLanguage(language);

            // 更新行号和状态栏
            UpdateLineNumbers();
            UpdateCodeStatusBar();
        }

        /// <summary>
        /// 更新行号显示
        /// </summary>
        private void UpdateLineNumbers()
        {
            if (codeRichTextBox == null || lineNumbersTextBlock == null) return;

            try
            {
                string text = GetPlainTextFromRichTextBox();
                int lineCount = string.IsNullOrEmpty(text) ? 1 : text.Split('\n').Length;

                var lineNumbers = new System.Text.StringBuilder();
                for (int i = 1; i <= lineCount; i++)
                {
                    lineNumbers.AppendLine(i.ToString());
                }

                lineNumbersTextBlock.Text = lineNumbers.ToString().TrimEnd();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新行号错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 同步行号区域的滚动
        /// </summary>
        private void SyncLineNumberScroll()
        {
            if (codeRichTextBox == null || lineNumberScrollViewer == null) return;

            try
            {
                // 获取代码编辑器的滚动位置
                var scrollViewer = GetScrollViewer(codeRichTextBox);
                if (scrollViewer != null)
                {
                    // 同步垂直滚动位置
                    lineNumberScrollViewer.ScrollToVerticalOffset(scrollViewer.VerticalOffset);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"同步行号滚动错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取控件内部的ScrollViewer
        /// </summary>
        private ScrollViewer? GetScrollViewer(DependencyObject element)
        {
            if (element is ScrollViewer scrollViewer)
                return scrollViewer;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(element); i++)
            {
                var child = VisualTreeHelper.GetChild(element, i);
                var result = GetScrollViewer(child);
                if (result != null)
                    return result;
            }

            return null;
        }





        /// <summary>
        /// 在外部浏览器中预览HTML代码
        /// </summary>
        private void PreviewHtmlInBrowser(string htmlCode)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(htmlCode))
                {
                    MessageBox.Show("请输入HTML代码", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 创建临时HTML文件
                string tempPath = System.IO.Path.GetTempPath();
                string fileName = $"preview_{DateTime.Now:yyyyMMdd_HHmmss}.html";
                string filePath = System.IO.Path.Combine(tempPath, fileName);

                // 写入HTML内容
                System.IO.File.WriteAllText(filePath, htmlCode, System.Text.Encoding.UTF8);

                // 在默认浏览器中打开
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                });

                // 不显示成功消息，直接打开浏览器
            }
            catch (Exception ex)
            {
                MessageBox.Show($"HTML预览错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 初始化语法高亮器
        /// </summary>
        private void InitializeSyntaxHighlighter()
        {
            // 释放旧的高亮器
            _syntaxHighlighter?.Dispose();

            // 创建新的高性能语法高亮器
            _syntaxHighlighter = new SyntaxHighlighter(codeRichTextBox);

            // 设置当前语言
            if (_currentCodePageNode != null)
            {
                _syntaxHighlighter.SetLanguage(_currentCodePageNode.Language);
            }
        }



        /// <summary>
        /// 从RichTextBox获取纯文本
        /// </summary>
        private string GetPlainTextFromRichTextBox()
        {
            if (codeRichTextBox?.Document == null) return "";

            var textRange = new TextRange(codeRichTextBox.Document.ContentStart, codeRichTextBox.Document.ContentEnd);
            return textRange.Text;
        }

        /// <summary>
        /// 将代码加载到RichTextBox
        /// </summary>
        private void LoadCodeIntoRichTextBox(string code)
        {
            if (codeRichTextBox?.Document == null) return;

            try
            {
                // 暂时禁用事件
                codeRichTextBox.TextChanged -= CodeRichTextBox_TextChanged;

                codeRichTextBox.Document.Blocks.Clear();

                if (string.IsNullOrEmpty(code))
                {
                    codeRichTextBox.Document.Blocks.Add(new Paragraph());
                }
                else
                {
                    var paragraph = new Paragraph();
                    paragraph.Margin = new Thickness(0);
                    paragraph.Inlines.Add(new Run(code));
                    codeRichTextBox.Document.Blocks.Add(paragraph);
                }

                // 重新启用事件
                codeRichTextBox.TextChanged += CodeRichTextBox_TextChanged;

                // 通知语法高亮器开始高亮
                _syntaxHighlighter?.RequestHighlight();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载代码错误: {ex.Message}");
                // 确保重新启用事件
                codeRichTextBox.TextChanged += CodeRichTextBox_TextChanged;
            }
        }

        /// <summary>
        /// 更新代码页状态栏信息
        /// </summary>
        private void UpdateCodeStatusBar()
        {
            if (_currentCodePageNode == null) return;

            try
            {
                string text = GetPlainTextFromRichTextBox();
                int lineCount = string.IsNullOrEmpty(text) ? 1 : text.Split('\n').Length;
                int charCount = text?.Length ?? 0;

                if (statusLineCount != null)
                    statusLineCount.Text = $"行数: {lineCount}";

                if (statusCharCount != null)
                    statusCharCount.Text = $"字符数: {charCount}";

                if (statusLanguage != null)
                    statusLanguage.Text = $"语言: {_currentCodePageNode.Language}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新代码页状态栏错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 真实的Python代码运行
        /// </summary>
        private void RunPythonCodeSimulation(string code)
        {
            try
            {
                // 检查Python是否安装
                if (!IsPythonInstalled())
                {
                    var result = MessageBox.Show(
                        "未检测到Python环境。\n\n" +
                        "要运行Python代码，请先安装Python：\n" +
                        "1. 访问 https://python.org 下载Python\n" +
                        "2. 安装时勾选 'Add Python to PATH'\n" +
                        "3. 重启应用程序\n\n" +
                        "是否继续显示代码内容？",
                        "Python环境未找到",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        ShowCodeContent(code);
                    }
                    return;
                }

                // 执行Python代码
                ExecutePythonCode(code);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Python运行错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 检查Python是否已安装
        /// </summary>
        private bool IsPythonInstalled()
        {
            try
            {
                var process = new System.Diagnostics.Process
                {
                    StartInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "python",
                        Arguments = "--version",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                process.WaitForExit(3000); // 等待3秒
                return process.ExitCode == 0;
            }
            catch
            {
                // 尝试python3命令
                try
                {
                    var process = new System.Diagnostics.Process
                    {
                        StartInfo = new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = "python3",
                            Arguments = "--version",
                            UseShellExecute = false,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            CreateNoWindow = true
                        }
                    };

                    process.Start();
                    process.WaitForExit(3000);
                    return process.ExitCode == 0;
                }
                catch
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// 执行Python代码
        /// </summary>
        private void ExecutePythonCode(string code)
        {
            try
            {
                // 创建临时Python文件
                string tempPath = System.IO.Path.GetTempPath();
                string fileName = $"temp_python_{DateTime.Now:yyyyMMdd_HHmmss}.py";
                string filePath = System.IO.Path.Combine(tempPath, fileName);

                // 写入代码到临时文件
                System.IO.File.WriteAllText(filePath, code, System.Text.Encoding.UTF8);

                // 执行Python文件
                var process = new System.Diagnostics.Process
                {
                    StartInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = GetPythonCommand(),
                        Arguments = $"\"{filePath}\"",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true,
                        StandardOutputEncoding = System.Text.Encoding.UTF8,
                        StandardErrorEncoding = System.Text.Encoding.UTF8
                    }
                };

                var output = new System.Text.StringBuilder();
                var error = new System.Text.StringBuilder();

                process.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        output.AppendLine(e.Data);
                    }
                };

                process.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                    {
                        error.AppendLine(e.Data);
                    }
                };

                process.Start();
                process.BeginOutputReadLine();
                process.BeginErrorReadLine();

                // 等待执行完成，最多10秒
                bool finished = process.WaitForExit(10000);

                if (!finished)
                {
                    process.Kill();
                    MessageBox.Show("Python代码执行超时（超过10秒），已终止执行。", "执行超时", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 清理临时文件
                try
                {
                    System.IO.File.Delete(filePath);
                }
                catch { }

                // 显示结果
                ShowPythonResult(code, output.ToString(), error.ToString(), process.ExitCode);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"执行Python代码时发生错误: {ex.Message}", "执行错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取Python命令
        /// </summary>
        private string GetPythonCommand()
        {
            // 优先尝试python命令
            try
            {
                var process = new System.Diagnostics.Process
                {
                    StartInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = "python",
                        Arguments = "--version",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }
                };
                process.Start();
                process.WaitForExit(1000);
                if (process.ExitCode == 0)
                {
                    return "python";
                }
            }
            catch { }

            // 尝试python3命令
            return "python3";
        }

        /// <summary>
        /// 显示Python运行结果
        /// </summary>
        private void ShowPythonResult(string code, string output, string error, int exitCode)
        {
            var result = new System.Text.StringBuilder();
            result.AppendLine("=== Python代码执行结果 ===");
            result.AppendLine();

            if (exitCode == 0)
            {
                result.AppendLine("✅ 执行成功");
            }
            else
            {
                result.AppendLine($"❌ 执行失败 (退出码: {exitCode})");
            }

            result.AppendLine();
            result.AppendLine("📝 代码内容:");
            result.AppendLine("```python");
            result.AppendLine(code);
            result.AppendLine("```");
            result.AppendLine();

            if (!string.IsNullOrWhiteSpace(output))
            {
                result.AppendLine("📤 输出结果:");
                result.AppendLine(output.Trim());
                result.AppendLine();
            }

            if (!string.IsNullOrWhiteSpace(error))
            {
                result.AppendLine("⚠️ 错误信息:");
                result.AppendLine(error.Trim());
                result.AppendLine();
            }

            if (string.IsNullOrWhiteSpace(output) && string.IsNullOrWhiteSpace(error) && exitCode == 0)
            {
                result.AppendLine("✨ 代码执行完成，无输出内容。");
            }

            MessageBox.Show(result.ToString(), "Python执行结果", MessageBoxButton.OK,
                exitCode == 0 ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }

        /// <summary>
        /// 显示代码内容（当Python未安装时）
        /// </summary>
        private void ShowCodeContent(string code)
        {
            var result = new System.Text.StringBuilder();
            result.AppendLine("=== Python代码内容 ===");
            result.AppendLine();
            result.AppendLine("📝 代码:");
            result.AppendLine("```python");
            result.AppendLine(code);
            result.AppendLine("```");
            result.AppendLine();
            result.AppendLine("💡 提示：安装Python后可以直接运行此代码。");

            MessageBox.Show(result.ToString(), "代码内容", MessageBoxButton.OK, MessageBoxImage.Information);
        }















        /// <summary>
        /// 窗口关闭时清理资源
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // 执行最后一次保存
                if (_currentSelectedNode != null && _hasPendingChanges)
                {
                    System.Diagnostics.Debug.WriteLine("程序关闭，执行最后一次保存");
                    SaveCurrentNodeAsync().Wait(TimeSpan.FromSeconds(5));
                    _hasPendingChanges = false;
                }

                // 取消事件订阅
                UnsubscribeFromRealtimeSaveEvents();

                // 清理语法高亮器资源
                _syntaxHighlighter?.Dispose();

                System.Diagnostics.Debug.WriteLine("程序关闭，资源清理完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"程序关闭清理失败: {ex.Message}");
            }

            base.OnClosed(e);
        }

        #endregion

        #region 自动保存和加载

        /// <summary>
        /// 初始化保存机制
        /// </summary>
        private void InitializeAutoSave()
        {
            // 只在节点切换和程序关闭时保存
            System.Diagnostics.Debug.WriteLine("节点切换保存模式已启用");
        }

        /// <summary>
        /// 标记内容已变化
        /// </summary>
        private void MarkContentChanged()
        {
            if (_isLoading) return;
            _hasPendingChanges = true;
            System.Diagnostics.Debug.WriteLine("内容已变化，将在节点切换或程序关闭时保存");
        }

        /// <summary>
        /// 保存当前节点
        /// </summary>
        private async Task SaveCurrentNodeAsync()
        {
            try
            {
                if (_currentSelectedNode == null) return;

                UpdateSaveStatus("保存中...");
                var nodeData = _currentSelectedNode;

                // 根据当前显示的内容类型进行保存
                if (editorContainer.Visibility == Visibility.Visible)
                {
                    // 使用新的文档图片管理器保存
                    try
                    {
                        // 保存RichTextBox内容为RTF格式
                        var rtfContent = GetRichTextBoxContent();
                        var documentPath = _saveManager.GetDocumentPath(nodeData);
                        await File.WriteAllTextAsync(documentPath, rtfContent);

                        System.Diagnostics.Debug.WriteLine($"使用新管理器保存文档: {nodeData.Name}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"新管理器保存失败: {ex.Message}");
                        UpdateSaveStatus("保存失败");
                    }
                }
                else if (codeEditorContainer.Visibility == Visibility.Visible && _currentCodePageNode != null)
                {
                    // 保存代码内容
                    var codeContent = _currentCodePageNode.Code;
                    var language = _currentCodePageNode.Language;
                    await _saveManager.SaveCodeAsync(nodeData, codeContent, language);
                }
                // 🔧 删除绘板保存逻辑

                System.Diagnostics.Debug.WriteLine($"实时保存完成: {nodeData.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存当前节点失败: {ex.Message}");
                UpdateSaveStatus("保存失败");
                throw; // 重新抛出异常以便上层处理
            }
        }

        /// <summary>
        /// 更新保存状态显示
        /// </summary>
        private void UpdateSaveStatus(string status)
        {
            try
            {
                // 在状态栏显示保存状态
                if (statusBarItem?.Content is TextBlock statusTextBlock)
                {
                    statusTextBlock.Text = status;

                    // 如果是成功状态，2秒后清除
                    if (status == "已保存")
                    {
                        var timer = new System.Windows.Threading.DispatcherTimer
                        {
                            Interval = TimeSpan.FromSeconds(2)
                        };
                        timer.Tick += (s, e) =>
                        {
                            timer.Stop();
                            if (statusBarItem?.Content is TextBlock tb)
                                tb.Text = "准备就绪";
                        };
                        timer.Start();
                    }
                }

                // 同时在调试输出中显示
                System.Diagnostics.Debug.WriteLine($"保存状态: {status}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新保存状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前代码语言
        /// </summary>
        private string GetCurrentCodeLanguage()
        {
            // 这里可以根据当前选择的语言返回
            return "csharp"; // 默认返回C#
        }

        // 🔧 删除GetDrawingBoardData方法



        // 🔧 删除SaveCurrentDrawingBoardAsync方法

        // 🔧 删除GetDrawingBoardDataFromCurrentInstance方法

        /// <summary>
        /// 加载保存的数据
        /// </summary>
        private async void LoadSavedData()
        {
            try
            {
                _isLoading = true;
                System.Diagnostics.Debug.WriteLine("开始加载保存的数据");

                var savedNodes = await _saveManager.LoadAllNodesAsync();

                if (savedNodes.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"找到 {savedNodes.Count} 个保存的根节点");

                    // 添加加载的节点到树管理器
                    foreach (var nodeData in savedNodes)
                    {
                        await LoadNodeHierarchy(nodeData, null);
                    }

                    System.Diagnostics.Debug.WriteLine("数据加载完成");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("没有找到保存的数据，显示空树");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载保存数据失败: {ex.Message}");
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// 递归加载节点层级
        /// </summary>
        private async Task LoadNodeHierarchy(TreeNodeData nodeData, BaseNode? parentNode)
        {
            try
            {
                // 根据节点深度确定节点类型
                BaseNode uiNode;
                int depth = nodeData.GetDepth();

                if (depth == 0)
                {
                    // 根节点 - 笔记本
                    uiNode = new NotebookNode { Name = nodeData.Name };
                }
                else if (depth == 1)
                {
                    // 第一级子节点 - 集合
                    uiNode = new CollectionNode { Name = nodeData.Name };
                }
                else
                {
                    // 第二级及以下 - 页面
                    // 根据保存的内容类型确定页面类型
                    var (content, nodeType) = await _saveManager.LoadNodeContentAsync(nodeData);

                    switch (nodeType)
                    {
                        case "Code":
                            uiNode = new CodePageNode { Name = nodeData.Name };
                            break;
                        // 🔧 删除绘板节点类型
                        default:
                            uiNode = new PageNode { Name = nodeData.Name };
                            break;
                    }
                }

                // 设置节点数据
                uiNode.Tag = nodeData;

                // 添加到树中
                if (parentNode == null)
                {
                    _treeManager.AddRootNode(uiNode);
                }
                else
                {
                    _treeManager.AddChildNode(parentNode, uiNode);
                }

                // 确保TreeNodeData的父子关系正确
                if (parentNode?.Tag is TreeNodeData parentTreeData && !parentTreeData.Children.Contains(nodeData))
                {
                    parentTreeData.Children.Add(nodeData);
                    nodeData.Parent = parentTreeData;
                }

                // 递归加载子节点
                foreach (var childData in nodeData.Children)
                {
                    await LoadNodeHierarchy(childData, uiNode);
                }

                System.Diagnostics.Debug.WriteLine($"节点加载完成: {nodeData.Name} (深度: {depth})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载节点层级失败: {nodeData.Name}, {ex.Message}");
            }
        }

        /// <summary>
        /// 获取父节点数据
        /// </summary>
        private TreeNodeData? GetParentNodeData(BaseNode node)
        {
            try
            {
                var parentNode = _treeManager.GetParentNode(node);
                return parentNode?.Tag as TreeNodeData;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 从UI节点创建TreeNodeData
        /// </summary>
        private TreeNodeData CreateTreeNodeDataFromUINode(BaseNode uiNode)
        {
            try
            {
                // 获取父节点数据
                var parentData = GetParentNodeData(uiNode);

                var nodeData = new TreeNodeData
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = uiNode.Name,
                    Parent = parentData,
                    CreatedTime = DateTime.Now,
                    LastModified = DateTime.Now
                };

                // 如果有父节点，添加到父节点的子节点列表
                if (parentData != null)
                {
                    parentData.Children.Add(nodeData);
                }

                System.Diagnostics.Debug.WriteLine($"创建TreeNodeData: {nodeData.Name} (深度: {nodeData.GetDepth()})");
                return nodeData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建TreeNodeData失败: {ex.Message}");
                return new TreeNodeData { Id = Guid.NewGuid().ToString(), Name = uiNode.Name };
            }
        }

        /// <summary>
        /// 内容变化事件处理 - 标记内容已变化
        /// </summary>
        private void OnContentChanged(object? sender, EventArgs e)
        {
            if (_isLoading) return;
            MarkContentChanged();
        }

        /// <summary>
        /// 手动触发保存 - Ctrl+S快捷键
        /// </summary>
        public async void TestSave()
        {
            try
            {
                UpdateSaveStatus("手动保存中...");
                await SaveCurrentNodeAsync();
                _hasPendingChanges = false;
                UpdateSaveStatus("手动保存完成");
                System.Diagnostics.Debug.WriteLine("手动保存完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"手动保存失败: {ex.Message}");
                UpdateSaveStatus("手动保存失败");
            }
        }

        /// <summary>
        /// 订阅实时保存相关事件
        /// </summary>
        private void SubscribeToRealtimeSaveEvents()
        {
            try
            {
                // 取消之前的订阅，避免重复
                UnsubscribeFromRealtimeSaveEvents();

                // RichTextBox的事件已在XAML中绑定，无需重复订阅

                System.Diagnostics.Debug.WriteLine("实时保存事件订阅完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"订阅实时保存事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消订阅实时保存相关事件
        /// </summary>
        private void UnsubscribeFromRealtimeSaveEvents()
        {
            try
            {
                // RichTextBox的事件在XAML中绑定，无需手动取消订阅
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"取消订阅实时保存事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 媒体插入事件处理
        /// </summary>
        private void OnMediaInserted(object? sender, EventArgs e)
        {
            if (_isLoading) return;
            MarkContentChanged();

            // 刷新瀑布流，显示新插入的媒体文件
            RefreshWaterfallIfNeeded();
        }

        /// <summary>
        /// 表格修改事件处理
        /// </summary>
        private void OnTableModified(object? sender, EventArgs e)
        {
            if (_isLoading) return;
            MarkContentChanged();
        }

        /// <summary>
        /// 格式化变化事件处理
        /// </summary>
        private void OnFormattingChanged(object? sender, EventArgs e)
        {
            if (_isLoading) return;
            MarkContentChanged();
        }

        #endregion

        #region RichTextBox核心功能方法

        /// <summary>
        /// 更新工具栏状态
        /// </summary>
        private void UpdateToolbarState()
        {
            if (richTextEditor?.Selection == null) return;

            try
            {
                // 这里可以根据当前选择的格式更新工具栏按钮状态
                // 例如：检查是否为粗体、斜体等
                var selection = richTextEditor.Selection;

                // 获取当前选择的格式属性
                var fontWeight = selection.GetPropertyValue(TextElement.FontWeightProperty);
                var fontStyle = selection.GetPropertyValue(TextElement.FontStyleProperty);
                var textDecorations = selection.GetPropertyValue(Inline.TextDecorationsProperty);

                // 这里可以更新工具栏按钮的选中状态
                System.Diagnostics.Debug.WriteLine($"当前格式 - 粗体: {fontWeight}, 斜体: {fontStyle}, 装饰: {textDecorations}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新工具栏状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换粗体格式
        /// </summary>
        private void ToggleBold()
        {
            if (richTextEditor?.Selection != null)
            {
                EditingCommands.ToggleBold.Execute(null, richTextEditor);
            }
        }

        /// <summary>
        /// 切换斜体格式
        /// </summary>
        private void ToggleItalic()
        {
            if (richTextEditor?.Selection != null)
            {
                EditingCommands.ToggleItalic.Execute(null, richTextEditor);
            }
        }

        /// <summary>
        /// 切换下划线格式
        /// </summary>
        private void ToggleUnderline()
        {
            if (richTextEditor?.Selection != null)
            {
                EditingCommands.ToggleUnderline.Execute(null, richTextEditor);
            }
        }

        /// <summary>
        /// 向RichTextBox插入图片
        /// </summary>
        private void InsertImageToRichTextBox(string imagePath)
        {
            try
            {
                if (!File.Exists(imagePath)) return;

                // 复制图片到节点附件文件夹
                if (_currentSelectedNode == null) return;

                var nodeFolder = _saveManager.GetNodePath(_currentSelectedNode);
                var attachmentFolder = Path.Combine(nodeFolder, "附件");
                Directory.CreateDirectory(attachmentFolder);

                var fileName = Path.GetFileName(imagePath);
                var targetPath = Path.Combine(attachmentFolder, fileName);

                // 如果文件已存在，生成新名称
                int counter = 1;
                while (File.Exists(targetPath))
                {
                    var nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                    var extension = Path.GetExtension(fileName);
                    fileName = $"{nameWithoutExt}_{counter}{extension}";
                    targetPath = Path.Combine(attachmentFolder, fileName);
                    counter++;
                }

                File.Copy(imagePath, targetPath, true);

                // 创建图片元素，优先使用原始尺寸
                var bitmapImage = new BitmapImage(new Uri(targetPath));

                // 计算合适的显示尺寸
                double displayWidth = bitmapImage.PixelWidth;
                double displayHeight = bitmapImage.PixelHeight;

                // 只对过大的图片进行缩放，保持宽高比
                const double maxWidth = 800;
                const double maxHeight = 600;

                if (displayWidth > maxWidth || displayHeight > maxHeight)
                {
                    double scaleX = maxWidth / displayWidth;
                    double scaleY = maxHeight / displayHeight;
                    double scale = Math.Min(scaleX, scaleY);

                    displayWidth = displayWidth * scale;
                    displayHeight = displayHeight * scale;
                }

                var image = new System.Windows.Controls.Image
                {
                    Source = bitmapImage,
                    Width = displayWidth,
                    Height = displayHeight,
                    Stretch = Stretch.Uniform,
                    Tag = targetPath // 保存图片路径用于右键编辑
                };

                // 为图片添加右键菜单
                AddImageContextMenu(image);

                // 将图片包装在InlineUIContainer中
                var container = new InlineUIContainer(image);

                // 插入到当前光标位置
                richTextEditor.CaretPosition.Paragraph?.Inlines.Add(container);

                System.Diagnostics.Debug.WriteLine($"图片插入成功: {fileName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入图片失败: {ex.Message}");
                MessageBox.Show($"插入图片失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 向RichTextBox插入表格
        /// </summary>
        private void InsertTableToRichTextBox(int rows, int columns)
        {
            try
            {
                var table = new Table();

                // 设置Figma风格表格样式
                table.CellSpacing = 0;
                table.Background = Brushes.White;
                table.BorderBrush = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED)); // #E8EAED
                table.BorderThickness = new Thickness(1);

                // 创建列
                for (int i = 0; i < columns; i++)
                {
                    table.Columns.Add(new TableColumn { Width = new GridLength(100) });
                }

                // 创建行组
                var rowGroup = new TableRowGroup();

                // 创建行和单元格
                for (int i = 0; i < rows; i++)
                {
                    var row = new TableRow();

                    for (int j = 0; j < columns; j++)
                    {
                        var cellParagraph = new Paragraph(new Run($"单元格 {i + 1},{j + 1}"))
                        {
                            Margin = new Thickness(0),
                            FontFamily = new FontFamily("Microsoft YaHei"),
                            FontSize = 14
                        };

                        var cell = new TableCell(cellParagraph)
                        {
                            BorderBrush = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED)), // #E8EAED
                            BorderThickness = new Thickness(0.5, 0.5, 0.5, 0.5),
                            Padding = new Thickness(12, 8, 12, 8),
                            Background = i == 0 ? new SolidColorBrush(Color.FromRgb(0xF8, 0xF9, 0xFA)) : Brushes.White // 表头背景
                        };

                        // 为表头设置粗体
                        if (i == 0)
                        {
                            cellParagraph.FontWeight = FontWeights.SemiBold;
                        }

                        row.Cells.Add(cell);
                    }

                    rowGroup.Rows.Add(row);
                }

                table.RowGroups.Add(rowGroup);

                // 为表格添加右键菜单
                AddTableContextMenu(table);

                // 插入表格到文档
                richTextEditor.Document.Blocks.Add(table);

                System.Diagnostics.Debug.WriteLine($"表格插入成功: {rows}x{columns}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入表格失败: {ex.Message}");
                MessageBox.Show($"插入表格失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 向RichTextBox插入文本
        /// </summary>
        private void InsertTextToRichTextBox(string text)
        {
            try
            {
                if (richTextEditor != null && !string.IsNullOrEmpty(text))
                {
                    richTextEditor.CaretPosition.InsertTextInRun(text);
                    richTextEditor.Focus();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入文本失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 向RichTextBox插入列表
        /// </summary>
        private void InsertListToRichTextBox(bool isOrdered)
        {
            try
            {
                if (richTextEditor != null)
                {
                    var list = new List();
                    list.MarkerStyle = isOrdered ? TextMarkerStyle.Decimal : TextMarkerStyle.Disc;

                    // 创建几个示例列表项
                    for (int i = 1; i <= 3; i++)
                    {
                        var listItem = new ListItem(new Paragraph(new Run($"列表项 {i}")));
                        list.ListItems.Add(listItem);
                    }

                    // 插入列表到文档
                    richTextEditor.Document.Blocks.Add(list);

                    System.Diagnostics.Debug.WriteLine($"列表插入成功: {(isOrdered ? "有序" : "无序")}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"插入列表失败: {ex.Message}");
                MessageBox.Show($"插入列表失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载内容到RichTextBox
        /// </summary>
        private void LoadContentToRichTextBox(string content)
        {
            try
            {
                if (richTextEditor == null) return;

                if (string.IsNullOrEmpty(content))
                {
                    richTextEditor.Document = new FlowDocument(new Paragraph(new Run("开始编写您的内容...")));
                    return;
                }

                // 如果是RTF内容
                if (content.StartsWith("{\\rtf"))
                {
                    // 创建新的FlowDocument
                    var newDocument = new FlowDocument();
                    var textRange = new TextRange(newDocument.ContentStart, newDocument.ContentEnd);

                    using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(content)))
                    {
                        textRange.Load(stream, DataFormats.Rtf);
                    }

                    // 设置新文档到RichTextBox
                    richTextEditor.Document = newDocument;
                }
                else
                {
                    // 纯文本内容
                    richTextEditor.Document = new FlowDocument(new Paragraph(new Run(content)));
                }

                System.Diagnostics.Debug.WriteLine("内容加载到RichTextBox成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载内容到RichTextBox失败: {ex.Message}");
                richTextEditor.Document = new FlowDocument(new Paragraph(new Run("加载内容失败，请重试")));
            }
        }

        /// <summary>
        /// 获取RichTextBox的内容
        /// </summary>
        private string GetRichTextBoxContent()
        {
            try
            {
                if (richTextEditor == null) return "";

                var textRange = new TextRange(richTextEditor.Document.ContentStart, richTextEditor.Document.ContentEnd);
                using (var stream = new MemoryStream())
                {
                    textRange.Save(stream, DataFormats.Rtf);
                    return Encoding.UTF8.GetString(stream.ToArray());
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取RichTextBox内容失败: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 处理RichTextBox中的图片路径
        /// </summary>
        private void ProcessImagePathsInRichTextBox(TreeNodeData nodeData)
        {
            try
            {
                if (richTextEditor?.Document == null) return;

                var nodeFolder = _saveManager.GetNodePath(nodeData);
                var attachmentFolder = Path.Combine(nodeFolder, "附件");

                // 遍历文档中的所有图片元素
                foreach (var block in richTextEditor.Document.Blocks)
                {
                    ProcessBlockForImages(block, attachmentFolder);
                }

                System.Diagnostics.Debug.WriteLine("图片路径处理完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理图片路径失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归处理Block中的图片元素
        /// </summary>
        private void ProcessBlockForImages(Block block, string attachmentFolder)
        {
            try
            {
                if (block is Paragraph paragraph)
                {
                    foreach (var inline in paragraph.Inlines)
                    {
                        if (inline is InlineUIContainer container && container.Child is System.Windows.Controls.Image image)
                        {
                            // 处理图片路径
                            ProcessImageElement(image, attachmentFolder);
                        }
                    }
                }
                else if (block is List list)
                {
                    foreach (var listItem in list.ListItems)
                    {
                        foreach (var listBlock in listItem.Blocks)
                        {
                            ProcessBlockForImages(listBlock, attachmentFolder);
                        }
                    }
                }
                else if (block is Table table)
                {
                    foreach (var rowGroup in table.RowGroups)
                    {
                        foreach (var row in rowGroup.Rows)
                        {
                            foreach (var cell in row.Cells)
                            {
                                foreach (var cellBlock in cell.Blocks)
                                {
                                    ProcessBlockForImages(cellBlock, attachmentFolder);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理Block图片失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理单个图片元素
        /// </summary>
        private void ProcessImageElement(System.Windows.Controls.Image image, string attachmentFolder)
        {
            try
            {
                if (image.Source is BitmapImage bitmapImage)
                {
                    var originalPath = bitmapImage.UriSource?.LocalPath;
                    if (!string.IsNullOrEmpty(originalPath) && File.Exists(originalPath))
                    {
                        var fileName = Path.GetFileName(originalPath);
                        var targetPath = Path.Combine(attachmentFolder, fileName);

                        // 如果图片不在附件文件夹中，复制过去
                        if (!originalPath.StartsWith(attachmentFolder, StringComparison.OrdinalIgnoreCase))
                        {
                            Directory.CreateDirectory(attachmentFolder);

                            // 如果目标文件已存在，生成新名称
                            int counter = 1;
                            while (File.Exists(targetPath))
                            {
                                var nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                                var extension = Path.GetExtension(fileName);
                                fileName = $"{nameWithoutExt}_{counter}{extension}";
                                targetPath = Path.Combine(attachmentFolder, fileName);
                                counter++;
                            }

                            File.Copy(originalPath, targetPath, true);

                            // 更新图片源为新路径
                            image.Source = new BitmapImage(new Uri(targetPath));

                            System.Diagnostics.Debug.WriteLine($"图片已复制到附件文件夹: {fileName}");
                        }

                        // 设置图片路径标签和合理的尺寸限制
                        image.Tag = targetPath;

                        // 如果图片没有设置尺寸或尺寸过大，设置合理的默认尺寸
                        if (double.IsNaN(image.Width) || image.Width > 800)
                        {
                            image.Width = Math.Min(400, bitmapImage.PixelWidth);
                        }
                        if (double.IsNaN(image.Height) || image.Height > 600)
                        {
                            image.Height = Math.Min(300, bitmapImage.PixelHeight);
                        }

                        // 确保图片有右键菜单
                        if (image.ContextMenu == null)
                        {
                            AddImageContextMenu(image);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理图片元素失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 为图片添加右键菜单
        /// </summary>
        private void AddImageContextMenu(System.Windows.Controls.Image image)
        {
            try
            {
                var contextMenu = new ContextMenu();

                // 应用Figma风格
                if (TryFindResource("FigmaContextMenuStyle") is Style contextMenuStyle)
                {
                    contextMenu.Style = contextMenuStyle;
                }

                // 编辑图片尺寸
                var editSizeItem = new MenuItem
                {
                    Header = "编辑图片尺寸(_S)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                editSizeItem.Click += (s, e) => EditImageSize(image);
                contextMenu.Items.Add(editSizeItem);

                // 分隔符
                var separator1 = new Separator
                {
                    Style = TryFindResource("FigmaSeparatorStyle") as Style
                };
                contextMenu.Items.Add(separator1);

                // 查看原图
                var viewOriginalItem = new MenuItem
                {
                    Header = "查看原图(_V)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                viewOriginalItem.Click += (s, e) => ViewOriginalImage(image);
                contextMenu.Items.Add(viewOriginalItem);

                // 在文件夹中显示
                var showInFolderItem = new MenuItem
                {
                    Header = "在文件夹中显示(_F)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                showInFolderItem.Click += (s, e) => ShowImageInFolder(image);
                contextMenu.Items.Add(showInFolderItem);

                // 分隔符
                var separator2 = new Separator
                {
                    Style = TryFindResource("FigmaSeparatorStyle") as Style
                };
                contextMenu.Items.Add(separator2);

                // 删除图片
                var deleteItem = new MenuItem
                {
                    Header = "删除图片(_D)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                deleteItem.Click += (s, e) => DeleteImage(image);
                contextMenu.Items.Add(deleteItem);

                // 直接设置ContextMenu
                image.ContextMenu = contextMenu;

                // 确保图片可以接收鼠标事件
                image.IsHitTestVisible = true;

                // 添加右键点击事件处理（双重保险）
                image.MouseRightButtonDown += (sender, e) =>
                {
                    System.Diagnostics.Debug.WriteLine("图片右键点击事件触发");
                    if (contextMenu != null)
                    {
                        contextMenu.PlacementTarget = image;
                        contextMenu.Placement = System.Windows.Controls.Primitives.PlacementMode.MousePoint;
                        contextMenu.IsOpen = true;
                    }
                    e.Handled = true;
                };

                // 调试信息
                System.Diagnostics.Debug.WriteLine($"图片右键菜单已添加，菜单项数量: {contextMenu.Items.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加图片右键菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 编辑图片尺寸 - 使用新的滑动条面板
        /// </summary>
        private void EditImageSize(System.Windows.Controls.Image image)
        {
            try
            {
                // 隐藏当前显示的编辑面板（如果有）
                HideImageEditPanel();

                // 创建新的编辑面板
                var editPanel = new ImageEditPanel();
                editPanel.SetTargetImage(image);

                // 监听面板事件
                editPanel.PanelClosed += (s, e) => HideImageEditPanel();
                editPanel.ImageSizeChanged += (s, e) =>
                {
                    System.Diagnostics.Debug.WriteLine($"图片尺寸已更新: {e.NewWidth:F0}x{e.NewHeight:F0} ({e.ScalePercent:F0}%)");
                };

                // 显示编辑面板
                ShowImageEditPanel(editPanel, image);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"编辑图片尺寸失败: {ex.Message}");
                MessageBox.Show($"编辑图片尺寸失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 查看原图
        /// </summary>
        private void ViewOriginalImage(System.Windows.Controls.Image image)
        {
            try
            {
                if (image.Tag is string imagePath && File.Exists(imagePath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = imagePath,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查看原图失败: {ex.Message}");
                MessageBox.Show($"无法打开图片: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 在文件夹中显示图片
        /// </summary>
        private void ShowImageInFolder(System.Windows.Controls.Image image)
        {
            try
            {
                if (image.Tag is string imagePath && File.Exists(imagePath))
                {
                    System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{imagePath}\"");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"在文件夹中显示失败: {ex.Message}");
                MessageBox.Show($"无法打开文件夹: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除图片
        /// </summary>
        private void DeleteImage(System.Windows.Controls.Image image)
        {
            try
            {
                // 获取图片文件路径
                string? imagePath = null;

                // 尝试从Tag获取路径
                if (image.Tag is string tagPath)
                {
                    imagePath = tagPath;
                }
                // 处理ResizableImageControl的情况
                InlineUIContainer? containerToRemove = null;
                if (image.Parent is InlineUIContainer imageContainer)
                {
                    containerToRemove = imageContainer;
                }
                // 如果是ResizableImageControl内部的Image
                else if (image.Parent is Grid grid && grid.Parent is ResizableImageControl resizableImage)
                {
                    imagePath = resizableImage.GetImageFilePath();
                    // 找到包含ResizableImageControl的容器
                    if (resizableImage.Parent is InlineUIContainer resizableContainer)
                    {
                        containerToRemove = resizableContainer;
                    }
                }

                // 从文档中删除图片控件
                if (containerToRemove != null && containerToRemove.Parent is Paragraph paragraph)
                {
                    paragraph.Inlines.Remove(containerToRemove);
                    System.Diagnostics.Debug.WriteLine("图片已从文档中删除");
                }

                // 删除物理文件
                if (!string.IsNullOrEmpty(imagePath) && File.Exists(imagePath))
                {
                    try
                    {
                        File.Delete(imagePath);
                        System.Diagnostics.Debug.WriteLine($"物理文件已删除: {imagePath}");
                    }
                    catch (Exception fileEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"删除物理文件失败: {fileEx.Message}");
                        // 文件删除失败不影响界面删除，只记录日志
                    }
                }

                // 触发保存以更新文档状态
                _hasPendingChanges = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除图片失败: {ex.Message}");
                MessageBox.Show($"删除图片失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 为表格添加右键菜单
        /// </summary>
        private void AddTableContextMenu(Table table)
        {
            try
            {
                var contextMenu = new ContextMenu();

                // 应用Figma风格
                if (TryFindResource("FigmaContextMenuStyle") is Style contextMenuStyle)
                {
                    contextMenu.Style = contextMenuStyle;
                }

                // 添加行
                var addRowItem = new MenuItem
                {
                    Header = "添加行(_R)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                addRowItem.Click += (s, e) => AddTableRow(table);
                contextMenu.Items.Add(addRowItem);

                // 添加列
                var addColumnItem = new MenuItem
                {
                    Header = "添加列(_C)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                addColumnItem.Click += (s, e) => AddTableColumn(table);
                contextMenu.Items.Add(addColumnItem);

                // 分隔符
                var separator1 = new Separator
                {
                    Style = TryFindResource("FigmaSeparatorStyle") as Style
                };
                contextMenu.Items.Add(separator1);

                // 删除行
                var deleteRowItem = new MenuItem
                {
                    Header = "删除行(_D)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                deleteRowItem.Click += (s, e) => DeleteTableRow(table);
                contextMenu.Items.Add(deleteRowItem);

                // 删除列
                var deleteColumnItem = new MenuItem
                {
                    Header = "删除列(_L)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                deleteColumnItem.Click += (s, e) => DeleteTableColumn(table);
                contextMenu.Items.Add(deleteColumnItem);

                // 分隔符
                var separator2 = new Separator
                {
                    Style = TryFindResource("FigmaSeparatorStyle") as Style
                };
                contextMenu.Items.Add(separator2);

                // 删除表格
                var deleteTableItem = new MenuItem
                {
                    Header = "删除表格(_T)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                deleteTableItem.Click += (s, e) => DeleteTable(table);
                contextMenu.Items.Add(deleteTableItem);

                table.ContextMenu = contextMenu;

                System.Diagnostics.Debug.WriteLine("表格右键菜单已添加");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加表格右键菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加表格行
        /// </summary>
        private void AddTableRow(Table table)
        {
            try
            {
                if (table.RowGroups.Count > 0)
                {
                    var rowGroup = table.RowGroups[0];
                    var columnCount = table.Columns.Count;
                    var newRow = new TableRow();

                    for (int j = 0; j < columnCount; j++)
                    {
                        var cellParagraph = new Paragraph(new Run("新单元格"))
                        {
                            Margin = new Thickness(0),
                            FontFamily = new FontFamily("Microsoft YaHei"),
                            FontSize = 14
                        };

                        var cell = new TableCell(cellParagraph)
                        {
                            BorderBrush = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED)),
                            BorderThickness = new Thickness(0.5, 0.5, 0.5, 0.5),
                            Padding = new Thickness(12, 8, 12, 8),
                            Background = Brushes.White
                        };

                        newRow.Cells.Add(cell);
                    }

                    rowGroup.Rows.Add(newRow);
                    System.Diagnostics.Debug.WriteLine("表格行添加成功");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加表格行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加表格列
        /// </summary>
        private void AddTableColumn(Table table)
        {
            try
            {
                // 添加新列定义
                table.Columns.Add(new TableColumn { Width = new GridLength(100) });

                // 为每一行添加新单元格
                if (table.RowGroups.Count > 0)
                {
                    var rowGroup = table.RowGroups[0];
                    for (int i = 0; i < rowGroup.Rows.Count; i++)
                    {
                        var row = rowGroup.Rows[i];
                        var cellParagraph = new Paragraph(new Run("新单元格"))
                        {
                            Margin = new Thickness(0),
                            FontFamily = new FontFamily("Microsoft YaHei"),
                            FontSize = 14
                        };

                        var cell = new TableCell(cellParagraph)
                        {
                            BorderBrush = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED)),
                            BorderThickness = new Thickness(0.5, 0.5, 0.5, 0.5),
                            Padding = new Thickness(12, 8, 12, 8),
                            Background = i == 0 ? new SolidColorBrush(Color.FromRgb(0xF8, 0xF9, 0xFA)) : Brushes.White
                        };

                        if (i == 0)
                        {
                            cellParagraph.FontWeight = FontWeights.SemiBold;
                        }

                        row.Cells.Add(cell);
                    }
                }

                System.Diagnostics.Debug.WriteLine("表格列添加成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加表格列失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除表格行
        /// </summary>
        private void DeleteTableRow(Table table)
        {
            try
            {
                if (table.RowGroups.Count > 0)
                {
                    var rowGroup = table.RowGroups[0];
                    if (rowGroup.Rows.Count > 1) // 至少保留一行
                    {
                        rowGroup.Rows.RemoveAt(rowGroup.Rows.Count - 1);
                        System.Diagnostics.Debug.WriteLine("表格行删除成功");
                    }
                    else
                    {
                        MessageBox.Show("表格至少需要保留一行", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除表格行失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除表格列
        /// </summary>
        private void DeleteTableColumn(Table table)
        {
            try
            {
                if (table.Columns.Count > 1) // 至少保留一列
                {
                    // 删除列定义
                    table.Columns.RemoveAt(table.Columns.Count - 1);

                    // 删除每行的最后一个单元格
                    if (table.RowGroups.Count > 0)
                    {
                        var rowGroup = table.RowGroups[0];
                        foreach (var row in rowGroup.Rows)
                        {
                            if (row.Cells.Count > 0)
                            {
                                row.Cells.RemoveAt(row.Cells.Count - 1);
                            }
                        }
                    }

                    System.Diagnostics.Debug.WriteLine("表格列删除成功");
                }
                else
                {
                    MessageBox.Show("表格至少需要保留一列", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除表格列失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 删除整个表格
        /// </summary>
        private void DeleteTable(Table table)
        {
            try
            {
                richTextEditor.Document.Blocks.Remove(table);
                System.Diagnostics.Debug.WriteLine("表格删除成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除表格失败: {ex.Message}");
                MessageBox.Show($"删除表格失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region 图片编辑面板管理

        private ImageEditPanel? _currentImageEditPanel;
        private System.Windows.Controls.Image? _currentEditingImage;

        /// <summary>
        /// 显示图片编辑面板
        /// </summary>
        private void ShowImageEditPanel(ImageEditPanel editPanel, System.Windows.Controls.Image targetImage)
        {
            try
            {
                _currentImageEditPanel = editPanel;
                _currentEditingImage = targetImage;

                // 将编辑面板添加到主网格的底部
                editPanel.HorizontalAlignment = HorizontalAlignment.Center;
                editPanel.VerticalAlignment = VerticalAlignment.Bottom;
                editPanel.Margin = new Thickness(0, 0, 0, 40);

                // 设置层级，确保在最上层
                Panel.SetZIndex(editPanel, 1000);

                // 添加到主网格
                MainGrid.Children.Add(editPanel);

                System.Diagnostics.Debug.WriteLine("图片编辑面板已显示");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示图片编辑面板失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 隐藏图片编辑面板
        /// </summary>
        private void HideImageEditPanel()
        {
            try
            {
                if (_currentImageEditPanel != null)
                {
                    // 从主网格中移除
                    MainGrid.Children.Remove(_currentImageEditPanel);

                    _currentImageEditPanel = null;
                    _currentEditingImage = null;

                    System.Diagnostics.Debug.WriteLine("图片编辑面板已隐藏");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"隐藏图片编辑面板失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置按钮点击事件
        /// </summary>
        private void BtnSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsDialog = new SettingsDialog(AppSettings.CurrentTheme);
                settingsDialog.Owner = this;

                if (settingsDialog.ShowDialog() == true && settingsDialog.SettingsChanged)
                {
                    // 更新应用程序设置
                    AppSettings.CurrentTheme = settingsDialog.SelectedTheme;
                    AppSettings.SaveToFile();

                    // 主题切换已在SettingsDialog中处理，这里只需要显示成功消息
                    if (settingsDialog.SelectedTheme != "Light")
                    {
                        MessageBox.Show("主题已切换！", "设置", MessageBoxButton.OK, MessageBoxImage.Information);
                    }

                    System.Diagnostics.Debug.WriteLine($"设置已更新: 主题={AppSettings.CurrentTheme}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"打开设置对话框失败: {ex.Message}");
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 为ResizableImageControl添加右键菜单
        /// </summary>
        private void AddResizableImageContextMenu(ResizableImageControl resizableImage)
        {
            try
            {
                var contextMenu = new ContextMenu();

                // 应用Figma风格
                if (TryFindResource("FigmaContextMenuStyle") is Style contextMenuStyle)
                {
                    contextMenu.Style = contextMenuStyle;
                }

                // 编辑图片尺寸
                var editSizeItem = new MenuItem
                {
                    Header = "编辑图片尺寸(_S)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                editSizeItem.Click += (s, e) => EditResizableImageSize(resizableImage);
                contextMenu.Items.Add(editSizeItem);

                // 分隔符
                var separator1 = new Separator
                {
                    Style = TryFindResource("FigmaSeparatorStyle") as Style
                };
                contextMenu.Items.Add(separator1);

                // 查看原图
                var viewOriginalItem = new MenuItem
                {
                    Header = "查看原图(_V)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                viewOriginalItem.Click += (s, e) => ViewOriginalResizableImage(resizableImage);
                contextMenu.Items.Add(viewOriginalItem);

                // 在文件夹中显示
                var showInFolderItem = new MenuItem
                {
                    Header = "在文件夹中显示(_F)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                showInFolderItem.Click += (s, e) => ShowResizableImageInFolder(resizableImage);
                contextMenu.Items.Add(showInFolderItem);

                // 分隔符
                var separator2 = new Separator
                {
                    Style = TryFindResource("FigmaSeparatorStyle") as Style
                };
                contextMenu.Items.Add(separator2);

                // 删除图片
                var deleteItem = new MenuItem
                {
                    Header = "删除图片(_D)",
                    Style = TryFindResource("FigmaMenuItemStyle") as Style
                };
                deleteItem.Click += (s, e) => DeleteResizableImage(resizableImage);
                contextMenu.Items.Add(deleteItem);

                // 设置右键菜单
                resizableImage.ContextMenu = contextMenu;

                System.Diagnostics.Debug.WriteLine($"ResizableImageControl右键菜单已添加，菜单项数量: {contextMenu.Items.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加ResizableImageControl右键菜单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 确保所有图片都有正确的右键菜单
        /// </summary>
        private void EnsureAllImagesHaveContextMenu()
        {
            try
            {
                if (richTextEditor?.Document == null) return;

                // 遍历文档中的所有图片
                var walker = richTextEditor.Document.ContentStart;
                while (walker != null && walker.CompareTo(richTextEditor.Document.ContentEnd) < 0)
                {
                    if (walker.GetAdjacentElement(LogicalDirection.Forward) is InlineUIContainer container)
                    {
                        // 处理普通Image控件
                        if (container.Child is System.Windows.Controls.Image image)
                        {
                            // 如果图片没有右键菜单，添加一个
                            if (image.ContextMenu == null)
                            {
                                AddImageContextMenu(image);
                                System.Diagnostics.Debug.WriteLine("为图片重新添加了右键菜单");
                            }
                        }
                        // 处理ResizableImageControl
                        else if (container.Child is ResizableImageControl resizableImage)
                        {
                            AddResizableImageContextMenu(resizableImage);
                            System.Diagnostics.Debug.WriteLine("为ResizableImageControl添加了右键菜单");
                        }
                    }

                    walker = walker.GetNextContextPosition(LogicalDirection.Forward);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"确保图片右键菜单失败: {ex.Message}");
            }
        }

        #endregion

        #region ResizableImageControl 辅助方法

        /// <summary>
        /// 编辑ResizableImageControl的尺寸
        /// </summary>
        private void EditResizableImageSize(ResizableImageControl resizableImage)
        {
            try
            {
                var imagePath = resizableImage.GetImageFilePath();
                if (string.IsNullOrEmpty(imagePath) || !File.Exists(imagePath))
                {
                    MessageBox.Show("无法找到图片文件", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 隐藏当前显示的编辑面板（如果有）
                HideImageEditPanel();

                // 获取ResizableImageControl内部的Image控件
                var imageControl = GetImageControlFromResizableImage(resizableImage);
                if (imageControl == null)
                {
                    MessageBox.Show("无法获取图片控件", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 创建新的编辑面板
                var editPanel = new ImageEditPanel();
                editPanel.SetTargetImage(imageControl);

                // 监听面板事件
                editPanel.PanelClosed += (s, e) => HideImageEditPanel();
                editPanel.ImageSizeChanged += (s, e) =>
                {
                    // 同时更新ResizableImageControl的尺寸
                    resizableImage.Width = e.NewWidth;
                    resizableImage.Height = e.NewHeight;
                    System.Diagnostics.Debug.WriteLine($"ResizableImageControl尺寸已更新: {e.NewWidth:F0}x{e.NewHeight:F0} ({e.ScalePercent:F0}%)");
                };

                // 显示编辑面板
                ShowImageEditPanel(editPanel, imageControl);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"编辑ResizableImageControl尺寸失败: {ex.Message}");
                MessageBox.Show($"编辑图片尺寸失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从ResizableImageControl获取内部的Image控件
        /// </summary>
        private System.Windows.Controls.Image? GetImageControlFromResizableImage(ResizableImageControl resizableImage)
        {
            try
            {
                // 使用反射获取内部的imageControl字段
                var field = resizableImage.GetType().GetField("imageControl",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (field != null)
                {
                    return field.GetValue(resizableImage) as System.Windows.Controls.Image;
                }

                // 如果反射失败，尝试通过名称查找
                if (resizableImage.FindName("imageControl") is System.Windows.Controls.Image imageControl)
                {
                    return imageControl;
                }

                System.Diagnostics.Debug.WriteLine("无法找到ResizableImageControl的内部Image控件");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取内部Image控件失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 查看ResizableImageControl原图
        /// </summary>
        private void ViewOriginalResizableImage(ResizableImageControl resizableImage)
        {
            try
            {
                var imagePath = resizableImage.GetImageFilePath();
                if (!string.IsNullOrEmpty(imagePath) && File.Exists(imagePath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = imagePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("无法找到图片文件", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开图片: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 在文件夹中显示ResizableImageControl
        /// </summary>
        private void ShowResizableImageInFolder(ResizableImageControl resizableImage)
        {
            try
            {
                var imagePath = resizableImage.GetImageFilePath();
                if (!string.IsNullOrEmpty(imagePath) && File.Exists(imagePath))
                {
                    System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{imagePath}\"");
                }
                else
                {
                    MessageBox.Show("无法找到图片文件", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法打开文件夹: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除ResizableImageControl
        /// </summary>
        private void DeleteResizableImage(ResizableImageControl resizableImage)
        {
            try
            {
                // 获取图片文件路径
                var imagePath = resizableImage.GetImageFilePath();

                // 从文档中删除控件
                if (resizableImage.Parent is InlineUIContainer container &&
                    container.Parent is Paragraph paragraph)
                {
                    paragraph.Inlines.Remove(container);
                    System.Diagnostics.Debug.WriteLine("ResizableImageControl已从文档中删除");
                }

                // 删除物理文件
                if (!string.IsNullOrEmpty(imagePath) && File.Exists(imagePath))
                {
                    try
                    {
                        File.Delete(imagePath);
                        System.Diagnostics.Debug.WriteLine($"物理文件已删除: {imagePath}");
                    }
                    catch (Exception fileEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"删除物理文件失败: {fileEx.Message}");
                        // 文件删除失败不影响界面删除，只记录日志
                    }
                }

                // 触发保存以更新文档状态
                _hasPendingChanges = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除ResizableImageControl失败: {ex.Message}");
                MessageBox.Show($"删除图片失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #endregion

    }
}
