using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Media.Imaging;

namespace 像素喵笔记
{
    /// <summary>
    /// 媒体文件管理器
    /// 负责管理文档中的所有媒体文件
    /// </summary>
    public class MediaManager
    {
        #region 私有字段

        private readonly List<MediaFileInfo> _mediaFiles;
        private readonly string _mediaDirectory;

        #endregion

        #region 构造函数

        public MediaManager()
        {
            _mediaFiles = new List<MediaFileInfo>();
            
            // 创建媒体文件存储目录
            _mediaDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "像素喵笔记", "Media");
            
            if (!Directory.Exists(_mediaDirectory))
            {
                Directory.CreateDirectory(_mediaDirectory);
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 添加媒体文件
        /// </summary>
        public MediaFileInfo AddMediaFile(string filePath, MediaFileType fileType)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException("文件不存在", filePath);
                }

                var fileName = Path.GetFileName(filePath);
                var fileExtension = Path.GetExtension(filePath);
                var uniqueFileName = GenerateUniqueFileName(fileName);
                var destinationPath = Path.Combine(_mediaDirectory, uniqueFileName);

                // 复制文件到媒体目录
                File.Copy(filePath, destinationPath, true);

                // 创建媒体文件信息
                var mediaFile = new MediaFileInfo
                {
                    Id = Guid.NewGuid().ToString(),
                    FileName = fileName,
                    UniqueFileName = uniqueFileName,
                    FilePath = destinationPath,
                    OriginalPath = filePath,
                    FileType = fileType,
                    FileSize = GetFileSizeString(new FileInfo(filePath).Length),
                    DateAdded = DateTime.Now,
                    ThumbnailPath = GenerateThumbnail(destinationPath, fileType)
                };

                _mediaFiles.Add(mediaFile);
                return mediaFile;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加媒体文件失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 移除媒体文件 - 改进版，使用文件引用管理器安全删除
        /// </summary>
        public bool RemoveMediaFile(string mediaFileId)
        {
            try
            {
                var mediaFile = _mediaFiles.FirstOrDefault(m => m.Id == mediaFileId);
                if (mediaFile == null) return false;

                // 🔧 使用文件引用管理器安全删除文件
                if (File.Exists(mediaFile.FilePath))
                {
                    FileReferenceManager.Instance.SafeDeleteFile(mediaFile.FilePath);
                }

                // 删除缩略图（缩略图通常不被共享，可以直接删除）
                if (!string.IsNullOrEmpty(mediaFile.ThumbnailPath) && File.Exists(mediaFile.ThumbnailPath))
                {
                    try
                    {
                        File.Delete(mediaFile.ThumbnailPath);
                    }
                    catch (Exception thumbEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"删除缩略图失败: {mediaFile.ThumbnailPath}, {thumbEx.Message}");
                    }
                }

                _mediaFiles.Remove(mediaFile);
                System.Diagnostics.Debug.WriteLine($"已从媒体管理器移除文件记录: {mediaFile.FileName}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"移除媒体文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取所有媒体文件
        /// </summary>
        public List<MediaFileInfo> GetAllMediaFiles()
        {
            return _mediaFiles.ToList();
        }

        /// <summary>
        /// 根据类型获取媒体文件
        /// </summary>
        public List<MediaFileInfo> GetMediaFilesByType(MediaFileType fileType)
        {
            return _mediaFiles.Where(m => m.FileType == fileType).ToList();
        }

        /// <summary>
        /// 获取媒体文件数量
        /// </summary>
        public int GetMediaFileCount()
        {
            return _mediaFiles.Count;
        }

        /// <summary>
        /// 根据ID获取媒体文件
        /// </summary>
        public MediaFileInfo? GetMediaFileById(string id)
        {
            return _mediaFiles.FirstOrDefault(m => m.Id == id);
        }

        /// <summary>
        /// 清空所有媒体文件
        /// </summary>
        public void ClearAllMediaFiles()
        {
            try
            {
                foreach (var mediaFile in _mediaFiles.ToList())
                {
                    RemoveMediaFile(mediaFile.Id);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清空媒体文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量添加媒体文件
        /// </summary>
        public List<MediaFileInfo> AddMediaFiles(string[] filePaths)
        {
            var addedFiles = new List<MediaFileInfo>();
            foreach (var filePath in filePaths)
            {
                try
                {
                    var fileExtension = Path.GetExtension(filePath).ToLower();
                    var mediaType = GetMediaFileType(fileExtension);
                    var mediaFile = AddMediaFile(filePath, mediaType);
                    addedFiles.Add(mediaFile);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"添加文件 {filePath} 失败: {ex.Message}");
                }
            }
            return addedFiles;
        }

        /// <summary>
        /// 获取媒体文件类型
        /// </summary>
        private MediaFileType GetMediaFileType(string extension)
        {
            return extension switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".tiff" or ".webp" => MediaFileType.Image,
                ".pdf" or ".doc" or ".docx" or ".txt" or ".rtf" => MediaFileType.Document,
                _ => MediaFileType.Document
            };
        }

        /// <summary>
        /// 搜索媒体文件
        /// </summary>
        public List<MediaFileInfo> SearchMediaFiles(string keyword)
        {
            if (string.IsNullOrWhiteSpace(keyword))
                return GetAllMediaFiles();

            return _mediaFiles.Where(m =>
                m.FileName.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                (!string.IsNullOrEmpty(m.Description) && m.Description.Contains(keyword, StringComparison.OrdinalIgnoreCase))
            ).ToList();
        }

        /// <summary>
        /// 获取媒体文件总大小
        /// </summary>
        public long GetTotalSize()
        {
            long totalSize = 0;
            foreach (var mediaFile in _mediaFiles)
            {
                if (File.Exists(mediaFile.FilePath))
                {
                    totalSize += new FileInfo(mediaFile.FilePath).Length;
                }
            }
            return totalSize;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 生成唯一文件名
        /// </summary>
        private string GenerateUniqueFileName(string originalFileName)
        {
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            var extension = Path.GetExtension(originalFileName);
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            return $"{nameWithoutExtension}_{timestamp}{extension}";
        }

        /// <summary>
        /// 获取文件大小字符串
        /// </summary>
        private string GetFileSizeString(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        /// <summary>
        /// 生成缩略图
        /// </summary>
        private string? GenerateThumbnail(string filePath, MediaFileType fileType)
        {
            try
            {
                if (fileType != MediaFileType.Image) return null;

                var thumbnailDirectory = Path.Combine(_mediaDirectory, "Thumbnails");
                if (!Directory.Exists(thumbnailDirectory))
                {
                    Directory.CreateDirectory(thumbnailDirectory);
                }

                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var thumbnailPath = Path.Combine(thumbnailDirectory, $"{fileName}_thumb.jpg");

                // 创建缩略图
                var originalImage = new BitmapImage();
                originalImage.BeginInit();
                originalImage.UriSource = new Uri(filePath);
                originalImage.DecodePixelWidth = 150; // 缩略图宽度
                originalImage.EndInit();

                // 保存缩略图
                var encoder = new JpegBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(originalImage));
                using (var fileStream = new FileStream(thumbnailPath, FileMode.Create))
                {
                    encoder.Save(fileStream);
                }

                return thumbnailPath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"生成缩略图失败: {ex.Message}");
                return null;
            }
        }

        #endregion
    }

    /// <summary>
    /// 媒体文件信息类
    /// </summary>
    public class MediaFileInfo
    {
        public string Id { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string UniqueFileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string OriginalPath { get; set; } = string.Empty;
        public MediaFileType FileType { get; set; }
        public string FileSize { get; set; } = string.Empty;
        public DateTime DateAdded { get; set; }
        public string? ThumbnailPath { get; set; }
        public string? Description { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();
    }
}
