<Window x:Class="像素喵笔记.MediaResizeDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="调整媒体尺寸" 
        Width="400" 
        Height="300"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F8F9FA">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Border Background="White" 
            CornerRadius="12" 
            Margin="16"
            Effect="{StaticResource StandardFigmaShadow}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0" 
                    Background="#4285F4" 
                    CornerRadius="12,12,0,0" 
                    Padding="20,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                               Text="📏" 
                               FontFamily="Segoe UI Emoji" 
                               FontSize="20" 
                               VerticalAlignment="Center" 
                               Margin="0,0,12,0"/>
                    
                    <TextBlock Grid.Column="1" 
                               Text="调整媒体尺寸" 
                               FontSize="16" 
                               FontWeight="SemiBold" 
                               Foreground="White" 
                               VerticalAlignment="Center"/>
                </Grid>
            </Border>

            <!-- 内容区域 -->
            <StackPanel Grid.Row="1" Margin="24,20">
                <!-- 当前尺寸显示 -->
                <TextBlock Text="当前尺寸" 
                           FontWeight="SemiBold" 
                           FontSize="14" 
                           Foreground="#202124" 
                           Margin="0,0,0,8"/>
                
                <Border Background="#F1F3F4" 
                        CornerRadius="6" 
                        Padding="12,8" 
                        Margin="0,0,0,16">
                    <TextBlock x:Name="currentSizeText" 
                               FontSize="13" 
                               Foreground="#5F6368"/>
                </Border>

                <!-- 新尺寸设置 -->
                <TextBlock Text="新尺寸设置" 
                           FontWeight="SemiBold" 
                           FontSize="14" 
                           Foreground="#202124" 
                           Margin="0,0,0,12"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 宽度设置 -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="宽度 (px)" 
                                   FontSize="12" 
                                   Foreground="#5F6368" 
                                   Margin="0,0,0,4"/>
                        <TextBox x:Name="widthTextBox" 
                                 Style="{StaticResource FigmaTextBoxStyle}" 
                                 Height="36" 
                                 TextAlignment="Center"
                                 PreviewTextInput="NumericTextBox_PreviewTextInput"/>
                    </StackPanel>

                    <!-- 分隔符 -->
                    <TextBlock Grid.Column="1" 
                               Text="×" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Foreground="#5F6368" 
                               VerticalAlignment="Center" 
                               Margin="16,20,16,0"/>

                    <!-- 高度设置 -->
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="高度 (px)" 
                                   FontSize="12" 
                                   Foreground="#5F6368" 
                                   Margin="0,0,0,4"/>
                        <TextBox x:Name="heightTextBox" 
                                 Style="{StaticResource FigmaTextBoxStyle}" 
                                 Height="36" 
                                 TextAlignment="Center"
                                 PreviewTextInput="NumericTextBox_PreviewTextInput"/>
                    </StackPanel>
                </Grid>

                <!-- 保持比例复选框 -->
                <CheckBox x:Name="keepAspectRatioCheckBox" 
                          Content="保持宽高比" 
                          IsChecked="True" 
                          FontSize="13" 
                          Foreground="#202124" 
                          Margin="0,16,0,0"
                          Checked="KeepAspectRatio_Changed"
                          Unchecked="KeepAspectRatio_Changed"/>
            </StackPanel>

            <!-- 按钮区域 -->
            <Border Grid.Row="2" 
                    BorderBrush="#E8EAED" 
                    BorderThickness="0,1,0,0" 
                    Padding="24,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Button Grid.Column="1" 
                            x:Name="cancelButton" 
                            Content="取消" 
                            Style="{StaticResource FigmaSecondaryButtonStyle}" 
                            Width="80" 
                            Height="36" 
                            Margin="0,0,12,0" 
                            Click="CancelButton_Click"/>

                    <Button Grid.Column="2" 
                            x:Name="okButton" 
                            Content="确定" 
                            Style="{StaticResource FigmaPrimaryButtonStyle}" 
                            Width="80" 
                            Height="36" 
                            Click="OkButton_Click"/>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Window>
