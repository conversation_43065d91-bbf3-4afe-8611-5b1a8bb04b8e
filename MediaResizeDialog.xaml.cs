using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Input;

namespace 像素喵笔记
{
    /// <summary>
    /// MediaResizeDialog.xaml 的交互逻辑
    /// </summary>
    public partial class MediaResizeDialog : Window
    {
        private double _originalWidth;
        private double _originalHeight;
        private double _aspectRatio;
        private bool _isUpdatingSize = false;

        public double NewWidth { get; private set; }
        public double NewHeight { get; private set; }

        public MediaResizeDialog(double currentWidth, double currentHeight)
        {
            InitializeComponent();
            
            _originalWidth = currentWidth;
            _originalHeight = currentHeight;
            _aspectRatio = currentWidth / currentHeight;

            InitializeDialog();
        }

        private void InitializeDialog()
        {
            try
            {
                // 显示当前尺寸
                currentSizeText.Text = $"宽度: {_originalWidth:F0} px, 高度: {_originalHeight:F0} px";

                // 设置初始值
                widthTextBox.Text = _originalWidth.ToString("F0");
                heightTextBox.Text = _originalHeight.ToString("F0");

                // 绑定文本变化事件
                widthTextBox.TextChanged += WidthTextBox_TextChanged;
                heightTextBox.TextChanged += HeightTextBox_TextChanged;

                // 设置焦点
                widthTextBox.Focus();
                widthTextBox.SelectAll();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化对话框失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 数字输入验证
        /// </summary>
        private void NumericTextBox_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // 只允许数字和小数点
            Regex regex = new Regex("[^0-9.]+");
            e.Handled = regex.IsMatch(e.Text);
        }

        /// <summary>
        /// 宽度文本框变化事件
        /// </summary>
        private void WidthTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            if (_isUpdatingSize || !keepAspectRatioCheckBox.IsChecked == true) return;

            try
            {
                if (double.TryParse(widthTextBox.Text, out double width) && width > 0)
                {
                    _isUpdatingSize = true;
                    double newHeight = width / _aspectRatio;
                    heightTextBox.Text = newHeight.ToString("F0");
                    _isUpdatingSize = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"宽度变化处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 高度文本框变化事件
        /// </summary>
        private void HeightTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            if (_isUpdatingSize || !keepAspectRatioCheckBox.IsChecked == true) return;

            try
            {
                if (double.TryParse(heightTextBox.Text, out double height) && height > 0)
                {
                    _isUpdatingSize = true;
                    double newWidth = height * _aspectRatio;
                    widthTextBox.Text = newWidth.ToString("F0");
                    _isUpdatingSize = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高度变化处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保持宽高比复选框变化事件
        /// </summary>
        private void KeepAspectRatio_Changed(object sender, RoutedEventArgs e)
        {
            // 当启用保持宽高比时，重新计算高度
            if (keepAspectRatioCheckBox.IsChecked == true)
            {
                WidthTextBox_TextChanged(widthTextBox, null!);
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!double.TryParse(widthTextBox.Text, out double width) || width <= 0)
                {
                    MessageBox.Show("请输入有效的宽度值（大于0）", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    widthTextBox.Focus();
                    return;
                }

                if (!double.TryParse(heightTextBox.Text, out double height) || height <= 0)
                {
                    MessageBox.Show("请输入有效的高度值（大于0）", "输入错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                    heightTextBox.Focus();
                    return;
                }

                // 检查尺寸限制
                if (width > 2000 || height > 2000)
                {
                    var result = MessageBox.Show("尺寸较大，可能影响性能。是否继续？", "确认", 
                        MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result != MessageBoxResult.Yes)
                    {
                        return;
                    }
                }

                NewWidth = width;
                NewHeight = height;
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"确定按钮处理失败: {ex.Message}");
                MessageBox.Show($"设置尺寸失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
