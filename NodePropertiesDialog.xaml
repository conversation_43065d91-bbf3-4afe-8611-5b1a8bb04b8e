<Window x:Class="像素喵笔记.NodePropertiesDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:像素喵笔记"
        mc:Ignorable="d"
        Title="节点属性" Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Window.Resources>
        <!-- Figma风格按钮样式 -->
        <Style x:Key="FigmaDialogButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#DADCE0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#202124"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="4,0"/>
            <Setter Property="MinWidth" Value="80"/>
            <Setter Property="MinHeight" Value="32"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="8">
                            <ContentPresenter x:Name="contentPresenter"
                                            Focusable="False"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            Margin="{TemplateBinding Padding}"
                                            RecognizesAccessKey="True"
                                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" TargetName="border" Value="#0B8CE8"/>
                                <Setter Property="BorderBrush" TargetName="border" Value="#0B8CE8"/>
                                <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="White"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" TargetName="border" Value="#0A7BD6"/>
                                <Setter Property="BorderBrush" TargetName="border" Value="#0A7BD6"/>
                                <Setter Property="TextElement.Foreground" TargetName="contentPresenter" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 关闭按钮样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource FigmaDialogButtonStyle}">
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#FF2323"/>
                    <Setter Property="BorderBrush" Value="#FF2323"/>
                    <Setter Property="Foreground" Value="White"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#FF2323"/>
                    <Setter Property="BorderBrush" Value="#FF2323"/>
                    <Setter Property="Foreground" Value="White"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="PropertyLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#5F6368"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
        </Style>

        <!-- 值样式 -->
        <Style x:Key="PropertyValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Foreground" Value="#202124"/>
            <Setter Property="Margin" Value="0,0,0,12"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>

        <!-- 可点击值样式 -->
        <Style x:Key="ClickableValueStyle" TargetType="TextBlock" BasedOn="{StaticResource PropertyValueStyle}">
            <Setter Property="Foreground" Value="#1A73E8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="TextDecorations" Value="Underline"/>
        </Style>

        <!-- Figma风格滚动条样式 -->
        <Style TargetType="ScrollBar">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="12"/>
            <Setter Property="MinWidth" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ScrollBar">
                        <Grid x:Name="GridRoot" Width="12" Background="{TemplateBinding Background}">
                            <Track x:Name="PART_Track" IsDirectionReversed="True" Focusable="False">
                                <Track.Thumb>
                                    <Thumb x:Name="Thumb" Background="#C1C1C1" Style="{DynamicResource ScrollBarThumbStyle}"/>
                                </Track.Thumb>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton x:Name="PageUp" Command="ScrollBar.PageDownCommand" Opacity="0" Focusable="False"/>
                                </Track.IncreaseRepeatButton>
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton x:Name="PageDown" Command="ScrollBar.PageUpCommand" Opacity="0" Focusable="False"/>
                                </Track.DecreaseRepeatButton>
                            </Track>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Thumb" Property="Background" Value="#A6A6A6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="Orientation" Value="Horizontal">
                    <Setter Property="Width" Value="Auto"/>
                    <Setter Property="MinWidth" Value="0"/>
                    <Setter Property="Height" Value="12"/>
                    <Setter Property="MinHeight" Value="12"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="ScrollBar">
                                <Grid x:Name="GridRoot" Height="12" Background="{TemplateBinding Background}">
                                    <Track x:Name="PART_Track" IsDirectionReversed="False" Focusable="False">
                                        <Track.Thumb>
                                            <Thumb x:Name="Thumb" Background="#C1C1C1" Style="{DynamicResource ScrollBarThumbStyle}"/>
                                        </Track.Thumb>
                                        <Track.IncreaseRepeatButton>
                                            <RepeatButton x:Name="PageUp" Command="ScrollBar.PageRightCommand" Opacity="0" Focusable="False"/>
                                        </Track.IncreaseRepeatButton>
                                        <Track.DecreaseRepeatButton>
                                            <RepeatButton x:Name="PageDown" Command="ScrollBar.PageLeftCommand" Opacity="0" Focusable="False"/>
                                        </Track.DecreaseRepeatButton>
                                    </Track>
                                </Grid>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="Thumb" Property="Background" Value="#A6A6A6"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 滚动条拖拽块样式 -->
        <Style x:Key="ScrollBarThumbStyle" TargetType="Thumb">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Thumb">
                        <Border Background="{TemplateBinding Background}" CornerRadius="6" Margin="2"/>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- 主容器，带圆角和阴影 -->
    <Border Background="White" CornerRadius="12" Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="12"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="12,12,0,0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <StackPanel Orientation="Horizontal" Margin="20,15" HorizontalAlignment="Center">
                    <TextBlock Text="⚙️" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock x:Name="titleText" Text="节点属性" FontSize="16" FontWeight="SemiBold"
                              Foreground="#202124" VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- 内容区域 - 选项卡式布局 -->
            <TabControl Grid.Row="1" Margin="0" Background="White" BorderThickness="0">
                <TabControl.Resources>
                    <!-- 选项卡头部样式 -->
                    <Style TargetType="TabItem">
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="TabItem">
                                    <Border x:Name="Border" Background="Transparent" BorderThickness="0,0,0,2" BorderBrush="Transparent" Padding="20,12">
                                        <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Center"
                                                        ContentSource="Header" Margin="0" RecognizesAccessKey="True"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Trigger.EnterActions>
                                                <BeginStoryboard>
                                                    <Storyboard>
                                                        <ColorAnimation Storyboard.TargetName="Border"
                                                                      Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                                                                      To="#0B8CE8" Duration="0:0:0.3">
                                                            <ColorAnimation.EasingFunction>
                                                                <CubicEase EasingMode="EaseOut"/>
                                                            </ColorAnimation.EasingFunction>
                                                        </ColorAnimation>
                                                    </Storyboard>
                                                </BeginStoryboard>
                                            </Trigger.EnterActions>
                                            <Setter TargetName="Border" Property="BorderBrush" Value="#0B8CE8"/>
                                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="#0B8CE8"/>
                                            <Setter TargetName="ContentSite" Property="TextElement.FontWeight" Value="SemiBold"/>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="False">
                                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="#5F6368"/>
                                            <Setter TargetName="ContentSite" Property="TextElement.FontWeight" Value="Normal"/>
                                        </Trigger>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="ContentSite" Property="TextElement.Foreground" Value="#0B8CE8"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Setter Property="FontSize" Value="13"/>
                        <Setter Property="FontFamily" Value="Segoe UI"/>
                    </Style>
                </TabControl.Resources>

                <!-- 基本信息选项卡 -->
                <TabItem Header="基本信息">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
                        <StackPanel>
                            <TextBlock Text="名称" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="nameValue" Style="{StaticResource PropertyValueStyle}"/>

                            <TextBlock Text="类型" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="typeValue" Style="{StaticResource PropertyValueStyle}"/>

                            <TextBlock Text="路径" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="pathValue" Style="{StaticResource PropertyValueStyle}"/>

                            <TextBlock x:Name="parentLabel" Text="所属节点" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="parentValue" Style="{StaticResource ClickableValueStyle}" MouseLeftButtonUp="ParentValue_Click"/>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- 统计信息选项卡 -->
                <TabItem Header="统计信息">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
                        <StackPanel>
                            <TextBlock Text="字数" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="wordCountValue" Style="{StaticResource PropertyValueStyle}"/>

                            <TextBlock Text="图片数" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="imageCountValue" Style="{StaticResource PropertyValueStyle}"/>

                            <TextBlock Text="视频数" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="videoCountValue" Style="{StaticResource PropertyValueStyle}"/>

                            <TextBlock Text="表格数" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="tableCountValue" Style="{StaticResource PropertyValueStyle}"/>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- 创建时间选项卡 -->
                <TabItem Header="创建时间">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
                        <StackPanel>
                            <TextBlock Text="创建时间" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="createdTimeValue" Style="{StaticResource PropertyValueStyle}"/>

                            <TextBlock Text="修改时间" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="modifiedTimeValue" Style="{StaticResource PropertyValueStyle}"/>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- 层级深度选项卡 -->
                <TabItem Header="层级深度">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20">
                        <StackPanel>
                            <TextBlock x:Name="childrenLabel" Text="子节点数" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="childrenCountValue" Style="{StaticResource PropertyValueStyle}"/>

                            <TextBlock x:Name="depthLabel" Text="层级深度" Style="{StaticResource PropertyLabelStyle}"/>
                            <TextBlock x:Name="depthValue" Style="{StaticResource PropertyValueStyle}"/>
                        </StackPanel>
                    </ScrollViewer>
                </TabItem>
            </TabControl>

            <!-- 底部按钮 -->
            <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="0,0,12,12" Padding="20,15">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button x:Name="btnOK" Content="确定" Style="{StaticResource FigmaDialogConfirmButtonStyle}"
                            Click="BtnOK_Click" IsDefault="True"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
