using System;
using System.Linq;
using System.Windows;
using System.Windows.Input;

namespace 像素喵笔记
{
    /// <summary>
    /// NodePropertiesDialog.xaml 的交互逻辑
    /// </summary>
    public partial class NodePropertiesDialog : Window
    {
        private BaseNode _node;
        private Action<BaseNode>? _onParentClick;

        public NodePropertiesDialog(BaseNode node, Action<BaseNode>? onParentClick = null)
        {
            InitializeComponent();
            _node = node;
            _onParentClick = onParentClick;
            LoadNodeProperties();
        }

        private void LoadNodeProperties()
        {
            try
            {
                // 基本信息
                titleText.Text = $"{_node.Name} - 属性";
                nameValue.Text = _node.Name;
                typeValue.Text = GetNodeTypeDisplayName(_node.NodeType);
                pathValue.Text = _node.GetFullPath();

                // 所属节点
                if (_node.Parent != null)
                {
                    parentValue.Text = _node.Parent.Name;
                    parentValue.Visibility = Visibility.Visible;
                    parentLabel.Visibility = Visibility.Visible;
                }
                else
                {
                    parentValue.Visibility = Visibility.Collapsed;
                    parentLabel.Visibility = Visibility.Collapsed;
                }

                // 统计信息
                var stats = _node.GetStatistics();
                wordCountValue.Text = stats.WordCount.ToString();
                imageCountValue.Text = stats.ImageCount.ToString();
                videoCountValue.Text = stats.VideoCount.ToString();
                tableCountValue.Text = stats.TableCount.ToString();

                // 时间信息
                createdTimeValue.Text = _node.CreatedTime.ToString("yyyy年MM月dd日 HH:mm:ss");
                modifiedTimeValue.Text = _node.ModifiedTime.ToString("yyyy年MM月dd日 HH:mm:ss");

                // 层级信息
                childrenCountValue.Text = _node.Children.Count.ToString();
                depthValue.Text = GetNodeDepth(_node).ToString();

                // 根据节点类型调整显示
                AdjustDisplayForNodeType();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载节点属性失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetNodeTypeDisplayName(NodeType nodeType)
        {
            return nodeType switch
            {
                NodeType.Notebook => "笔记本",
                NodeType.Collection => "集合",
                NodeType.Page => "页面",
                _ => "未知"
            };
        }

        private int GetNodeDepth(BaseNode node)
        {
            int depth = 0;
            var current = node.Parent;
            while (current != null)
            {
                depth++;
                current = current.Parent;
            }
            return depth;
        }

        private void AdjustDisplayForNodeType()
        {
            switch (_node.NodeType)
            {
                case NodeType.Page:
                    // 页面节点不显示子节点信息
                    childrenLabel.Visibility = Visibility.Collapsed;
                    childrenCountValue.Visibility = Visibility.Collapsed;
                    break;

                case NodeType.Notebook:
                    // 笔记本显示集合数而不是子节点数
                    childrenLabel.Text = "集合数";
                    break;

                case NodeType.Collection:
                    // 集合显示页面数而不是子节点数
                    childrenLabel.Text = "页面数";
                    break;
            }
        }

        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                this.DragMove();
            }
        }



        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void ParentValue_Click(object sender, MouseButtonEventArgs e)
        {
            if (_node.Parent != null && _onParentClick != null)
            {
                _onParentClick(_node.Parent);
                this.Close();
            }
        }
    }
}
