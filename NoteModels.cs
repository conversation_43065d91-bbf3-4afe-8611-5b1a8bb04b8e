using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Runtime.CompilerServices;

namespace 像素喵笔记
{
    /// <summary>
    /// 节点类型枚举
    /// </summary>
    public enum NodeType
    {
        Notebook,     // 笔记本（一级节点）
        Collection,   // 集合（二级节点）
        Page,         // 笔记页（三级节点）
        CodePage      // 代码页（三级节点）
    }

    /// <summary>
    /// 节点状态枚举
    /// </summary>
    public enum NodeState
    {
        Normal,     // 正常状态
        Selected,   // 选中状态
        Cut,        // 剪切状态
        Dragging,   // 拖拽状态
        Editing     // 编辑状态
    }

    /// <summary>
    /// 基础节点类
    /// </summary>
    public abstract class BaseNode : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private NodeState _state = NodeState.Normal;
        private bool _isExpanded = false;
        private bool _isSelected = false;
        private DateTime _createdTime = DateTime.Now;
        private DateTime _modifiedTime = DateTime.Now;

        public string Id { get; set; } = Guid.NewGuid().ToString();

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    ModifiedTime = DateTime.Now;
                    OnPropertyChanged();
                }
            }
        }

        public abstract NodeType NodeType { get; }

        public NodeState State
        {
            get => _state;
            set
            {
                if (_state != value)
                {
                    _state = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsExpanded
        {
            get => _isExpanded;
            set
            {
                if (_isExpanded != value)
                {
                    _isExpanded = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    State = value ? NodeState.Selected : NodeState.Normal;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime CreatedTime
        {
            get => _createdTime;
            set
            {
                if (_createdTime != value)
                {
                    _createdTime = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime ModifiedTime
        {
            get => _modifiedTime;
            set
            {
                if (_modifiedTime != value)
                {
                    _modifiedTime = value;
                    OnPropertyChanged();
                }
            }
        }

        public BaseNode? Parent { get; set; }
        public ObservableCollection<BaseNode> Children { get; set; } = new ObservableCollection<BaseNode>();

        /// <summary>
        /// 用于存储额外数据的标签属性
        /// </summary>
        public object? Tag { get; set; }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 获取节点的完整路径
        /// </summary>
        public string GetFullPath()
        {
            if (Parent == null)
                return Name;
            return Path.Combine(Parent.GetFullPath(), Name);
        }

        /// <summary>
        /// 检查是否可以添加指定类型的子节点
        /// </summary>
        public abstract bool CanAddChild(NodeType childType);

        /// <summary>
        /// 检查是否可以移动到指定的父节点
        /// </summary>
        public abstract bool CanMoveTo(BaseNode? newParent);

        /// <summary>
        /// 获取节点统计信息
        /// </summary>
        public abstract NodeStatistics GetStatistics();

        /// <summary>
        /// 创建节点的深度副本
        /// </summary>
        public abstract BaseNode CreateDeepCopy();
    }

    /// <summary>
    /// 笔记本节点（一级节点）
    /// </summary>
    public class NotebookNode : BaseNode
    {
        public override NodeType NodeType => NodeType.Notebook;

        public NotebookNode()
        {
            Name = "新笔记本";
        }

        public NotebookNode(string name)
        {
            Name = name;
        }

        public override bool CanAddChild(NodeType childType)
        {
            return childType == NodeType.Collection;
        }

        public override bool CanMoveTo(BaseNode? newParent)
        {
            // 笔记本只能移动到根级别（newParent为null）
            return newParent == null;
        }

        public override NodeStatistics GetStatistics()
        {
            var stats = new NodeStatistics();
            stats.CollectionCount = Children.Count;

            foreach (var child in Children)
            {
                var childStats = child.GetStatistics();
                stats.PageCount += childStats.PageCount;
                stats.WordCount += childStats.WordCount;
                stats.ImageCount += childStats.ImageCount;
                stats.VideoCount += childStats.VideoCount;
                stats.AudioCount += childStats.AudioCount;
                stats.TableCount += childStats.TableCount;
                stats.OtherFileCount += childStats.OtherFileCount;
            }

            return stats;
        }

        public override BaseNode CreateDeepCopy()
        {
            var copy = new NotebookNode(Name)
            {
                CreatedTime = CreatedTime,
                ModifiedTime = ModifiedTime
            };

            foreach (var child in Children)
            {
                var childCopy = child.CreateDeepCopy();
                childCopy.Parent = copy;
                copy.Children.Add(childCopy);
            }

            return copy;
        }
    }

    /// <summary>
    /// 集合节点（二级节点）
    /// </summary>
    public class CollectionNode : BaseNode
    {
        public override NodeType NodeType => NodeType.Collection;

        public CollectionNode()
        {
            Name = "新集合";
        }

        public CollectionNode(string name)
        {
            Name = name;
        }

        public override bool CanAddChild(NodeType childType)
        {
            return childType == NodeType.Page || childType == NodeType.CodePage;
        }

        public override bool CanMoveTo(BaseNode? newParent)
        {
            // 集合只能移动到笔记本下
            return newParent != null && newParent.NodeType == NodeType.Notebook;
        }

        public override NodeStatistics GetStatistics()
        {
            var stats = new NodeStatistics();
            stats.PageCount = Children.Count;

            foreach (var child in Children)
            {
                var childStats = child.GetStatistics();
                stats.WordCount += childStats.WordCount;
                stats.ImageCount += childStats.ImageCount;
                stats.VideoCount += childStats.VideoCount;
                stats.AudioCount += childStats.AudioCount;
                stats.TableCount += childStats.TableCount;
                stats.OtherFileCount += childStats.OtherFileCount;
            }

            return stats;
        }

        public override BaseNode CreateDeepCopy()
        {
            var copy = new CollectionNode(Name)
            {
                CreatedTime = CreatedTime,
                ModifiedTime = ModifiedTime
            };

            foreach (var child in Children)
            {
                var childCopy = child.CreateDeepCopy();
                childCopy.Parent = copy;
                copy.Children.Add(childCopy);
            }

            return copy;
        }
    }

    /// <summary>
    /// 笔记页节点（三级节点）
    /// </summary>
    public class PageNode : BaseNode
    {
        private string _content = string.Empty;
        private int _wordCount = 0;
        private int _imageCount = 0;
        private int _videoCount = 0;
        private int _audioCount = 0;
        private int _otherFileCount = 0;
        private int _tableCount = 0;

        public override NodeType NodeType => NodeType.Page;

        public string Content
        {
            get => _content;
            set
            {
                if (_content != value)
                {
                    _content = value;
                    ModifiedTime = DateTime.Now;
                    UpdateStatistics();
                    OnPropertyChanged();
                }
            }
        }

        public int WordCount
        {
            get => _wordCount;
            private set
            {
                if (_wordCount != value)
                {
                    _wordCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public int ImageCount
        {
            get => _imageCount;
            set
            {
                if (_imageCount != value)
                {
                    _imageCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public int VideoCount
        {
            get => _videoCount;
            set
            {
                if (_videoCount != value)
                {
                    _videoCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public int AudioCount
        {
            get => _audioCount;
            set
            {
                if (_audioCount != value)
                {
                    _audioCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public int OtherFileCount
        {
            get => _otherFileCount;
            set
            {
                if (_otherFileCount != value)
                {
                    _otherFileCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public int TableCount
        {
            get => _tableCount;
            set
            {
                if (_tableCount != value)
                {
                    _tableCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public PageNode()
        {
            Name = "新页面";
        }

        public PageNode(string name)
        {
            Name = name;
        }

        public override bool CanAddChild(NodeType childType)
        {
            // 笔记页不能添加子节点
            return false;
        }

        public override bool CanMoveTo(BaseNode? newParent)
        {
            // 笔记页只能移动到集合下
            return newParent != null && newParent.NodeType == NodeType.Collection;
        }

        public override NodeStatistics GetStatistics()
        {
            return new NodeStatistics
            {
                PageCount = 1,
                WordCount = WordCount,
                ImageCount = ImageCount,
                VideoCount = VideoCount,
                AudioCount = AudioCount,
                TableCount = TableCount,
                OtherFileCount = OtherFileCount
            };
        }

        public override BaseNode CreateDeepCopy()
        {
            var copy = new PageNode(Name)
            {
                Content = Content,
                CreatedTime = CreatedTime,
                ModifiedTime = ModifiedTime,
                WordCount = WordCount,
                ImageCount = ImageCount,
                VideoCount = VideoCount,
                AudioCount = AudioCount,
                TableCount = TableCount,
                OtherFileCount = OtherFileCount
            };

            return copy;
        }

        private void UpdateStatistics()
        {
            // 计算字符数
            WordCount = string.IsNullOrWhiteSpace(Content) ? 0 : Content.Trim().Length;
        }


    }

    // 🔧 删除DrawingBoardNode类

    /// <summary>
    /// 代码页节点（三级节点）
    /// </summary>
    public class CodePageNode : BaseNode
    {
        private string _code = string.Empty;
        private string _language = "Python";
        private string _output = string.Empty;

        public override NodeType NodeType => NodeType.CodePage;

        public string Code
        {
            get => _code;
            set
            {
                if (_code != value)
                {
                    _code = value;
                    ModifiedTime = DateTime.Now;
                    OnPropertyChanged();
                }
            }
        }

        public string Language
        {
            get => _language;
            set
            {
                if (_language != value)
                {
                    _language = value;
                    ModifiedTime = DateTime.Now;
                    OnPropertyChanged();
                }
            }
        }

        public string Output
        {
            get => _output;
            set
            {
                if (_output != value)
                {
                    _output = value;
                    OnPropertyChanged();
                }
            }
        }

        public CodePageNode()
        {
            Name = "新代码页";
        }

        public CodePageNode(string name)
        {
            Name = name;
        }

        public override bool CanAddChild(NodeType childType)
        {
            // 代码页不能添加子节点
            return false;
        }

        public override bool CanMoveTo(BaseNode? newParent)
        {
            // 代码页只能移动到集合下
            return newParent != null && newParent.NodeType == NodeType.Collection;
        }

        public override NodeStatistics GetStatistics()
        {
            return new NodeStatistics
            {
                PageCount = 1,
                WordCount = Code?.Length ?? 0,
                ImageCount = 0,
                VideoCount = 0,
                AudioCount = 0,
                TableCount = 0,
                OtherFileCount = 0
            };
        }

        public override BaseNode CreateDeepCopy()
        {
            var copy = new CodePageNode(Name)
            {
                CreatedTime = CreatedTime,
                ModifiedTime = ModifiedTime,
                Code = Code,
                Language = Language,
                Output = Output
            };

            return copy;
        }
    }

    /// <summary>
    /// 节点统计信息
    /// </summary>
    public class NodeStatistics
    {
        public int CollectionCount { get; set; } = 0;
        public int PageCount { get; set; } = 0;
        public int WordCount { get; set; } = 0;
        public int ImageCount { get; set; } = 0;
        public int VideoCount { get; set; } = 0;
        public int AudioCount { get; set; } = 0;
        public int TableCount { get; set; } = 0;
        public int OtherFileCount { get; set; } = 0;

        public int TotalFileCount => ImageCount + VideoCount + AudioCount + OtherFileCount;
    }
}
