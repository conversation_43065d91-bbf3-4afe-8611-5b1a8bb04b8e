<Window x:Class="像素喵笔记.NumberingDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="编号" Height="500" Width="700"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ResizeMode="NoResize"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        RenderOptions.BitmapScalingMode="HighQuality">
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <!-- 添加外层Grid，增加边距，防止阴影被切断 -->
    <Grid Margin="15">
        <Border Style="{StaticResource FigmaCardStyle}" RenderTransformOrigin="0.5,0.5">
            <Border.RenderTransform>
                <TransformGroup>
                    <ScaleTransform/>
                    <SkewTransform/>
                    <RotateTransform Angle="0"/>
                    <TranslateTransform/>
                </TransformGroup>
            </Border.RenderTransform>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <TextBlock Text="编号" FontSize="18" FontWeight="Bold" Foreground="#202124" VerticalAlignment="Center"/>
                    <Button x:Name="btnClose" Content="✕"
                           HorizontalAlignment="Right" Click="BtnClose_Click"
                           Style="{StaticResource FigmaDialogCloseButtonStyle}"/>
                </Grid>

                <!-- 选项卡 -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,16">
                    <Button x:Name="btnNumberingTab" Content="编号" Style="{StaticResource TabButtonStyle}"
                            Click="BtnNumberingTab_Click" Tag="Selected"/>
                    <Button x:Name="btnBulletsTab" Content="预设样式" Style="{StaticResource TabButtonStyle}"
                            Click="BtnBulletsTab_Click"/>
                    <Button x:Name="btnMultiLevelTab" Content="多级编号" Style="{StaticResource TabButtonStyle}"
                            Click="BtnMultiLevelTab_Click"/>
                </StackPanel>

                <!-- 内容区域 - 卡片形式 -->
                <Border Grid.Row="2" Style="{StaticResource FigmaCardStyle}" Margin="0,0,0,16">
                    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                        <Grid x:Name="contentGrid">
                            <!-- 编号选项卡内容 -->
                            <WrapPanel x:Name="numberingPanel" Orientation="Horizontal" Visibility="Visible"/>

                            <!-- 预设样式选项卡内容 -->
                            <WrapPanel x:Name="bulletsPanel" Orientation="Horizontal" Visibility="Collapsed"/>

                            <!-- 多级编号选项卡内容 -->
                            <WrapPanel x:Name="multiLevelPanel" Orientation="Horizontal" Visibility="Collapsed"/>
                        </Grid>
                    </ScrollViewer>
                </Border>

                <!-- 底部按钮 -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
                    <Button x:Name="btnCancel" Content="取消" Style="{StaticResource FigmaDialogCancelButtonStyle}"
                            Click="BtnCancel_Click" IsCancel="True"/>
                    <Button x:Name="btnOK" Content="确定" Style="{StaticResource FigmaDialogConfirmButtonStyle}"
                            Click="BtnOK_Click" IsDefault="True"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
