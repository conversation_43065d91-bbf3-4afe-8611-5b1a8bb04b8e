using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace 像素喵笔记
{
    public partial class NumberingDialog : Window
    {
        public string SelectedNumberingStyle { get; private set; } = string.Empty;
        public string SelectedBulletStyle { get; private set; } = string.Empty;
        public string SelectedMultiLevelStyle { get; private set; } = string.Empty;

        // 编号样式
        private readonly Dictionary<string, string[]> numberingStyles = new Dictionary<string, string[]>
        {
            ["无"] = new string[] { "无编号" },
            ["数字"] = new string[] { "1.", "2.", "3." },
            ["数字括号"] = new string[] { "1)", "2)", "3)" },
            ["括号数字"] = new string[] { "(1)", "(2)", "(3)" },
            ["圆圈数字"] = new string[] { "①", "②", "③" },
            ["大写字母"] = new string[] { "A.", "B.", "C." },
            ["小写字母"] = new string[] { "a.", "b.", "c." },
            ["大写罗马"] = new string[] { "I.", "II.", "III." },
            ["小写罗马"] = new string[] { "i.", "ii.", "iii." },
            ["中文数字"] = new string[] { "一.", "二.", "三." }
        };

        // 项目符号样式
        private readonly Dictionary<string, string[]> bulletStyles = new Dictionary<string, string[]>
        {
            ["无"] = new string[] { "无符号" },
            ["实心圆"] = new string[] { "●", "●", "●" },
            ["空心圆"] = new string[] { "○", "○", "○" },
            ["实心方"] = new string[] { "■", "■", "■" },
            ["空心方"] = new string[] { "□", "□", "□" },
            ["菱形"] = new string[] { "♦", "♦", "♦" },
            ["三角"] = new string[] { "►", "►", "►" },
            ["对勾"] = new string[] { "✓", "✓", "✓" },
            ["星号"] = new string[] { "★", "★", "★" }
        };

        // 多级编号样式
        private readonly Dictionary<string, string[]> multiLevelStyles = new Dictionary<string, string[]>
        {
            ["标准"] = new string[] { "1.", "1.1.", "1.1.1." },
            ["大纲"] = new string[] { "第一章", "第一节", "第一条" },
            ["法律"] = new string[] { "第一章", "1.1", "1.1.1" },
            ["项目"] = new string[] { "1.", "1.1", "1.1.1" },
            ["混合"] = new string[] { "1.", "a.", "i." }
        };

        public NumberingDialog()
        {
            InitializeComponent();
            InitializeContent();
        }

        private void InitializeContent()
        {
            LoadNumberingStyles();
            LoadBulletStyles();
            LoadMultiLevelStyles();
        }

        private void LoadNumberingStyles()
        {
            numberingPanel.Children.Clear();

            foreach (var style in numberingStyles)
            {
                var button = CreateNumberingButton(style.Key, style.Value, "numbering");
                numberingPanel.Children.Add(button);
            }
        }

        private void LoadBulletStyles()
        {
            bulletsPanel.Children.Clear();

            foreach (var style in bulletStyles)
            {
                var button = CreateNumberingButton(style.Key, style.Value, "bullet");
                bulletsPanel.Children.Add(button);
            }
        }

        private void LoadMultiLevelStyles()
        {
            multiLevelPanel.Children.Clear();

            foreach (var style in multiLevelStyles)
            {
                var button = CreateNumberingButton(style.Key, style.Value, "multilevel");
                multiLevelPanel.Children.Add(button);
            }
        }

        private Button CreateNumberingButton(string styleName, string[] examples, string type)
        {
            var button = new Button
            {
                Style = (Style)FindResource("NumberingButtonStyle"),
                Tag = type + "_" + styleName
            };

            var stackPanel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 添加样式名称
            var nameText = new TextBlock
            {
                Text = styleName,
                FontSize = 10,
                Foreground = new SolidColorBrush(Color.FromRgb(0x5F, 0x63, 0x68)),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 4)
            };
            stackPanel.Children.Add(nameText);

            // 添加示例
            foreach (var example in examples)
            {
                var exampleText = new TextBlock
                {
                    Text = example,
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(0x20, 0x21, 0x24)),
                    HorizontalAlignment = HorizontalAlignment.Left,
                    Margin = new Thickness(0, 1, 0, 1)
                };
                stackPanel.Children.Add(exampleText);
            }

            button.Content = stackPanel;
            button.Click += (s, e) => SelectStyle(button, styleName, type);

            return button;
        }

        private void SelectStyle(Button button, string styleName, string type)
        {
            // 清除同类型按钮的选中状态
            ClearSelection(type);

            // 设置当前按钮为选中状态
            button.Tag = "Selected";

            // 保存选择
            switch (type)
            {
                case "numbering":
                    SelectedNumberingStyle = styleName;
                    break;
                case "bullet":
                    SelectedBulletStyle = styleName;
                    break;
                case "multilevel":
                    SelectedMultiLevelStyle = styleName;
                    break;
            }
        }

        private void ClearSelection(string type)
        {
            WrapPanel? panel = null;
            switch (type)
            {
                case "numbering":
                    panel = numberingPanel;
                    break;
                case "bullet":
                    panel = bulletsPanel;
                    break;
                case "multilevel":
                    panel = multiLevelPanel;
                    break;
            }

            if (panel != null)
            {
                foreach (Button btn in panel.Children)
                {
                    btn.Tag = null;
                }
            }
        }

        private void BtnNumberingTab_Click(object sender, RoutedEventArgs e)
        {
            SetActiveTab(btnNumberingTab);
            ShowPanel(numberingPanel);
        }

        private void BtnBulletsTab_Click(object sender, RoutedEventArgs e)
        {
            SetActiveTab(btnBulletsTab);
            ShowPanel(bulletsPanel);
        }

        private void BtnMultiLevelTab_Click(object sender, RoutedEventArgs e)
        {
            SetActiveTab(btnMultiLevelTab);
            ShowPanel(multiLevelPanel);
        }

        private void SetActiveTab(Button activeButton)
        {
            // 重置所有选项卡
            btnNumberingTab.Tag = null;
            btnBulletsTab.Tag = null;
            btnMultiLevelTab.Tag = null;

            // 设置活动选项卡
            activeButton.Tag = "Selected";
        }

        private void ShowPanel(WrapPanel activePanel)
        {
            // 隐藏所有面板
            numberingPanel.Visibility = Visibility.Collapsed;
            bulletsPanel.Visibility = Visibility.Collapsed;
            multiLevelPanel.Visibility = Visibility.Collapsed;

            // 显示活动面板
            activePanel.Visibility = Visibility.Visible;
        }

        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
