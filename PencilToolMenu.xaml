<UserControl x:Class="像素喵笔记.PencilToolMenu"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:像素喵笔记"
             mc:Ignorable="d"
             d:DesignHeight="80" d:DesignWidth="400">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Border Background="White"
           CornerRadius="25"
           Padding="15,10"
           RenderTransformOrigin="0.5,0.5">
        <Border.RenderTransform>
            <ScaleTransform ScaleX="1" ScaleY="1"/>
        </Border.RenderTransform>
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="12"/>
        </Border.Effect>

        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">

            <!-- 铅笔工具 -->
            <Button x:Name="btnPencil"
                   Style="{StaticResource FigmaToolButtonStyle}"
                   Width="40" Height="40"
                   Margin="0,0,8,0"
                   Click="BtnPencil_Click"
                   ToolTip="铅笔">
                <Path Data="M3,17.25V21h3.75L17.81,9.94l-3.75-3.75L3,17.25z M20.71,7.04c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34c-0.39-0.39-1.02-0.39-1.41,0l-1.83,1.83 3.75,3.75L20.71,7.04z"
                     Fill="#333333"
                     Stretch="Uniform"
                     Width="16" Height="16"/>
            </Button>

            <!-- 马克笔工具 -->
            <Button x:Name="btnMarker"
                   Style="{StaticResource FigmaToolButtonStyle}"
                   Width="40" Height="40"
                   Margin="0,0,8,0"
                   Click="BtnMarker_Click"
                   ToolTip="马克笔 (20px方形笔尖)">
                <Path Data="M2,2 L22,2 L22,6 L20,8 L4,8 L2,6 Z M4,8 L4,20 L20,20 L20,8 M8,12 L16,12 M8,16 L16,16"
                     Fill="#333333"
                     Stroke="#333333"
                     StrokeThickness="0.5"
                     Stretch="Uniform"
                     Width="16" Height="16"
                     Opacity="0.7"/>
            </Button>



            <!-- 分割线 -->
            <Rectangle Width="1" Height="30" Fill="#E0E0E0" Margin="8,0"/>

            <!-- 橡皮擦工具 -->
            <Button x:Name="btnEraser"
                   Style="{StaticResource FigmaToolButtonStyle}"
                   Width="40" Height="40"
                   Margin="8,0,8,0"
                   Click="BtnEraser_Click"
                   ToolTip="橡皮擦">
                <Path Data="M16,2 L20,6 L20,8 L18,10 L6,10 L4,8 L4,6 L8,2 Z M6,10 L6,18 C6,19.1 6.9,20 8,20 L16,20 C17.1,20 18,19.1 18,18 L18,10 M10,14 L10,16 M14,14 L14,16"
                     Fill="#FF6B6B"
                     Stroke="#333333"
                     StrokeThickness="0.5"
                     Stretch="Uniform"
                     Width="16" Height="16"/>
            </Button>

            <!-- 分割线 -->
            <Rectangle Width="1" Height="30" Fill="#E0E0E0" Margin="8,0"/>

            <!-- 笔刷大小控制 -->
            <StackPanel Orientation="Horizontal" Margin="8,0,0,0">
                <TextBlock Text="Size:"
                          FontSize="12"
                          Foreground="#666666"
                          VerticalAlignment="Center"
                          Margin="0,0,8,0"/>

                <Slider x:Name="brushSizeSlider"
                       Width="80"
                       Minimum="1"
                       Maximum="20"
                       Value="2"
                       VerticalAlignment="Center"
                       ValueChanged="BrushSizeSlider_ValueChanged"/>

                <TextBlock x:Name="brushSizeText"
                          Text="2px"
                          FontSize="12"
                          Foreground="#666666"
                          VerticalAlignment="Center"
                          Margin="8,0,0,0"
                          MinWidth="30"/>
            </StackPanel>

            <!-- 分割线 -->
            <Rectangle Width="1" Height="30" Fill="#E0E0E0" Margin="8,0"/>

            <!-- 🔧 增强：专业级平滑控制 -->
            <StackPanel Orientation="Horizontal" Margin="8,0,0,0">
                <!-- 平滑模式选择 -->
                <ComboBox x:Name="smoothingModeCombo"
                         Width="80"
                         FontSize="11"
                         VerticalAlignment="Center"
                         SelectionChanged="SmoothingModeCombo_SelectionChanged"
                         Margin="0,0,8,0">
                    <ComboBoxItem Content="关闭" Tag="None"/>
                    <ComboBoxItem Content="基础" Tag="Basic" IsSelected="True"/>
                    <ComboBoxItem Content="抖动修正" Tag="AntiShake"/>
                    <ComboBoxItem Content="稳定器" Tag="Stabilizer"/>
                    <ComboBoxItem Content="专业" Tag="Professional"/>
                </ComboBox>

                <!-- 平滑强度 -->
                <TextBlock Text="强度:"
                          FontSize="12"
                          Foreground="#666666"
                          VerticalAlignment="Center"
                          Margin="0,0,4,0"/>

                <Slider x:Name="smoothingSlider"
                       Width="50"
                       Minimum="0"
                       Maximum="1"
                       Value="0.3"
                       VerticalAlignment="Center"
                       ValueChanged="SmoothingSlider_ValueChanged"/>

                <TextBlock x:Name="smoothingText"
                          Text="30%"
                          FontSize="11"
                          Foreground="#666666"
                          VerticalAlignment="Center"
                          Margin="4,0,8,0"
                          MinWidth="25"/>

                <!-- 高级设置按钮 -->
                <Button x:Name="btnAdvancedSmoothing"
                       Content="⚙"
                       Width="24"
                       Height="24"
                       FontSize="12"
                       Background="Transparent"
                       BorderBrush="#E0E0E0"
                       BorderThickness="1"
                       ToolTip="高级平滑设置"
                       Click="BtnAdvancedSmoothing_Click"/>
            </StackPanel>

            <!-- 分割线 -->
            <Rectangle Width="1" Height="30" Fill="#E0E0E0" Margin="8,0"/>

            <!-- 颜色选择 -->
            <StackPanel Orientation="Horizontal" Margin="8,0,0,0">
                <TextBlock Text="颜色:"
                          FontSize="12"
                          Foreground="#666666"
                          VerticalAlignment="Center"
                          Margin="0,0,8,0"/>

                <Border Width="24" Height="24"
                       Background="Black"
                       BorderBrush="#333333"
                       BorderThickness="1"
                       CornerRadius="4">
                    <Button x:Name="btnColorPicker"
                           Background="Transparent"
                           BorderThickness="0"
                           Click="BtnColorPicker_Click"
                           ToolTip="选择颜色"/>
                </Border>

                <!-- 快速颜色选择 -->
                <StackPanel Orientation="Horizontal" Margin="8,0,0,0">
                    <Border Width="16" Height="16"
                           Background="Black"
                           BorderThickness="1"
                           BorderBrush="#333333"
                           CornerRadius="2"
                           Margin="0,0,2,0">
                        <Button Background="Transparent"
                               BorderThickness="0"
                               Click="QuickColor_Click"
                               Tag="Black"/>
                    </Border>
                    <Border Width="16" Height="16"
                           Background="Red"
                           BorderThickness="1"
                           BorderBrush="#333333"
                           CornerRadius="2"
                           Margin="0,0,2,0">
                        <Button Background="Transparent"
                               BorderThickness="0"
                               Click="QuickColor_Click"
                               Tag="Red"/>
                    </Border>
                    <Border Width="16" Height="16"
                           Background="Blue"
                           BorderThickness="1"
                           BorderBrush="#333333"
                           CornerRadius="2"
                           Margin="0,0,2,0">
                        <Button Background="Transparent"
                               BorderThickness="0"
                               Click="QuickColor_Click"
                               Tag="Blue"/>
                    </Border>
                    <Border Width="16" Height="16"
                           Background="Green"
                           BorderThickness="1"
                           BorderBrush="#333333"
                           CornerRadius="2"
                           Margin="0,0,2,0">
                        <Button Background="Transparent"
                               BorderThickness="0"
                               Click="QuickColor_Click"
                               Tag="Green"/>
                    </Border>
                    <Border Width="16" Height="16"
                           Background="Yellow"
                           BorderThickness="1"
                           BorderBrush="#333333"
                           CornerRadius="2">
                        <Button Background="Transparent"
                               BorderThickness="0"
                               Click="QuickColor_Click"
                               Tag="Yellow"/>
                    </Border>

                    <!-- 自定义颜色按钮 -->
                    <Border Width="20" Height="16"
                           Background="White"
                           BorderThickness="1"
                           BorderBrush="#333333"
                           CornerRadius="2"
                           Margin="4,0,0,0">
                        <Button Background="Transparent"
                               BorderThickness="0"
                               Click="CustomColor_Click"
                               ToolTip="自定义颜色">
                            <TextBlock Text="+" FontSize="10" Foreground="#333333" HorizontalAlignment="Center"/>
                        </Button>
                    </Border>
                </StackPanel>
            </StackPanel>

        </StackPanel>
    </Border>
</UserControl>
