using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace 像素喵笔记
{
    /// <summary>
    /// PencilToolMenu.xaml 的交互逻辑
    /// </summary>
    public partial class PencilToolMenu : UserControl
    {
        public enum PencilToolType
        {
            Penci<PERSON>,
            Marker,
            Eraser
        }

        public event EventHandler<PencilToolType>? ToolSelected;
        public event EventHandler<double>? BrushSizeChanged;
        public event EventHandler<Color>? ColorChanged;
        public event EventHandler<double>? SmoothingChanged;
        public event EventHandler<AdvancedSmoothingEngine.SmoothingMode>? SmoothingModeChanged;
        public event EventHandler<AdvancedSmoothingEngine.SmoothingConfig>? SmoothingConfigChanged;

        private PencilToolType _currentTool = PencilToolType.Pencil;
        private Color _currentColor = Colors.Black;
        private bool _isAdvancedMenuVisible = false;
        private AdvancedSmoothingEngine.SmoothingConfig _smoothingConfig = new();

        public PencilToolMenu()
        {
            InitializeComponent();
            SetActiveTool(PencilToolType.Pencil);
        }

        public PencilToolType CurrentTool => _currentTool;
        public double BrushSize => brushSizeSlider.Value;
        public Color CurrentColor => _currentColor;

        private void BtnPencil_Click(object sender, RoutedEventArgs e)
        {
            // 铅笔工具支持3级菜单
            if (_currentTool == PencilToolType.Pencil && !_isAdvancedMenuVisible)
            {
                ShowAdvancedPencilMenu();
            }
            else
            {
                SetActiveTool(PencilToolType.Pencil);
                HideAdvancedPencilMenu();
            }
        }

        private void BtnMarker_Click(object sender, RoutedEventArgs e)
        {
            SetActiveTool(PencilToolType.Marker);
        }



        private void BtnEraser_Click(object sender, RoutedEventArgs e)
        {
            SetActiveTool(PencilToolType.Eraser);
        }

        private void SetActiveTool(PencilToolType tool)
        {
            _currentTool = tool;

            // 重置所有按钮状态
            ResetToolButtonStates();

            // 设置当前工具按钮为激活状态 - 统一使用#0099ff蓝色
            var activeBrush = new SolidColorBrush(Color.FromRgb(0x00, 0x99, 0xFF));
            switch (tool)
            {
                case PencilToolType.Pencil:
                    btnPencil.Background = activeBrush;
                    break;
                case PencilToolType.Marker:
                    btnMarker.Background = activeBrush;
                    // 马克笔固定设置
                    brushSizeSlider.Value = 20;
                    brushSizeSlider.IsEnabled = false;
                    break;
                case PencilToolType.Eraser:
                    btnEraser.Background = activeBrush;
                    break;
            }

            // 恢复画笔大小滑块状态（除了马克笔）
            if (tool != PencilToolType.Marker)
            {
                brushSizeSlider.IsEnabled = true;
            }

            ToolSelected?.Invoke(this, tool);
        }

        private void ResetToolButtonStates()
        {
            var defaultBrush = new SolidColorBrush(Colors.Transparent);
            btnPencil.Background = defaultBrush;
            btnMarker.Background = defaultBrush;
            btnEraser.Background = defaultBrush;
        }

        private void BrushSizeSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (brushSizeText != null)
            {
                brushSizeText.Text = $"{(int)e.NewValue}px";
                BrushSizeChanged?.Invoke(this, e.NewValue);
            }
        }

        private void BtnColorPicker_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现自定义颜色选择器对话框
            // 暂时使用预设颜色
            var colors = new[] { Colors.Black, Colors.Red, Colors.Blue, Colors.Green,
                               Colors.Yellow, Colors.Purple, Colors.Orange, Colors.Brown };

            var random = new Random();
            var randomColor = colors[random.Next(colors.Length)];
            SetColor(randomColor);
        }

        private void QuickColor_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string colorName)
            {
                Color color = colorName switch
                {
                    "Black" => Colors.Black,
                    "Red" => Colors.Red,
                    "Blue" => Colors.Blue,
                    "Green" => Colors.Green,
                    "Yellow" => Colors.Yellow,
                    _ => Colors.Black
                };

                SetColor(color);
            }
        }

        private void SetColor(Color color)
        {
            _currentColor = color;
            btnColorPicker.Background = new SolidColorBrush(color);
            ColorChanged?.Invoke(this, color);
        }

        private void CustomColor_Click(object sender, RoutedEventArgs e)
        {
            var colorPickerDialog = new ColorPickerDialog();
            if (colorPickerDialog.ShowDialog() == true)
            {
                SetColor(colorPickerDialog.SelectedColor);
            }
        }

        public void SetBrushSize(double size)
        {
            brushSizeSlider.Value = Math.Max(brushSizeSlider.Minimum,
                                           Math.Min(brushSizeSlider.Maximum, size));
        }

        private void ShowAdvancedPencilMenu()
        {
            _isAdvancedMenuVisible = true;
            // TODO: 实现高级铅笔菜单显示逻辑
            // 这里可以显示曲线平滑滑块等高级选项
        }

        private void HideAdvancedPencilMenu()
        {
            _isAdvancedMenuVisible = false;
            // TODO: 实现高级铅笔菜单隐藏逻辑
        }

        private void SmoothingSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (smoothingText != null)
            {
                smoothingText.Text = $"{(int)(e.NewValue * 100)}%";
                _smoothingConfig.Strength = e.NewValue;
                SmoothingChanged?.Invoke(this, e.NewValue);
                SmoothingConfigChanged?.Invoke(this, _smoothingConfig);
            }
        }

        private void SmoothingModeCombo_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            if (smoothingModeCombo.SelectedItem is System.Windows.Controls.ComboBoxItem item &&
                item.Tag is string modeString)
            {
                if (Enum.TryParse<AdvancedSmoothingEngine.SmoothingMode>(modeString, out var mode))
                {
                    _smoothingConfig.Mode = mode;
                    SmoothingModeChanged?.Invoke(this, mode);
                    SmoothingConfigChanged?.Invoke(this, _smoothingConfig);
                }
            }
        }

        private void BtnAdvancedSmoothing_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new AdvancedSmoothingDialog(_smoothingConfig);
            if (dialog.ShowDialog() == true)
            {
                _smoothingConfig = dialog.Config;

                // 更新UI显示
                smoothingSlider.Value = _smoothingConfig.Strength;

                // 更新模式选择
                foreach (System.Windows.Controls.ComboBoxItem item in smoothingModeCombo.Items)
                {
                    if (item.Tag?.ToString() == _smoothingConfig.Mode.ToString())
                    {
                        smoothingModeCombo.SelectedItem = item;
                        break;
                    }
                }

                SmoothingConfigChanged?.Invoke(this, _smoothingConfig);
            }
        }

        public AdvancedSmoothingEngine.SmoothingConfig GetSmoothingConfig()
        {
            return _smoothingConfig;
        }

        public void SetSmoothingConfig(AdvancedSmoothingEngine.SmoothingConfig config)
        {
            _smoothingConfig = config;
            smoothingSlider.Value = config.Strength;

            // 更新模式选择
            foreach (System.Windows.Controls.ComboBoxItem item in smoothingModeCombo.Items)
            {
                if (item.Tag?.ToString() == config.Mode.ToString())
                {
                    smoothingModeCombo.SelectedItem = item;
                    break;
                }
            }
        }
    }
}
