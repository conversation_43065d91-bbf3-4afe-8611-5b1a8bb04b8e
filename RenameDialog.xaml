<Window x:Class="像素喵笔记.RenameDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="重命名" Height="318" Width="480"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <!-- Figma风格对话框容器 -->
    <Border Background="White" CornerRadius="12" Margin="20">
        <Border.Effect>
            <DropShadowEffect Color="#000000" Direction="270" ShadowDepth="4" Opacity="0.2" BlurRadius="20"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Figma风格标题栏 -->
            <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="12,12,0,0" Padding="20,16">
                <Grid>
                    <TextBlock Text="重命名" Style="{StaticResource FigmaDialogTitleStyle}"
                               FontSize="16" FontWeight="SemiBold" Foreground="#202124"
                               VerticalAlignment="Center" HorizontalAlignment="Left" Margin="0"/>
                    <Button x:Name="btnClose" Content="✕"
                            Style="{StaticResource FigmaDialogCloseButtonStyle}"
                            HorizontalAlignment="Right" VerticalAlignment="Center"
                            Click="BtnClose_Click"/>
                </Grid>
            </Border>

            <!-- Figma风格内容区域 -->
            <Grid Grid.Row="1" Margin="24,20,24,16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="请输入新名称:"
                           FontFamily="微软雅黑" FontSize="14" Foreground="#5F6368"
                           Margin="0,0,0,12"/>
                <Border Grid.Row="1" BorderThickness="1" BorderBrush="#DADCE0"
                        Background="White" CornerRadius="8" MinHeight="40">
                    <TextBox x:Name="txtNewName"
                             FontFamily="Segoe UI" FontSize="14" Padding="16,10"
                             BorderThickness="0" Background="Transparent"
                             VerticalContentAlignment="Center"
                             Foreground="#202124"/>
                </Border>
            </Grid>

            <!-- Figma风格按钮区域 -->
            <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="0,0,12,12" Padding="24,16">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button x:Name="btnCancel" Content="取消"
                            Style="{StaticResource FigmaDialogCancelButtonStyle}"
                            Margin="0,0,12,0"
                            Click="BtnCancel_Click"/>
                    <Button x:Name="btnOK" Content="确定"
                            Style="{StaticResource FigmaDialogConfirmButtonStyle}"
                            Click="BtnOK_Click"/>
                </StackPanel>
            </Border>
        </Grid>
    </Border>
</Window>
