using System;
using System.Windows;
using System.Windows.Input;

namespace 像素喵笔记
{
    /// <summary>
    /// 重命名对话框
    /// </summary>
    public partial class RenameDialog : Window
    {
        public string NewName { get; private set; } = string.Empty;

        public RenameDialog(string currentName)
        {
            InitializeComponent();
            
            // 设置当前名称
            txtNewName.Text = currentName;
            
            // 绑定事件
            Loaded += (s, e) =>
            {
                txtNewName.Focus();
                txtNewName.SelectAll();
            };
            
            // 支持Enter和Escape键
            txtNewName.KeyDown += (s, e) =>
            {
                if (e.Key == Key.Enter)
                {
                    BtnOK_Click(s, e);
                }
                else if (e.Key == Key.Escape)
                {
                    BtnCancel_Click(s, e);
                }
            };
        }

        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            var newName = txtNewName.Text.Trim();
            if (string.IsNullOrWhiteSpace(newName))
            {
                MessageBox.Show("名称不能为空", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                txtNewName.Focus();
                return;
            }

            NewName = newName;
            DialogResult = true;
            Close();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
