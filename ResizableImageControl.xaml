<UserControl x:Class="像素喵笔记.ResizableImageControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="400">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Grid x:Name="mainGrid" Background="Transparent">
        <!-- 图片显示区域 -->
        <Border x:Name="imageBorder"
                BorderBrush="#E8EAED"
                BorderThickness="1"
                CornerRadius="8"
                Background="White"
                Effect="{StaticResource StandardFigmaShadow}">
            <Image x:Name="imageControl" 
                   Stretch="Uniform"
                   MouseLeftButtonDown="Image_MouseLeftButtonDown"/>
        </Border>

        <!-- 缩放手柄 -->
        <Grid x:Name="resizeHandles" Visibility="Collapsed">
            <!-- 右下角缩放手柄 -->
            <Rectangle x:Name="bottomRightHandle"
                       Width="12" Height="12"
                       Fill="#0099ff"
                       Stroke="White"
                       StrokeThickness="2"
                       HorizontalAlignment="Right"
                       VerticalAlignment="Bottom"
                       Margin="0,0,-6,-6"
                       Cursor="SizeNWSE"
                       MouseLeftButtonDown="BottomRightHandle_MouseLeftButtonDown"
                       MouseLeftButtonUp="Handle_MouseLeftButtonUp"
                       MouseMove="BottomRightHandle_MouseMove">
                <Rectangle.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="2"/>
                </Rectangle.Effect>
            </Rectangle>

            <!-- 右边缩放手柄 -->
            <Rectangle x:Name="rightHandle"
                       Width="8" Height="20"
                       Fill="#0099ff"
                       Stroke="White"
                       StrokeThickness="2"
                       HorizontalAlignment="Right"
                       VerticalAlignment="Center"
                       Margin="0,0,-4,0"
                       Cursor="SizeWE"
                       MouseLeftButtonDown="RightHandle_MouseLeftButtonDown"
                       MouseLeftButtonUp="Handle_MouseLeftButtonUp"
                       MouseMove="RightHandle_MouseMove">
                <Rectangle.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="2"/>
                </Rectangle.Effect>
            </Rectangle>

            <!-- 底边缩放手柄 -->
            <Rectangle x:Name="bottomHandle"
                       Width="20" Height="8"
                       Fill="#0099ff"
                       Stroke="White"
                       StrokeThickness="2"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Bottom"
                       Margin="0,0,0,-4"
                       Cursor="SizeNS"
                       MouseLeftButtonDown="BottomHandle_MouseLeftButtonDown"
                       MouseLeftButtonUp="Handle_MouseLeftButtonUp"
                       MouseMove="BottomHandle_MouseMove">
                <Rectangle.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.2" BlurRadius="2"/>
                </Rectangle.Effect>
            </Rectangle>
        </Grid>
        
        <!-- 编辑模式控制面板 -->
        <StackPanel x:Name="editModePanel"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Margin="0,-30,0,0"
                    Visibility="Collapsed">
            
            <!-- 确认按钮 -->
            <Button x:Name="btnConfirm"
                    Width="24" Height="24"
                    Margin="4,0"
                    Background="#4CAF50"
                    BorderThickness="0"
                    Click="BtnConfirm_Click">
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="12">
                            <Path Data="M9,16.2L4.8,12l-1.4,1.4L9,19L21,7l-1.4-1.4L9,16.2z"
                                  Fill="White"
                                  Stretch="Uniform"
                                  Width="12" Height="12"/>
                        </Border>
                    </ControlTemplate>
                </Button.Template>
                <Button.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.3" BlurRadius="3"/>
                </Button.Effect>
            </Button>
            
            <!-- 取消按钮 -->
            <Button x:Name="btnCancel"
                    Width="24" Height="24"
                    Margin="4,0"
                    Background="#F44336"
                    BorderThickness="0"
                    Click="BtnCancel_Click">
                <Button.Template>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="12">
                            <Path Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41z"
                                  Fill="White"
                                  Stretch="Uniform"
                                  Width="12" Height="12"/>
                        </Border>
                    </ControlTemplate>
                </Button.Template>
                <Button.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.3" BlurRadius="3"/>
                </Button.Effect>
            </Button>
        </StackPanel>
    </Grid>
</UserControl>
