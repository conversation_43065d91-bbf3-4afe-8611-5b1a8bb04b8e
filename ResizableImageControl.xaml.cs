using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace 像素喵笔记
{
    /// <summary>
    /// 可缩放的图片控件
    /// </summary>
    public partial class ResizableImageControl : UserControl
    {
        #region 依赖属性

        /// <summary>
        /// 图片文件路径依赖属性 - 确保XAML序列化时不丢失
        /// </summary>
        public static readonly DependencyProperty ImageFilePathProperty =
            DependencyProperty.Register("ImageFilePath", typeof(string), typeof(ResizableImageControl),
                new PropertyMetadata(string.Empty));

        public string ImageFilePath
        {
            get { return (string)GetValue(ImageFilePathProperty); }
            set { SetValue(ImageFilePathProperty, value); }
        }

        #endregion

        #region 私有字段

        private bool _isResizing = false;
        private Point _startPoint;
        private Size _startSize;
        private bool _isSelected = false;
        private bool _isEditMode = false; // 新增：编辑模式标志

        #endregion

        #region 构造函数

        public ResizableImageControl()
        {
            InitializeComponent();
            this.Loaded += ResizableImageControl_Loaded;
            this.MouseEnter += ResizableImageControl_MouseEnter;
            this.MouseLeave += ResizableImageControl_MouseLeave;
        }

        #endregion

        #region 事件处理

        private void ResizableImageControl_Loaded(object sender, RoutedEventArgs e)
        {
            // 控件加载完成后的初始化
        }

        private void ResizableImageControl_MouseEnter(object sender, MouseEventArgs e)
        {
            if (!_isResizing && !_isEditMode)
            {
                resizeHandles.Visibility = Visibility.Visible;
            }
        }

        private void ResizableImageControl_MouseLeave(object sender, MouseEventArgs e)
        {
            if (!_isResizing && !_isSelected && !_isEditMode)
            {
                resizeHandles.Visibility = Visibility.Collapsed;
            }
        }

        private void Image_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!_isEditMode) // 非编辑模式下才切换选中状态
            {
                _isSelected = !_isSelected;
                resizeHandles.Visibility = _isSelected ? Visibility.Visible : Visibility.Collapsed;
            }

            // 不阻止事件传播，让双击事件能够正常工作
            // e.Handled = true; // 注释掉这行，允许事件冒泡
        }

        #endregion

        #region 缩放手柄事件

        private void BottomRightHandle_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            StartResize(e.GetPosition(this));
            e.Handled = true;
        }

        private void RightHandle_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            StartResize(e.GetPosition(this));
            e.Handled = true;
        }

        private void BottomHandle_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            StartResize(e.GetPosition(this));
            e.Handled = true;
        }

        private void BottomRightHandle_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isResizing && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPoint = e.GetPosition(this);
                var deltaX = currentPoint.X - _startPoint.X;
                var deltaY = currentPoint.Y - _startPoint.Y;

                var newWidth = Math.Max(50, _startSize.Width + deltaX);
                var newHeight = Math.Max(50, _startSize.Height + deltaY);

                this.Width = newWidth;
                this.Height = newHeight;
            }
            e.Handled = true;
        }

        private void RightHandle_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isResizing && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPoint = e.GetPosition(this);
                var deltaX = currentPoint.X - _startPoint.X;

                var newWidth = Math.Max(50, _startSize.Width + deltaX);
                this.Width = newWidth;
            }
        }

        private void BottomHandle_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isResizing && e.LeftButton == MouseButtonState.Pressed)
            {
                var currentPoint = e.GetPosition(this);
                var deltaY = currentPoint.Y - _startPoint.Y;

                var newHeight = Math.Max(50, _startSize.Height + deltaY);
                this.Height = newHeight;
            }
        }

        private void Handle_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            EndResize();
            e.Handled = true;
        }

        #endregion

        #region 私有方法

        private void StartResize(Point startPoint)
        {
            _isResizing = true;
            _startPoint = startPoint;
            _startSize = new Size(this.ActualWidth, this.ActualHeight);
        }

        private void EndResize()
        {
            _isResizing = false;
            Mouse.Capture(null);
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 加载图片文件
        /// </summary>
        public void LoadImageFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException("图片文件不存在", filePath);
                }

                // 使用依赖属性保存路径，确保XAML序列化时不丢失
                ImageFilePath = filePath;
                
                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(filePath, UriKind.Absolute);
                bitmap.CacheOption = BitmapCacheOption.OnLoad; // 立即加载到内存，释放文件句柄
                bitmap.EndInit();
                bitmap.Freeze(); // 冻结对象，释放资源

                imageControl.Source = bitmap;

                // 设置初始大小 - 优先使用原始尺寸，只对过大图片进行限制
                const double maxWidth = 800;
                const double maxHeight = 600;

                if (bitmap.PixelWidth > maxWidth || bitmap.PixelHeight > maxHeight)
                {
                    var scaleX = maxWidth / bitmap.PixelWidth;
                    var scaleY = maxHeight / bitmap.PixelHeight;
                    var scale = Math.Min(scaleX, scaleY);

                    this.Width = bitmap.PixelWidth * scale;
                    this.Height = bitmap.PixelHeight * scale;
                }
                else
                {
                    // 使用原始尺寸
                    this.Width = bitmap.PixelWidth;
                    this.Height = bitmap.PixelHeight;
                }

                System.Diagnostics.Debug.WriteLine($"图片已加载: {filePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载图片失败: {ex.Message}");
                MessageBox.Show($"加载图片失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取图片文件路径
        /// </summary>
        public string GetImageFilePath()
        {
            return ImageFilePath;
        }

        /// <summary>
        /// 进入编辑模式
        /// </summary>
        public void EnterEditMode()
        {
            _isEditMode = true;
            _isSelected = true;
            resizeHandles.Visibility = Visibility.Visible;
            
            // 显示编辑模式控制按钮
            editModePanel.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 退出编辑模式
        /// </summary>
        public void ExitEditMode()
        {
            _isEditMode = false;
            _isSelected = false;
            resizeHandles.Visibility = Visibility.Collapsed;
            
            // 隐藏编辑模式控制按钮
            editModePanel.Visibility = Visibility.Collapsed;
        }

        #endregion

        #region 编辑模式按钮事件处理

        private void BtnConfirm_Click(object sender, RoutedEventArgs e)
        {
            ExitEditMode();
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            // 这里可以添加取消编辑的逻辑，如恢复原始尺寸
            ExitEditMode();
        }

        #endregion
    }
}
