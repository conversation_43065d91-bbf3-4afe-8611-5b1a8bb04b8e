using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Documents;
using System.Windows.Markup;
using System.Diagnostics;

namespace 像素喵笔记
{
    /// <summary>
    /// 保存管理器 - 纯文件夹结构方案
    /// 严格三层结构：父节点 → 子节点 → 孙节点
    /// </summary>
    public class SaveManager
    {
        private readonly string _saveRootPath;
        private readonly string _attachmentsFolderName = "附件";

        public SaveManager()
        {
            _saveRootPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SAVE");

            // 确保保存目录存在
            if (!Directory.Exists(_saveRootPath))
            {
                Directory.CreateDirectory(_saveRootPath);
                Debug.WriteLine($"创建保存目录: {_saveRootPath}");
            }
        }

        /// <summary>
        /// 加载所有节点 - 直接从文件夹结构构建（简化版）
        /// </summary>
        public async Task<List<TreeNodeData>> LoadAllNodesAsync()
        {
            try
            {
                Debug.WriteLine("=== 开始加载节点（直接文件夹结构）===");

                var rootNodes = new List<TreeNodeData>();

                if (!Directory.Exists(_saveRootPath))
                {
                    Debug.WriteLine("保存目录不存在，创建空目录");
                    Directory.CreateDirectory(_saveRootPath);
                    return rootNodes;
                }

                // 直接递归加载所有文件夹
                var rootDirs = Directory.GetDirectories(_saveRootPath);
                Debug.WriteLine($"SAVE根目录中找到 {rootDirs.Length} 个文件夹");

                foreach (var rootDir in rootDirs)
                {
                    var rootNode = LoadNodeRecursive(rootDir, null, 0);
                    if (rootNode != null)
                    {
                        rootNodes.Add(rootNode);
                        Debug.WriteLine($"加载根节点: {rootNode.Name} (子节点: {rootNode.Children.Count})");
                    }
                }

                // 打印完整的节点树结构
                Debug.WriteLine($"=== 节点加载完成，共 {rootNodes.Count} 个根节点 ===");
                PrintNodeTree(rootNodes);
                return rootNodes;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载节点失败: {ex.Message}");
                return new List<TreeNodeData>();
            }
        }

        /// <summary>
        /// 递归加载节点 - 直接基于文件夹结构
        /// </summary>
        private TreeNodeData? LoadNodeRecursive(string folderPath, TreeNodeData? parent, int depth)
        {
            try
            {
                var folderName = Path.GetFileName(folderPath);

                // 跳过附件文件夹和隐藏文件夹
                if (folderName == _attachmentsFolderName || folderName.StartsWith("."))
                {
                    Debug.WriteLine($"跳过特殊文件夹: {folderName}");
                    return null;
                }

                // 确定节点类型
                string nodeType;
                if (depth == 0)
                    nodeType = "Notebook";
                else if (depth == 1)
                    nodeType = "Collection";
                else
                {
                    // 检查是否有内容文件来确定页面类型
                    var hasRtf = File.Exists(Path.Combine(folderPath, "document.rtf"));
                    var hasXaml = File.Exists(Path.Combine(folderPath, "document.xaml"));
                    var hasCode = File.Exists(Path.Combine(folderPath, "code.json"));

                    if (hasCode)
                        nodeType = "Code";
                    else if (hasRtf || hasXaml)
                        nodeType = "Document";
                    else
                        nodeType = "Page"; // 默认页面类型

                    Debug.WriteLine($"页面节点 {folderName}: RTF={hasRtf}, XAML={hasXaml}, Code={hasCode}, 类型={nodeType}");
                }

                var node = new TreeNodeData
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = folderName,
                    NodeType = nodeType,
                    Parent = parent
                };

                // 递归加载子文件夹
                var subDirs = Directory.GetDirectories(folderPath);
                foreach (var subDir in subDirs)
                {
                    var childNode = LoadNodeRecursive(subDir, node, depth + 1);
                    if (childNode != null)
                    {
                        node.Children.Add(childNode);
                    }
                }

                Debug.WriteLine($"加载节点: {folderName} (深度: {depth}, 类型: {nodeType}, 子节点: {node.Children.Count})");
                return node;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载节点失败: {folderPath}, {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 加载父节点
        /// </summary>
        private TreeNodeData? LoadParentNode(string parentPath)
        {
            try
            {
                var parentName = Path.GetFileName(parentPath);

                // 跳过无效目录
                if (string.IsNullOrEmpty(parentName) || parentName.StartsWith("."))
                {
                    return null;
                }

                var parentNode = new TreeNodeData
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = parentName,
                    NodeType = "Notebook",
                    Parent = null
                };

                // 加载子节点（深度1）
                var childDirs = Directory.GetDirectories(parentPath);
                foreach (var childDir in childDirs)
                {
                    var childDirName = Path.GetFileName(childDir);

                    // 跳过附件文件夹和无效目录
                    if (childDirName == _attachmentsFolderName || childDirName.StartsWith("."))
                    {
                        continue;
                    }

                    var childNode = LoadChildNode(childDir, parentNode);
                    if (childNode != null)
                    {
                        parentNode.Children.Add(childNode);
                    }
                }

                return parentNode;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载父节点失败: {parentPath}, {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 加载子节点
        /// </summary>
        private TreeNodeData? LoadChildNode(string childPath, TreeNodeData parent)
        {
            try
            {
                var childName = Path.GetFileName(childPath);

                var childNode = new TreeNodeData
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = childName,
                    NodeType = "Collection",
                    Parent = parent
                };

                // 加载孙节点（深度2）
                var grandChildDirs = Directory.GetDirectories(childPath);
                foreach (var grandChildDir in grandChildDirs)
                {
                    var grandChildDirName = Path.GetFileName(grandChildDir);

                    // 跳过附件文件夹和无效目录
                    if (grandChildDirName == _attachmentsFolderName || grandChildDirName.StartsWith("."))
                    {
                        continue;
                    }

                    var grandChildNode = LoadGrandChildNode(grandChildDir, childNode);
                    if (grandChildNode != null)
                    {
                        childNode.Children.Add(grandChildNode);
                    }
                }

                return childNode;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载子节点失败: {childPath}, {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 加载孙节点
        /// </summary>
        private TreeNodeData? LoadGrandChildNode(string grandChildPath, TreeNodeData parent)
        {
            try
            {
                var grandChildName = Path.GetFileName(grandChildPath);

                // 验证孙节点内容 - 支持RTF和XAML格式
                var hasRtfDocumentFile = File.Exists(Path.Combine(grandChildPath, "document.rtf"));
                var hasXamlDocumentFile = File.Exists(Path.Combine(grandChildPath, "document.xaml"));
                var hasCodeFile = File.Exists(Path.Combine(grandChildPath, "code.json"));

                if (!hasRtfDocumentFile && !hasXamlDocumentFile && !hasCodeFile)
                {
                    Debug.WriteLine($"孙节点无有效内容文件，跳过: {grandChildPath}");
                    return null;
                }

                // 确定节点类型
                var nodeType = (hasRtfDocumentFile || hasXamlDocumentFile) ? "Document" : "Code";

                Debug.WriteLine($"加载孙节点: {grandChildName}, 类型: {nodeType}, RTF: {hasRtfDocumentFile}, XAML: {hasXamlDocumentFile}, Code: {hasCodeFile}");

                var grandChildNode = new TreeNodeData
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = grandChildName,
                    NodeType = nodeType,
                    Parent = parent
                };

                return grandChildNode;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载孙节点失败: {grandChildPath}, {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 清理无效的文件夹
        /// </summary>
        private void CleanupInvalidDirectories()
        {
            try
            {
                Debug.WriteLine("=== 开始清理无效文件夹 ===");

                if (!Directory.Exists(_saveRootPath))
                {
                    return;
                }

                var rootDirs = Directory.GetDirectories(_saveRootPath);

                foreach (var rootDir in rootDirs)
                {
                    // 检查是否是孤立的孙节点文件夹（包含document.xaml或code.json）
                    var hasDocumentFile = File.Exists(Path.Combine(rootDir, "document.xaml"));
                    var hasCodeFile = File.Exists(Path.Combine(rootDir, "code.json"));

                    if (hasDocumentFile || hasCodeFile)
                    {
                        Debug.WriteLine($"发现孤立的孙节点文件夹，删除: {rootDir}");
                        try
                        {
                            Directory.Delete(rootDir, true);
                        }
                        catch (Exception deleteEx)
                        {
                            Debug.WriteLine($"删除孤立文件夹失败: {deleteEx.Message}");
                        }
                    }
                }

                Debug.WriteLine("=== 无效文件夹清理完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"清理无效文件夹失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取节点路径
        /// </summary>
        public string GetNodePath(TreeNodeData nodeData)
        {
            try
            {
                var pathParts = new List<string>();
                var current = nodeData;

                // 向上遍历到根节点
                while (current != null)
                {
                    pathParts.Insert(0, current.Name);
                    current = current.Parent;
                }

                // 构建完整路径
                string fullPath = _saveRootPath;
                foreach (var part in pathParts)
                {
                    fullPath = Path.Combine(fullPath, part);
                }

                return fullPath;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取节点路径失败: {ex.Message}");
                return _saveRootPath;
            }
        }

        /// <summary>
        /// 创建节点文件夹
        /// </summary>
        public async Task<bool> CreateNodeAsync(TreeNodeData nodeData)
        {
            try
            {
                var nodePath = GetNodePath(nodeData);

                if (!Directory.Exists(nodePath))
                {
                    Directory.CreateDirectory(nodePath);
                    Debug.WriteLine($"创建节点文件夹: {nodePath}");
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建节点失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 删除节点 - 改进版，使用文件引用管理器安全删除
        /// </summary>
        public bool DeleteNode(TreeNodeData nodeData)
        {
            try
            {
                var nodePath = GetNodePath(nodeData);
                Debug.WriteLine($"=== 开始删除节点 ===");
                Debug.WriteLine($"节点名称: {nodeData.Name}");
                Debug.WriteLine($"节点类型: {nodeData.NodeType}");
                Debug.WriteLine($"计算路径: {nodePath}");
                Debug.WriteLine($"路径是否存在: {Directory.Exists(nodePath)}");

                if (!Directory.Exists(nodePath))
                {
                    Debug.WriteLine($"警告：节点路径不存在，可能已被删除: {nodePath}");
                    return true; // 如果文件夹不存在，认为删除成功
                }

                // 🔧 新增：使用文件引用管理器安全清理附件
                Debug.WriteLine("开始安全清理节点附件...");
                FileReferenceManager.Instance.CleanupNodeAttachments(nodePath);

                // 列出剩余要删除的内容
                try
                {
                    var files = Directory.GetFiles(nodePath, "*", SearchOption.AllDirectories);
                    var dirs = Directory.GetDirectories(nodePath, "*", SearchOption.AllDirectories);
                    Debug.WriteLine($"附件清理后，剩余 {files.Length} 个文件和 {dirs.Length} 个子目录");

                    foreach (var file in files.Take(5)) // 只显示前5个文件
                    {
                        Debug.WriteLine($"  剩余文件: {file}");
                    }
                }
                catch (Exception listEx)
                {
                    Debug.WriteLine($"列出剩余文件失败: {listEx.Message}");
                }

                // 尝试删除整个节点目录
                Debug.WriteLine($"开始删除节点目录: {nodePath}");
                Directory.Delete(nodePath, true);

                // 验证删除结果
                Thread.Sleep(100); // 等待文件系统操作完成
                bool stillExists = Directory.Exists(nodePath);
                Debug.WriteLine($"删除后验证 - 目录是否仍存在: {stillExists}");

                if (stillExists)
                {
                    Debug.WriteLine($"警告：标准删除失败，目录仍然存在，尝试强制删除");
                    return ForceDeleteDirectory(nodePath);
                }

                Debug.WriteLine($"节点删除成功: {nodePath}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"删除节点失败: {ex.Message}");
                Debug.WriteLine($"异常类型: {ex.GetType().Name}");
                Debug.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 尝试强制删除
                var nodePath = GetNodePath(nodeData);
                if (Directory.Exists(nodePath))
                {
                    Debug.WriteLine("尝试强制删除...");
                    return ForceDeleteDirectory(nodePath);
                }

                return false;
            }
        }

        /// <summary>
        /// 强制删除目录
        /// </summary>
        private bool ForceDeleteDirectory(string dirPath)
        {
            try
            {
                Debug.WriteLine($"=== 开始强制删除 ===");
                Debug.WriteLine($"目标路径: {dirPath}");

                if (!Directory.Exists(dirPath))
                {
                    Debug.WriteLine("目录不存在，强制删除成功");
                    return true;
                }

                // 方法1：使用文件引用管理器安全删除附件文件
                try
                {
                    var attachmentPath = Path.Combine(dirPath, _attachmentsFolderName);
                    if (Directory.Exists(attachmentPath))
                    {
                        Debug.WriteLine($"使用文件引用管理器清理附件: {attachmentPath}");
                        FileReferenceManager.Instance.CleanupNodeAttachments(dirPath);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"文件引用管理器清理失败: {ex.Message}");
                }

                // 方法2：强制删除剩余文件（带重试机制）
                try
                {
                    var files = Directory.GetFiles(dirPath, "*", SearchOption.AllDirectories);
                    Debug.WriteLine($"强制删除剩余 {files.Length} 个文件");

                    foreach (var file in files)
                    {
                        bool fileDeleted = false;
                        for (int retry = 0; retry < 3 && !fileDeleted; retry++)
                        {
                            try
                            {
                                // 确保文件不是只读的
                                if (File.Exists(file))
                                {
                                    File.SetAttributes(file, FileAttributes.Normal);
                                    File.Delete(file);
                                    fileDeleted = true;
                                    Debug.WriteLine($"删除文件成功: {file}");
                                }
                                else
                                {
                                    fileDeleted = true; // 文件已不存在
                                }
                            }
                            catch (Exception fileEx)
                            {
                                Debug.WriteLine($"删除文件失败 (重试 {retry + 1}/3): {file}, {fileEx.Message}");
                                if (retry < 2)
                                {
                                    // 强制垃圾回收，释放可能的文件句柄
                                    GC.Collect();
                                    GC.WaitForPendingFinalizers();
                                    Thread.Sleep(100);
                                }
                            }
                        }

                        if (!fileDeleted)
                        {
                            Debug.WriteLine($"文件删除最终失败: {file}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"删除文件阶段失败: {ex.Message}");
                }

                // 方法2：删除所有子目录（带重试机制）
                try
                {
                    var dirs = Directory.GetDirectories(dirPath, "*", SearchOption.AllDirectories)
                                      .OrderByDescending(d => d.Length); // 从最深层开始删除
                    Debug.WriteLine($"强制删除 {dirs.Count()} 个子目录");

                    foreach (var dir in dirs)
                    {
                        bool dirDeleted = false;
                        for (int retry = 0; retry < 3 && !dirDeleted; retry++)
                        {
                            try
                            {
                                if (Directory.Exists(dir))
                                {
                                    Directory.Delete(dir, false);
                                    dirDeleted = true;
                                    Debug.WriteLine($"删除子目录成功: {dir}");
                                }
                                else
                                {
                                    dirDeleted = true; // 目录已不存在
                                }
                            }
                            catch (Exception dirEx)
                            {
                                Debug.WriteLine($"删除子目录失败 (重试 {retry + 1}/3): {dir}, {dirEx.Message}");
                                if (retry < 2)
                                {
                                    Thread.Sleep(100);
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"删除子目录阶段失败: {ex.Message}");
                }

                // 方法3：删除根目录（带重试机制）
                bool rootDeleted = false;
                for (int retry = 0; retry < 3 && !rootDeleted; retry++)
                {
                    try
                    {
                        if (Directory.Exists(dirPath))
                        {
                            Directory.Delete(dirPath, false);
                            rootDeleted = true;
                            Debug.WriteLine("根目录删除成功");
                        }
                        else
                        {
                            rootDeleted = true; // 目录已不存在
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"删除根目录失败 (重试 {retry + 1}/3): {ex.Message}");
                        if (retry < 2)
                        {
                            Thread.Sleep(200);
                        }
                    }
                }

                // 最终验证
                Thread.Sleep(200);
                bool stillExists = Directory.Exists(dirPath);
                Debug.WriteLine($"强制删除后验证 - 目录是否仍存在: {stillExists}");

                if (stillExists)
                {
                    Debug.WriteLine($"强制删除失败，目录仍然存在: {dirPath}");
                    return false;
                }

                Debug.WriteLine($"强制删除成功: {dirPath}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"强制删除异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重命名节点
        /// </summary>
        public bool RenameNode(TreeNodeData nodeData, string newName)
        {
            try
            {
                var oldPath = GetNodePath(nodeData);

                // 更新节点名称
                nodeData.Name = newName;
                var newPath = GetNodePath(nodeData);

                if (Directory.Exists(oldPath) && oldPath != newPath)
                {
                    Directory.Move(oldPath, newPath);
                    Debug.WriteLine($"重命名节点: {oldPath} -> {newPath}");
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"重命名节点失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重命名节点文件夹 - 兼容性方法
        /// </summary>
        public async Task<bool> RenameNodeFolderAsync(TreeNodeData nodeData, string oldName, string newName)
        {
            return RenameNode(nodeData, newName);
        }

        /// <summary>
        /// 更新子节点路径 - 兼容性方法
        /// </summary>
        public async Task UpdateChildNodePathsAsync(TreeNodeData nodeData)
        {
            // 纯文件夹结构不需要特殊处理
            await Task.CompletedTask;
        }

        /// <summary>
        /// 创建节点文件夹 - 兼容性方法
        /// </summary>
        public async Task<bool> CreateNodeFolderAsync(TreeNodeData nodeData)
        {
            return await CreateNodeAsync(nodeData);
        }

        /// <summary>
        /// 加载节点内容 - 兼容性方法
        /// </summary>
        public async Task<(object? content, string nodeType)> LoadNodeContentAsync(TreeNodeData nodeData)
        {
            try
            {
                var nodePath = GetNodePath(nodeData);

                // 检查RTF文档文件（新格式）
                var rtfDocumentPath = Path.Combine(nodePath, "document.rtf");
                if (File.Exists(rtfDocumentPath))
                {
                    var rtfContent = await File.ReadAllTextAsync(rtfDocumentPath);
                    return (rtfContent, "Document");
                }

                // 检查旧的XAML文档文件（向后兼容）
                var documentPath = Path.Combine(nodePath, "document.xaml");
                if (File.Exists(documentPath))
                {
                    var xamlContent = await File.ReadAllTextAsync(documentPath);
                    return (xamlContent, "Document");
                }

                // 检查代码文件
                var codePath = Path.Combine(nodePath, "code.json");
                if (File.Exists(codePath))
                {
                    var codeContent = await File.ReadAllTextAsync(codePath);
                    return (codeContent, "Code");
                }

                return (null, "Unknown");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载节点内容失败: {ex.Message}");
                return (null, "Error");
            }
        }

        /// <summary>
        /// 保存文档 - 兼容性方法
        /// </summary>
        public async Task<bool> SaveDocumentAsync(TreeNodeData nodeData, string xamlContent)
        {
            try
            {
                var nodePath = GetNodePath(nodeData);
                var documentPath = Path.Combine(nodePath, "document.xaml");

                if (!Directory.Exists(nodePath))
                {
                    Directory.CreateDirectory(nodePath);
                }

                await File.WriteAllTextAsync(documentPath, xamlContent);
                Debug.WriteLine($"保存文档成功: {documentPath}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存文档失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 保存代码 - 兼容性方法
        /// </summary>
        public async Task<bool> SaveCodeAsync(TreeNodeData nodeData, string codeContent, string language = "")
        {
            try
            {
                var nodePath = GetNodePath(nodeData);
                var codePath = Path.Combine(nodePath, "code.json");

                if (!Directory.Exists(nodePath))
                {
                    Directory.CreateDirectory(nodePath);
                }

                // 创建包含代码和语言信息的JSON对象
                var codeData = new
                {
                    Code = codeContent,
                    Language = language,
                    LastModified = DateTime.Now
                };

                var jsonContent = System.Text.Json.JsonSerializer.Serialize(codeData, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });

                await File.WriteAllTextAsync(codePath, jsonContent);
                Debug.WriteLine($"保存代码成功: {codePath}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存代码失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 保存附件 - 改进版，使用智能文件管理
        /// </summary>
        public async Task<string> SaveAttachmentAsync(TreeNodeData nodeData, string sourceFilePath)
        {
            try
            {
                if (!File.Exists(sourceFilePath))
                {
                    Debug.WriteLine($"源文件不存在: {sourceFilePath}");
                    return string.Empty;
                }

                var nodePath = GetNodePath(nodeData);

                // 🔧 使用文件引用管理器的智能复制功能
                var copiedPath = FileReferenceManager.Instance.SmartCopyFileToNode(sourceFilePath, nodePath);

                if (!string.IsNullOrEmpty(copiedPath))
                {
                    Debug.WriteLine($"智能保存附件成功: {sourceFilePath} -> {copiedPath}");
                    return copiedPath;
                }
                else
                {
                    Debug.WriteLine($"智能保存附件失败，回退到传统方法: {sourceFilePath}");
                    // 回退到传统方法
                    return await SaveAttachmentLegacy(nodeData, sourceFilePath);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存附件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 传统的保存附件方法 - 作为备用
        /// </summary>
        private async Task<string> SaveAttachmentLegacy(TreeNodeData nodeData, string sourceFilePath)
        {
            try
            {
                var nodePath = GetNodePath(nodeData);
                var attachmentsPath = Path.Combine(nodePath, _attachmentsFolderName);

                if (!Directory.Exists(attachmentsPath))
                {
                    Directory.CreateDirectory(attachmentsPath);
                }

                var fileName = Path.GetFileName(sourceFilePath);
                var targetPath = Path.Combine(attachmentsPath, fileName);

                // 如果目标文件已存在，生成新的文件名
                if (File.Exists(targetPath))
                {
                    var nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                    var extension = Path.GetExtension(fileName);
                    var counter = 1;

                    do
                    {
                        fileName = $"{nameWithoutExt}_{counter}{extension}";
                        targetPath = Path.Combine(attachmentsPath, fileName);
                        counter++;
                    } while (File.Exists(targetPath));
                }

                File.Copy(sourceFilePath, targetPath);

                Debug.WriteLine($"传统保存附件成功: {sourceFilePath} -> {targetPath}");
                return targetPath;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"传统保存附件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 保存附件 - 兼容性方法（从字节数组）
        /// </summary>
        public async Task<string> SaveAttachmentAsync(TreeNodeData nodeData, string fileName, byte[] fileData)
        {
            try
            {
                var nodePath = GetNodePath(nodeData);
                var attachmentsPath = Path.Combine(nodePath, _attachmentsFolderName);

                if (!Directory.Exists(attachmentsPath))
                {
                    Directory.CreateDirectory(attachmentsPath);
                }

                var filePath = Path.Combine(attachmentsPath, fileName);
                await File.WriteAllBytesAsync(filePath, fileData);

                Debug.WriteLine($"保存附件成功: {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存附件失败: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取文档路径 - 用于RichTextBox RTF格式
        /// </summary>
        public string GetDocumentPath(TreeNodeData nodeData)
        {
            var nodePath = GetNodePath(nodeData);
            return Path.Combine(nodePath, "document.rtf");
        }

        /// <summary>
        /// 打印节点树结构 - 用于调试
        /// </summary>
        private void PrintNodeTree(List<TreeNodeData> nodes, int depth = 0)
        {
            foreach (var node in nodes)
            {
                var indent = new string(' ', depth * 2);
                Debug.WriteLine($"{indent}- {node.Name} ({node.NodeType}) [子节点: {node.Children.Count}]");

                if (node.Children.Count > 0)
                {
                    PrintNodeTree(node.Children, depth + 1);
                }
            }
        }

        /// <summary>
        /// 单例实例 - 兼容性属性
        /// </summary>
        public static SaveManager Instance { get; } = new SaveManager();
    }
}