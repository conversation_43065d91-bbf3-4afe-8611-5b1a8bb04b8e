using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace 像素喵笔记.Services
{
    /// <summary>
    /// 树节点服务接口
    /// </summary>
    public interface ITreeNodeService
    {
        /// <summary>
        /// 创建笔记本
        /// </summary>
        /// <param name="name">笔记本名称</param>
        /// <returns>创建的笔记本节点</returns>
        Task<NotebookNode> CreateNotebookAsync(string? name = null);

        /// <summary>
        /// 创建集合
        /// </summary>
        /// <param name="parent">父节点</param>
        /// <param name="name">集合名称</param>
        /// <returns>创建的集合节点</returns>
        Task<CollectionNode> CreateCollectionAsync(NotebookNode parent, string? name = null);

        /// <summary>
        /// 创建页面
        /// </summary>
        /// <param name="parent">父节点</param>
        /// <param name="name">页面名称</param>
        /// <returns>创建的页面节点</returns>
        Task<PageNode> CreatePageAsync(CollectionNode parent, string? name = null);

        /// <summary>
        /// 创建代码页
        /// </summary>
        /// <param name="parent">父节点</param>
        /// <param name="name">代码页名称</param>
        /// <returns>创建的代码页节点</returns>
        Task<CodePageNode> CreateCodePageAsync(CollectionNode parent, string? name = null);

        /// <summary>
        /// 删除节点
        /// </summary>
        /// <param name="node">要删除的节点</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteNodeAsync(BaseNode node);

        /// <summary>
        /// 重命名节点
        /// </summary>
        /// <param name="node">要重命名的节点</param>
        /// <param name="newName">新名称</param>
        /// <returns>是否重命名成功</returns>
        Task<bool> RenameNodeAsync(BaseNode node, string newName);

        /// <summary>
        /// 移动节点
        /// </summary>
        /// <param name="node">要移动的节点</param>
        /// <param name="newParent">新父节点</param>
        /// <returns>是否移动成功</returns>
        Task<bool> MoveNodeAsync(BaseNode node, BaseNode? newParent);

        /// <summary>
        /// 复制节点
        /// </summary>
        /// <param name="node">要复制的节点</param>
        /// <param name="targetParent">目标父节点</param>
        /// <returns>复制的节点</returns>
        Task<BaseNode> CopyNodeAsync(BaseNode node, BaseNode? targetParent);

        /// <summary>
        /// 获取节点统计信息
        /// </summary>
        /// <param name="node">节点</param>
        /// <returns>统计信息</returns>
        Task<NodeStatistics> GetNodeStatisticsAsync(BaseNode node);

        /// <summary>
        /// 验证节点名称
        /// </summary>
        /// <param name="name">名称</param>
        /// <param name="parent">父节点</param>
        /// <returns>验证结果</returns>
        ValidationResult ValidateNodeName(string name, BaseNode? parent);

        /// <summary>
        /// 生成唯一名称
        /// </summary>
        /// <param name="baseName">基础名称</param>
        /// <param name="parent">父节点</param>
        /// <returns>唯一名称</returns>
        string GenerateUniqueName(string baseName, BaseNode? parent);
    }

    /// <summary>
    /// 验证结果
    /// </summary>
    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }

        public static ValidationResult Success() => new ValidationResult { IsValid = true };
        public static ValidationResult Error(string message) => new ValidationResult { IsValid = false, ErrorMessage = message };
    }
}
