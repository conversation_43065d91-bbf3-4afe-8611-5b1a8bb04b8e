using System;
using System.Diagnostics;

namespace 像素喵笔记.Services
{
    /// <summary>
    /// 简化的日志服务，替代Serilog
    /// </summary>
    public static class SimpleLogger
    {
        /// <summary>
        /// 记录信息日志
        /// </summary>
        public static void LogInformation(string message)
        {
            Debug.WriteLine($"[INFO] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {message}");
        }

        /// <summary>
        /// 记录警告日志
        /// </summary>
        public static void LogWarning(string message)
        {
            Debug.WriteLine($"[WARN] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {message}");
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        public static void LogError(string message, Exception? exception = null)
        {
            Debug.WriteLine($"[ERROR] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {message}");
            if (exception != null)
            {
                Debug.WriteLine($"Exception: {exception}");
            }
        }

        /// <summary>
        /// 记录调试日志
        /// </summary>
        public static void LogDebug(string message)
        {
#if DEBUG
            Debug.WriteLine($"[DEBUG] {DateTime.Now:yyyy-MM-dd HH:mm:ss} {message}");
#endif
        }
    }

    /// <summary>
    /// 简化的日志接口，兼容原有代码
    /// </summary>
    public interface ISimpleLogger
    {
        void LogInformation(string message);
        void LogWarning(string message);
        void LogError(string message, Exception? exception = null);
        void LogDebug(string message);
    }

    /// <summary>
    /// 简化的日志实现
    /// </summary>
    public class SimpleLoggerImpl : ISimpleLogger
    {
        public void LogInformation(string message) => SimpleLogger.LogInformation(message);
        public void LogWarning(string message) => SimpleLogger.LogWarning(message);
        public void LogError(string message, Exception? exception = null) => SimpleLogger.LogError(message, exception);
        public void LogDebug(string message) => SimpleLogger.LogDebug(message);
    }
}
