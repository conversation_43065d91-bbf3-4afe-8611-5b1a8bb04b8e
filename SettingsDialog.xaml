<Window x:Class="像素喵笔记.SettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="软件设置" Height="400" Width="500"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <!-- 主容器 - 支持主题切换 -->
    <Border Background="{DynamicResource AppCardBackgroundBrush}"
            CornerRadius="12"
            BorderBrush="{DynamicResource AppBorderBrush}"
            BorderThickness="1"
            Effect="{StaticResource StandardFigmaShadow}">
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 标题栏 - 支持主题切换 -->
            <Border Grid.Row="0"
                    Background="{DynamicResource AppSecondaryBackgroundBrush}"
                    CornerRadius="12,12,0,0"
                    Padding="24,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0"
                              Text="软件设置"
                              FontFamily="Microsoft YaHei"
                              FontSize="18"
                              FontWeight="SemiBold"
                              Foreground="{DynamicResource AppForegroundBrush}"
                              VerticalAlignment="Center"/>
                    
                    <Button Grid.Column="1" 
                           x:Name="CloseButton"
                           Width="32" 
                           Height="32" 
                           Background="Transparent" 
                           BorderThickness="0"
                           Cursor="Hand"
                           Click="CloseButton_Click">
                        <TextBlock Text="✕"
                                  FontSize="14"
                                  Foreground="{DynamicResource AppSecondaryTextBrush}"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"/>
                    </Button>
                </Grid>
            </Border>
            
            <!-- 设置内容 -->
            <ScrollViewer Grid.Row="1" 
                         VerticalScrollBarVisibility="Auto" 
                         Padding="24,20">
                <StackPanel>
                    

                    <!-- 主题设置 -->
                    <StackPanel>
                        <TextBlock Text="界面主题"
                                  FontFamily="Microsoft YaHei"
                                  FontSize="16"
                                  FontWeight="SemiBold"
                                  Foreground="{DynamicResource AppForegroundBrush}"
                                  Margin="0,0,0,12"/>
                        
                        <StackPanel Orientation="Horizontal">
                            <RadioButton x:Name="LightThemeRadio"
                                        Content="浅色主题"
                                        FontFamily="Microsoft YaHei"
                                        FontSize="14"
                                        IsChecked="True"
                                        Margin="0,0,12,0"
                                        Style="{StaticResource FigmaRadioButtonStyle}"/>

                            <RadioButton x:Name="DarkThemeRadio"
                                        Content="深色主题"
                                        FontFamily="Microsoft YaHei"
                                        FontSize="14"
                                        Style="{StaticResource FigmaRadioButtonStyle}"/>
                        </StackPanel>
                        
                        <TextBlock Text="深色主题功能即将推出"
                                  FontFamily="Microsoft YaHei"
                                  FontSize="12"
                                  Foreground="{DynamicResource AppSecondaryTextBrush}"
                                  Margin="0,8,0,0"/>
                    </StackPanel>
                    
                </StackPanel>
            </ScrollViewer>
            
            <!-- 按钮栏 - 支持主题切换 -->
            <Border Grid.Row="2"
                    Background="{DynamicResource AppSecondaryBackgroundBrush}"
                    CornerRadius="0,0,12,12"
                    Padding="24,16">
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Right">
                    
                    <Button x:Name="CancelButton"
                           Content="取消"
                           Style="{StaticResource FigmaDialogCancelButtonStyle}"
                           Click="CancelButton_Click"/>

                    <Button x:Name="OkButton"
                           Content="确定"
                           Style="{StaticResource FigmaDialogConfirmButtonStyle}"
                           Click="OkButton_Click"/>
                </StackPanel>
            </Border>
            
        </Grid>
    </Border>
</Window>
