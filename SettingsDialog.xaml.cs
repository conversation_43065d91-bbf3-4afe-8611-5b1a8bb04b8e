using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;

namespace 像素喵笔记
{
    /// <summary>
    /// SettingsDialog.xaml 的交互逻辑
    /// 软件设置对话框
    /// </summary>
    public partial class SettingsDialog : Window
    {
        #region 公共属性

        /// <summary>
        /// 选择的主题
        /// </summary>
        public string SelectedTheme { get; private set; } = "Light";

        /// <summary>
        /// 设置是否已更改
        /// </summary>
        public bool SettingsChanged { get; private set; } = false;

        #endregion

        #region 构造函数

        public SettingsDialog()
        {
            InitializeComponent();
            LoadCurrentSettings();
        }

        public SettingsDialog(string currentTheme) : this()
        {
            SelectedTheme = currentTheme;
            LoadCurrentSettings();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 加载当前设置
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                // 设置主题选择
                if (SelectedTheme == "Dark")
                {
                    DarkThemeRadio.IsChecked = true;
                }
                else
                {
                    LightThemeRadio.IsChecked = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        private void SaveSettings()
        {
            try
            {
                var oldTheme = SelectedTheme;

                // 获取选择的主题
                SelectedTheme = DarkThemeRadio.IsChecked == true ? "Dark" : "Light";

                // 检查设置是否有变化
                SettingsChanged = oldTheme != SelectedTheme;

                // 如果主题改变了，立即应用新主题
                if (oldTheme != SelectedTheme)
                {
                    var newTheme = SelectedTheme == "Dark" ? AppTheme.Dark : AppTheme.Light;
                    ThemeManager.ApplyTheme(newTheme);
                }

                System.Diagnostics.Debug.WriteLine($"设置已保存: 主题={SelectedTheme}, 已更改={SettingsChanged}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存设置失败: {ex.Message}");
            }
        }

        #endregion

        #region 事件处理

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveSettings();
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"确定按钮处理失败: {ex.Message}");
                MessageBox.Show($"保存设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DialogResult = false;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"取消按钮处理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DialogResult = false;
                Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭按钮处理失败: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 应用程序设置类
    /// </summary>
    public static class AppSettings
    {
        private static readonly string SettingsFilePath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "像素喵笔记", "settings.json");

        /// <summary>
        /// 当前主题
        /// </summary>
        public static string CurrentTheme { get; set; } = "Light";

        /// <summary>
        /// 保存设置到配置文件
        /// </summary>
        public static void SaveToFile()
        {
            try
            {
                var settings = new
                {
                    Theme = CurrentTheme
                };

                var directory = Path.GetDirectoryName(SettingsFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = System.Text.Json.JsonSerializer.Serialize(settings, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = true
                });

                File.WriteAllText(SettingsFilePath, json, System.Text.Encoding.UTF8);
                System.Diagnostics.Debug.WriteLine($"设置已保存到文件: 主题={CurrentTheme}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存设置到文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 从配置文件加载设置
        /// </summary>
        public static void LoadFromFile()
        {
            try
            {
                if (File.Exists(SettingsFilePath))
                {
                    var json = File.ReadAllText(SettingsFilePath, System.Text.Encoding.UTF8);
                    using var document = System.Text.Json.JsonDocument.Parse(json);
                    var root = document.RootElement;

                    if (root.TryGetProperty("Theme", out var themeElement))
                    {
                        CurrentTheme = themeElement.GetString() ?? "Light";
                    }

                    System.Diagnostics.Debug.WriteLine($"设置已从文件加载: 主题={CurrentTheme}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("设置文件不存在，使用默认设置");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从文件加载设置失败: {ex.Message}");
                // 加载失败时使用默认设置
                CurrentTheme = "Light";
            }
        }
    }
}
