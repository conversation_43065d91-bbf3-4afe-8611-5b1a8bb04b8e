<UserControl x:Class="像素喵笔记.ShapesSidebar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:像素喵笔记"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="300">
    
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <Border Background="White"
           CornerRadius="12"
           Padding="20"
           BorderBrush="#E0E0E0"
           BorderThickness="1"
           RenderTransformOrigin="0,0.5">
        <Border.RenderTransform>
            <ScaleTransform ScaleX="1" ScaleY="1"/>
        </Border.RenderTransform>
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
        </Border.Effect>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 标题栏 -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="形状"
                          FontSize="18"
                          FontWeight="SemiBold"
                          Foreground="#333333"
                          HorizontalAlignment="Left"
                          VerticalAlignment="Center"/>
            </Grid>
            
            <!-- 搜索框 -->
            <Border Grid.Row="1" 
                   Background="#F5F5F5" 
                   CornerRadius="8" 
                   Padding="12,8"
                   Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                              Text="🔍" 
                              FontSize="14" 
                              Foreground="#999999"
                              VerticalAlignment="Center"
                              Margin="0,0,8,0"/>
                    
                    <TextBox Grid.Column="1" 
                            x:Name="searchTextBox"
                            Background="Transparent" 
                            BorderThickness="0"
                            FontSize="14"
                            Foreground="#333333"
                            Text="搜索形状"
                            GotFocus="SearchTextBox_GotFocus"
                            LostFocus="SearchTextBox_LostFocus"/>
                </Grid>
            </Border>
            
            <!-- 形状列表 -->
            <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    
                    <!-- 最近使用 分组 -->
                    <Expander x:Name="recentsExpander" IsExpanded="True" Margin="0,0,0,16">
                        <Expander.Header>
                            <TextBlock Text="最近使用" FontSize="14" FontWeight="Medium" Foreground="#333333"/>
                        </Expander.Header>
                        <WrapPanel Margin="0,12,0,0">
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Rectangle">
                                <Rectangle Width="24" Height="16" Fill="Transparent" Stroke="#333333" StrokeThickness="2"/>
                            </Button>
                        </WrapPanel>
                    </Expander>
                    
                    <!-- 连接线 分组 -->
                    <Expander x:Name="connectionsExpander" IsExpanded="True" Margin="0,0,0,16">
                        <Expander.Header>
                            <TextBlock Text="连接线" FontSize="14" FontWeight="Medium" Foreground="#333333"/>
                        </Expander.Header>
                        <WrapPanel Margin="0,12,0,0">
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Arrow">
                                <Path Data="M2,12 L22,12 M15,5 L22,12 L15,19" 
                                     Stroke="#333333" 
                                     StrokeThickness="2" 
                                     Fill="Transparent"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}"
                                   Width="60" Height="60"
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Line">
                                <Line X1="6" Y1="18" X2="18" Y2="6"
                                     Stroke="#333333"
                                     StrokeThickness="2"/>
                            </Button>
                        </WrapPanel>
                    </Expander>
                    
                    <!-- 基础形状 分组 -->
                    <Expander x:Name="basicExpander" IsExpanded="True" Margin="0,0,0,16">
                        <Expander.Header>
                            <TextBlock Text="基础形状" FontSize="14" FontWeight="Medium" Foreground="#333333"/>
                        </Expander.Header>
                        <WrapPanel Margin="0,12,0,0">
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Rectangle">
                                <Rectangle Width="24" Height="16" Fill="Transparent" Stroke="#333333" StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Circle">
                                <Ellipse Width="20" Height="20" Fill="Transparent" Stroke="#333333" StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Diamond">
                                <Path Data="M12,2 L22,12 L12,22 L2,12 Z" 
                                     Fill="Transparent" 
                                     Stroke="#333333" 
                                     StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Triangle">
                                <Path Data="M12,2 L22,20 L2,20 Z" 
                                     Fill="Transparent" 
                                     Stroke="#333333" 
                                     StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="InvertedTriangle">
                                <Path Data="M2,4 L22,4 L12,22 Z" 
                                     Fill="Transparent" 
                                     Stroke="#333333" 
                                     StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Oval">
                                <Ellipse Width="28" Height="16" Fill="Transparent" Stroke="#333333" StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Pentagon">
                                <Path Data="M12,2 L20,8 L16,20 L8,20 L4,8 Z" 
                                     Fill="Transparent" 
                                     Stroke="#333333" 
                                     StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Hexagon">
                                <Path Data="M6,4 L18,4 L22,12 L18,20 L6,20 L2,12 Z" 
                                     Fill="Transparent" 
                                     Stroke="#333333" 
                                     StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Plus">
                                <Path Data="M12,2 L12,22 M2,12 L22,12" 
                                     Stroke="#333333" 
                                     StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="LeftArrow">
                                <Path Data="M19,12 L5,12 M12,19 L5,12 L12,5" 
                                     Stroke="#333333" 
                                     StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}" 
                                   Width="60" Height="60" 
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="RightArrow">
                                <Path Data="M5,12 L19,12 M12,5 L19,12 L12,19" 
                                     Stroke="#333333" 
                                     StrokeThickness="2"/>
                            </Button>
                            <Button Style="{StaticResource FigmaButtonStyle}"
                                   Width="60" Height="60"
                                   Margin="0,0,8,8"
                                   Click="ShapeButton_Click"
                                   Tag="Star">
                                <Path Data="M12,2 L15,8 L22,9 L17,14 L18,21 L12,18 L6,21 L7,14 L2,9 L9,8 Z"
                                     Fill="Transparent"
                                     Stroke="#333333"
                                     StrokeThickness="2"/>
                            </Button>
                        </WrapPanel>
                    </Expander>
                    
                </StackPanel>
            </ScrollViewer>
        </Grid>
    </Border>
</UserControl>
