using System;
using System.Windows;
using System.Windows.Controls;

namespace 像素喵笔记
{
    /// <summary>
    /// ShapesSidebar.xaml 的交互逻辑
    /// </summary>
    public partial class ShapesSidebar : UserControl
    {
        public event EventHandler<string>? ShapeSelected;

        public ShapesSidebar()
        {
            InitializeComponent();
            InitializeSearchBox();
        }

        private void InitializeSearchBox()
        {
            // 设置搜索框的占位符文本样式
            searchTextBox.SetResourceReference(TextBox.ForegroundProperty, "AppSecondaryTextBrush");
        }



        private void ShapeButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string shapeType)
            {
                ShapeSelected?.Invoke(this, shapeType);
            }
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (searchTextBox.Text == "Search shapes")
            {
                searchTextBox.Text = "";
                searchTextBox.SetResourceReference(TextBox.ForegroundProperty, "AppForegroundBrush");
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(searchTextBox.Text))
            {
                searchTextBox.Text = "Search shapes";
                searchTextBox.SetResourceReference(TextBox.ForegroundProperty, "AppSecondaryTextBrush");
            }
        }

        public void FilterShapes(string searchText)
        {
            // TODO: 实现形状搜索过滤功能
            // 根据搜索文本显示/隐藏相应的形状按钮
        }
    }
}
