<Window x:Class="像素喵笔记.SymbolDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="符号" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ResizeMode="NoResize"
        UseLayoutRounding="True"
        SnapsToDevicePixels="True"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        RenderOptions.BitmapScalingMode="HighQuality">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="FigmaStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <!-- 主容器 -->
    <Border Background="#F8F9FA" CornerRadius="12" Margin="15">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="4" Opacity="0.15" BlurRadius="16"/>
        </Border.Effect>

        <Grid Margin="24">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="符号" FontSize="20" FontWeight="SemiBold" Foreground="#202124" VerticalAlignment="Center" FontFamily="Segoe UI"/>
                <Button x:Name="btnClose" Content="✕"
                       HorizontalAlignment="Right" Click="BtnClose_Click"
                       Style="{StaticResource FigmaDialogCloseButtonStyle}"/>
            </Grid>

            <!-- 字体和子集选择 -->
            <Border Grid.Row="1" Style="{StaticResource StandardFigmaCardStyle}" Margin="0,0,0,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="16"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧字体列表 -->
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="字体(F):" Style="{StaticResource FigmaLabelStyle}" Margin="0,0,0,8"/>
                        <ComboBox x:Name="cmbFont" Style="{StaticResource FigmaComboBoxStyle}" SelectionChanged="CmbFont_SelectionChanged"/>
                    </StackPanel>

                    <!-- 右侧子集列表 -->
                    <StackPanel Grid.Column="2">
                        <TextBlock Text="子集(U):" Style="{StaticResource FigmaLabelStyle}" Margin="0,0,0,8"/>
                        <ComboBox x:Name="cmbSubset" Style="{StaticResource FigmaComboBoxStyle}" SelectionChanged="CmbSubset_SelectionChanged"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 符号网格 - 卡片形式 -->
            <Border Grid.Row="2" Style="{StaticResource StandardFigmaCardStyle}" Margin="0,0,0,16">
                <ScrollViewer x:Name="symbolScrollViewer" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                    <WrapPanel x:Name="symbolPanel" Orientation="Horizontal"/>
                </ScrollViewer>
            </Border>

            <!-- 最近使用的符号 - 卡片形式 -->
            <Border Grid.Row="3" Style="{StaticResource StandardFigmaCardStyle}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="近期使用的符号(R):" Style="{StaticResource FigmaLabelStyle}" Margin="0,0,0,8"/>
                    <WrapPanel x:Name="recentSymbolPanel" Orientation="Horizontal" Height="60"/>
                </StackPanel>
            </Border>

            <!-- 底部按钮 -->
            <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,8,0,0">
                <Button x:Name="btnCancel" Content="取消" Style="{StaticResource FigmaDialogCancelButtonStyle}"
                        Click="BtnCancel_Click" IsCancel="True"/>
                <Button x:Name="btnOK" Content="确定" Style="{StaticResource FigmaDialogConfirmButtonStyle}"
                        Click="BtnOK_Click" IsDefault="True"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
