using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Globalization;
using System.Windows.Input;
using System.Threading.Tasks;
using System.Windows.Threading;
using System.ComponentModel;
using System.Threading;
using System.Windows.Media.TextFormatting;

namespace 像素喵笔记
{
    public partial class SymbolDialog : Window
    {
        public string SelectedSymbol { get; private set; } = string.Empty;
        private List<string> recentSymbols = new List<string>();
        private const int MaxRecentSymbols = 15;
        public bool KeepOpen { get; set; } = false;

        // 性能优化相关字段
        private bool _isLoading = false;
        private CancellationTokenSource? _loadingCancellationTokenSource;
        private readonly DispatcherTimer _loadingDelayTimer;
        private const int LoadingDelayMs = 300; // 延迟加载，避免频繁切换时的性能问题
        private const int MaxSymbolsPerBatch = 50; // 每批加载的符号数量
        private readonly Dictionary<string, List<string>> _fontSymbolCache = new(); // 字体符号缓存

        // Unicode子集定义
        private readonly Dictionary<string, (int start, int end)> unicodeSubsets = new Dictionary<string, (int, int)>
        {
            ["全部"] = (0x0000, 0x10FFFF), // 特殊标记，表示显示字体中所有可用字符
            ["基本拉丁语"] = (0x0020, 0x007F),
            ["拉丁语-1补充"] = (0x00A0, 0x00FF),
            ["拉丁语扩展-A"] = (0x0100, 0x017F),
            ["拉丁语扩展-B"] = (0x0180, 0x024F),
            ["IPA扩展"] = (0x0250, 0x02AF),
            ["间距修饰字母"] = (0x02B0, 0x02FF),
            ["组合变音符号"] = (0x0300, 0x036F),
            ["希腊语和科普特语"] = (0x0370, 0x03FF),
            ["西里尔语"] = (0x0400, 0x04FF),
            ["希伯来语"] = (0x0590, 0x05FF),
            ["阿拉伯语"] = (0x0600, 0x06FF),
            ["一般标点符号"] = (0x2000, 0x206F),
            ["上标和下标"] = (0x2070, 0x209F),
            ["货币符号"] = (0x20A0, 0x20CF),
            ["字母式符号"] = (0x2100, 0x214F),
            ["数字形式"] = (0x2150, 0x218F),
            ["箭头"] = (0x2190, 0x21FF),
            ["数学运算符"] = (0x2200, 0x22FF),
            ["杂项技术符号"] = (0x2300, 0x23FF),
            ["控制图片"] = (0x2400, 0x243F),
            ["光学字符识别"] = (0x2440, 0x245F),
            ["封闭式字母数字"] = (0x2460, 0x24FF),
            ["制表符"] = (0x2500, 0x257F),
            ["块元素"] = (0x2580, 0x259F),
            ["几何形状"] = (0x25A0, 0x25FF),
            ["杂项符号"] = (0x2600, 0x26FF),
            ["装饰符号"] = (0x2700, 0x27BF),
            ["杂项符号和箭头"] = (0x2B00, 0x2BFF),
            ["CJK符号和标点"] = (0x3000, 0x303F),
            ["平假名"] = (0x3040, 0x309F),
            ["片假名"] = (0x30A0, 0x30FF),
            ["CJK统一汉字"] = (0x4E00, 0x9FFF),
            ["私用区域A"] = (0xE000, 0xF8FF), // Private Use Area - 包含Segoe MDL2 Assets等字体符号
            ["私用区域B"] = (0xF0000, 0xFFFFD),
            ["私用区域C"] = (0x100000, 0x10FFFD)
        };

        // 字体符号映射 - 这些是预定义的常用符号，"全部"选项会直接从字体获取
        private readonly Dictionary<string, string[]> fontSymbols = new Dictionary<string, string[]>
        {
            ["Segoe MDL2 Assets"] = new string[] {
                "\uE700", "\uE701", "\uE702", "\uE703", "\uE704", "\uE705", "\uE706", "\uE707", "\uE708", "\uE709",
                "\uE70A", "\uE70B", "\uE70C", "\uE70D", "\uE70E", "\uE70F", "\uE710", "\uE711", "\uE712", "\uE713",
                "\uE714", "\uE715", "\uE716", "\uE717", "\uE718", "\uE719", "\uE71A", "\uE71B", "\uE71C", "\uE71D",
                "\uE71E", "\uE71F", "\uE720", "\uE721", "\uE722", "\uE723", "\uE724", "\uE725", "\uE726", "\uE727",
                "\uE728", "\uE729", "\uE72A", "\uE72B", "\uE72C", "\uE72D", "\uE72E", "\uE730", "\uE731", "\uE734"
            },
            ["Wingdings"] = new string[] {
                "✌", "✍", "✎", "✏", "✐", "✑", "✒", "✓", "✔", "✕",
                "✖", "✗", "✘", "✙", "✚", "✛", "✜", "✝", "✞", "✟",
                "✠", "✡", "✢", "✣", "✤", "✥", "✦", "✧", "✨", "✩"
            },
            ["Webdings"] = new string[] {
                "♠", "♣", "♥", "♦", "♪", "♫", "♬", "♭", "♮", "♯",
                "☀", "☁", "☂", "☃", "☄", "★", "☆", "☇", "☈", "☉",
                "☊", "☋", "☌", "☍", "☎", "☏", "☐", "☑", "☒", "☓"
            },
            ["Symbol"] = new string[] {
                "Α", "Β", "Γ", "Δ", "Ε", "Ζ", "Η", "Θ", "Ι", "Κ",
                "Λ", "Μ", "Ν", "Ξ", "Ο", "Π", "Ρ", "Σ", "Τ", "Υ",
                "Φ", "Χ", "Ψ", "Ω", "α", "β", "γ", "δ", "ε", "ζ"
            },
            ["MS Gothic"] = new string[] {
                "あ", "い", "う", "え", "お", "か", "き", "く", "け", "こ",
                "さ", "し", "す", "せ", "そ", "た", "ち", "つ", "て", "と",
                "な", "に", "ぬ", "ね", "の", "は", "ひ", "ふ", "へ", "ほ"
            },
            ["Arial Unicode MS"] = new string[] {
                "☀", "☁", "☂", "☃", "☄", "★", "☆", "☇", "☈", "☉",
                "☊", "☋", "☌", "☍", "☎", "☏", "☐", "☑", "☒", "☓",
                "☔", "☕", "☖", "☗", "☘", "☙", "☚", "☛", "☜", "☝"
            }
        };

        // 当前选中的字体
        private string currentFont = string.Empty;

        // 添加事件处理委托
        public delegate void SymbolSelectedHandler(string symbol);
        public event SymbolSelectedHandler? OnSymbolSelected;

        // 存储按钮和符号的映射，用于事件清理
        private readonly Dictionary<Button, string> _buttonSymbolMap = new Dictionary<Button, string>();

        public SymbolDialog()
        {
            InitializeComponent();

            // 初始化延迟加载定时器
            _loadingDelayTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(LoadingDelayMs)
            };
            _loadingDelayTimer.Tick += LoadingDelayTimer_Tick;

            InitializeDialog();
        }

        private void InitializeDialog()
        {
            // 加载系统字体
            LoadSystemFonts();

            // 初始化子集列表
            LoadSubsets();

            // 加载最近使用的符号
            LoadRecentSymbols();
        }

        private void LoadSystemFonts()
        {
            // 清空字体列表
            cmbFont.Items.Clear();

            // 获取系统所有字体
            var systemFonts = Fonts.SystemFontFamilies
                .OrderBy(f => f.Source)
                .Select(f => f.Source)
                .ToList();

            // 添加到下拉列表
            foreach (var font in systemFonts)
            {
                cmbFont.Items.Add(font);
            }

            // 选择第一个字体
            if (cmbFont.Items.Count > 0)
            {
                cmbFont.SelectedIndex = 0;
            }
        }

        private void LoadSubsets()
        {
            // 清空子集列表
            cmbSubset.Items.Clear();

            // 添加所有子集
            foreach (var subset in unicodeSubsets.Keys)
            {
                cmbSubset.Items.Add(subset);
            }

            // 默认选择第一个子集
            if (cmbSubset.Items.Count > 0)
            {
                cmbSubset.SelectedIndex = 0;
            }
        }

        private void LoadingDelayTimer_Tick(object? sender, EventArgs e)
        {
            _loadingDelayTimer.Stop();
            _ = LoadSymbolsAsync();
        }

        private void LoadSymbols()
        {
            // 取消之前的加载操作
            _loadingCancellationTokenSource?.Cancel();
            _loadingDelayTimer.Stop();

            // 启动延迟加载定时器
            _loadingDelayTimer.Start();
        }

        private async Task LoadSymbolsAsync()
        {
            if (_isLoading) return;

            _isLoading = true;
            symbolPanel.Children.Clear();

            // 显示加载指示器
            ShowLoadingIndicator();

            try
            {
                _loadingCancellationTokenSource?.Cancel();
                _loadingCancellationTokenSource = new CancellationTokenSource();
                var cancellationToken = _loadingCancellationTokenSource.Token;

                if (cmbFont.SelectedItem == null || cmbSubset.SelectedItem == null) return;

                string selectedFont = cmbFont.SelectedItem.ToString() ?? "Segoe UI";
                string selectedSubset = cmbSubset.SelectedItem.ToString() ?? "";

                if (!string.IsNullOrEmpty(selectedSubset) && unicodeSubsets.ContainsKey(selectedSubset))
                {
                    if (selectedSubset == "全部")
                    {
                        await LoadAllAvailableSymbolsAsync(selectedFont, cancellationToken);
                    }
                    else
                    {
                        var (start, end) = unicodeSubsets[selectedSubset];
                        await LoadSymbolsFromUnicodeRangeAsync(selectedFont, start, end, cancellationToken);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 操作被取消，这是正常的
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载符号时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                HideLoadingIndicator();
                _isLoading = false;
            }
        }

        private void ShowLoadingIndicator()
        {
            // 在符号面板中显示加载指示器
            var loadingText = new TextBlock
            {
                Text = "正在加载符号...",
                FontSize = 14,
                Foreground = new SolidColorBrush(Color.FromRgb(95, 99, 104)),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20)
            };
            symbolPanel.Children.Add(loadingText);
        }

        private void HideLoadingIndicator()
        {
            // 移除加载指示器（如果存在）
            var loadingIndicator = symbolPanel.Children.OfType<TextBlock>().FirstOrDefault();
            if (loadingIndicator != null)
            {
                symbolPanel.Children.Remove(loadingIndicator);
            }
        }

        private async Task LoadAllAvailableSymbolsAsync(string fontFamily, CancellationToken cancellationToken)
        {
            // 检查缓存
            string cacheKey = $"{fontFamily}_all";
            if (_fontSymbolCache.TryGetValue(cacheKey, out var cachedSymbols))
            {
                await LoadSymbolsFromCacheAsync(cachedSymbols, fontFamily, cancellationToken);
                return;
            }

            var symbols = new List<string>();

            await Task.Run(() =>
            {
                try
                {
                    var font = new FontFamily(fontFamily);
                    var typeface = new Typeface(font, FontStyles.Normal, FontWeights.Normal, FontStretches.Normal);

                    if (typeface.TryGetGlyphTypeface(out GlyphTypeface glyphTypeface))
                    {
                        var characterMap = glyphTypeface.CharacterToGlyphMap;
                        var sortedCharacters = characterMap.Keys.OrderBy(c => (int)c).ToList();

                        int maxSymbols = 500; // 减少最大符号数量以提高性能
                        int count = 0;

                        foreach (char character in sortedCharacters)
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            if (count >= maxSymbols) break;

                            if (char.IsControl(character) || (char.IsWhiteSpace(character) && character != ' '))
                                continue;

                            symbols.Add(character.ToString());
                            count++;
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception)
                {
                    // 如果出错，使用预定义符号
                    if (fontSymbols.ContainsKey(fontFamily))
                    {
                        symbols.AddRange(fontSymbols[fontFamily]);
                    }
                }
            }, cancellationToken);

            // 缓存结果
            _fontSymbolCache[cacheKey] = symbols;

            // 加载到UI
            await LoadSymbolsFromCacheAsync(symbols, fontFamily, cancellationToken);
        }

        private async Task LoadSymbolsFromCacheAsync(List<string> symbols, string fontFamily, CancellationToken cancellationToken)
        {
            // 分批加载符号以避免UI冻结
            for (int i = 0; i < symbols.Count; i += MaxSymbolsPerBatch)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var batch = symbols.Skip(i).Take(MaxSymbolsPerBatch);

                await Dispatcher.InvokeAsync(() =>
                {
                    foreach (var symbol in batch)
                    {
                        var button = CreateSymbolButton(symbol, fontFamily);
                        symbolPanel.Children.Add(button);
                    }
                }, DispatcherPriority.Background, cancellationToken);

                // 给UI一些时间来响应
                await Task.Delay(10, cancellationToken);
            }
        }

        private async Task LoadSymbolsFromUnicodeRangeAsync(string fontFamily, int startCode, int endCode, CancellationToken cancellationToken)
        {
            // 检查缓存
            string cacheKey = $"{fontFamily}_{startCode}_{endCode}";
            if (_fontSymbolCache.TryGetValue(cacheKey, out var cachedSymbols))
            {
                await LoadSymbolsFromCacheAsync(cachedSymbols, fontFamily, cancellationToken);
                return;
            }

            var symbols = new List<string>();

            await Task.Run(() =>
            {
                try
                {
                    var font = new FontFamily(fontFamily);

                    for (int code = startCode; code <= endCode; code++)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        if (IsFontSupportsCharacter(font, code))
                        {
                            string symbol = char.ConvertFromUtf32(code);
                            symbols.Add(symbol);
                        }
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
                catch (Exception)
                {
                    // 忽略错误，继续处理
                }
            }, cancellationToken);

            // 缓存结果
            _fontSymbolCache[cacheKey] = symbols;

            // 加载到UI
            await LoadSymbolsFromCacheAsync(symbols, fontFamily, cancellationToken);
        }

        // 删除旧的同步方法，已被异步版本替代

        private bool IsFontSupportsCharacter(FontFamily font, int unicodeValue)
        {
            // 这个方法检查字体是否支持特定的Unicode字符
            try
            {
                Typeface typeface = new Typeface(font, FontStyles.Normal, FontWeights.Normal, FontStretches.Normal);

                if (typeface.TryGetGlyphTypeface(out GlyphTypeface glyphTypeface))
                {
                    // 处理代理对（高Unicode值）
                    if (unicodeValue > 0xFFFF)
                    {
                        string surrogatePair = char.ConvertFromUtf32(unicodeValue);
                        if (surrogatePair.Length == 2)
                        {
                            return glyphTypeface.CharacterToGlyphMap.ContainsKey(surrogatePair[0]) ||
                                   glyphTypeface.CharacterToGlyphMap.ContainsKey(surrogatePair[1]);
                        }
                    }
                    else
                    {
                        char c = (char)unicodeValue;
                        return glyphTypeface.CharacterToGlyphMap.ContainsKey(c);
                    }
                }
            }
            catch
            {
                // 如果有任何异常，假设字体不支持该字符
            }

            return false;
        }

        private void LoadRecentSymbols()
        {
            recentSymbolPanel.Children.Clear();

            // 如果没有最近使用的符号，显示一些默认符号
            if (recentSymbols.Count == 0)
            {
                var defaultRecent = new string[] { "▼", "☆", "★", "∞", "≡", "⌘", "⚙", "≡", "⋯", "⋮", "⋰", "⋱", "—", "→", "←", "↑", "↓" };
                for (int i = 0; i < Math.Min(defaultRecent.Length, MaxRecentSymbols); i++)
                {
                    var button = CreateSymbolButton(defaultRecent[i]);
                    recentSymbolPanel.Children.Add(button);
                }
            }
            else
            {
                foreach (var symbol in recentSymbols)
                {
                    var button = CreateSymbolButton(symbol);
                    recentSymbolPanel.Children.Add(button);
                }
            }
        }

        private Button CreateSymbolButton(string symbol, string fontFamily = "Segoe UI")
        {
            var button = new Button
            {
                Content = symbol,
                Style = (Style)FindResource("SymbolButtonStyle"),
                FontFamily = new FontFamily(fontFamily),
                FontSize = 16, // 确保符号足够大以便显示
                Width = 32,
                Height = 32,
                Margin = new Thickness(2),
                // 确保字体渲染正确
                UseLayoutRounding = true,
                SnapsToDevicePixels = true
            };

            // 设置文本渲染选项
            TextOptions.SetTextFormattingMode(button, TextFormattingMode.Display);
            TextOptions.SetTextRenderingMode(button, TextRenderingMode.ClearType);

            // 对于特殊符号，确保使用正确的字体
            if (cmbFont.SelectedItem != null && symbol.Length > 0)
            {
                button.FontFamily = new FontFamily(cmbFont.SelectedItem.ToString());
            }
            else if (symbol.Length > 0)
            {
                // 为特定Unicode范围的字符自动选择合适的字体
                int charCode = char.ConvertToUtf32(symbol, 0);
                string bestFont = GetBestFontForCharacter(charCode);
                if (!string.IsNullOrEmpty(bestFont))
                {
                    button.FontFamily = new FontFamily(bestFont);
                }
            }

            // 设置工具提示显示Unicode值
            try
            {
                if (symbol.Length > 0)
                {
                    int unicodeValue = char.ConvertToUtf32(symbol, 0);
                    button.ToolTip = $"U+{unicodeValue:X4}: {symbol}";
                }
            }
            catch
            {
                button.ToolTip = symbol;
            }

            // 存储按钮和符号的映射
            _buttonSymbolMap[button] = symbol;

            // 使用具名方法绑定事件
            button.Click += OnSymbolButtonClick;
            button.MouseDoubleClick += OnSymbolButtonDoubleClick;

            return button;
        }

        /// <summary>
        /// 符号按钮点击事件处理器
        /// </summary>
        private void OnSymbolButtonClick(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && _buttonSymbolMap.TryGetValue(button, out string? symbol))
            {
                SelectedSymbol = symbol;
                AddToRecentSymbols(symbol);

                // 如果设置了保持窗口打开，则不关闭窗口
                if (!KeepOpen)
                {
                    DialogResult = true;
                    Close();
                }
                else
                {
                    // 触发符号选择事件（累积插入模式）
                    OnSymbolSelected?.Invoke(symbol);
                }
            }
        }

        /// <summary>
        /// 符号按钮双击事件处理器
        /// </summary>
        private async void OnSymbolButtonDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (sender is Button button && _buttonSymbolMap.TryGetValue(button, out string? symbol))
            {
                // 防止双击时的卡顿
                await Task.Run(() =>
                {
                    Dispatcher.Invoke(() =>
                    {
                        AddToRecentSymbols(symbol);
                        // 触发符号选择事件（累积插入，不替换）
                        OnSymbolSelected?.Invoke(symbol);
                        // 设置保持窗口打开
                        KeepOpen = true;
                    });
                });

                // 双击时保持窗口打开
                e.Handled = true;
            }
        }

        private void AddToRecentSymbols(string symbol)
        {
            // 如果符号已存在，先移除
            if (recentSymbols.Contains(symbol))
            {
                recentSymbols.Remove(symbol);
            }

            // 在开头插入新符号
            recentSymbols.Insert(0, symbol);

            // 保持最多15个符号
            if (recentSymbols.Count > MaxRecentSymbols)
            {
                recentSymbols.RemoveAt(MaxRecentSymbols);
            }

            // 异步刷新显示以避免阻塞
            _ = Task.Run(() =>
            {
                Dispatcher.BeginInvoke(() => LoadRecentSymbols(), DispatcherPriority.Background);
            });
        }

        private void CmbFont_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (cmbFont.SelectedItem != null && !_isLoading)
            {
                currentFont = cmbFont.SelectedItem.ToString() ?? "Segoe UI";
                LoadSymbols();
            }
        }

        private void CmbSubset_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (!_isLoading)
            {
                LoadSymbols();
            }
        }

        private void BtnOK_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(SelectedSymbol))
            {
                DialogResult = true;
                Close();
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            // 清理资源
            _loadingCancellationTokenSource?.Cancel();
            _loadingCancellationTokenSource?.Dispose();
            _loadingDelayTimer?.Stop();

            // 清理按钮事件绑定
            foreach (var button in _buttonSymbolMap.Keys)
            {
                button.Click -= OnSymbolButtonClick;
                button.MouseDoubleClick -= OnSymbolButtonDoubleClick;
            }
            _buttonSymbolMap.Clear();

            // 清理符号面板
            symbolPanel.Children.Clear();
            recentSymbolPanel.Children.Clear();

            // 清理字体符号缓存
            _fontSymbolCache.Clear();

            base.OnClosed(e);
        }

        private string GetBestFontForCharacter(int charCode)
        {
            // 为不同的Unicode范围指定最佳字体
            if (charCode >= 0x4E00 && charCode <= 0x9FFF) // 中文汉字
                return "Microsoft YaHei UI";
            else if (charCode >= 0x0370 && charCode <= 0x03FF) // 希腊文
                return "Segoe UI";
            else if (charCode >= 0x0400 && charCode <= 0x04FF) // 西里尔文
                return "Segoe UI";
            else if (charCode >= 0x0600 && charCode <= 0x06FF) // 阿拉伯文
                return "Arial";
            else if (charCode >= 0x0900 && charCode <= 0x097F) // 梵文
                return "Nirmala UI";
            else if (charCode >= 0x1100 && charCode <= 0x11FF) // 韩文
                return "Malgun Gothic";
            else if (charCode >= 0x0E00 && charCode <= 0x0E7F) // 泰文
                return "Leelawadee UI";
            else if (charCode >= 0x3040 && charCode <= 0x309F) // 日文平假名
                return "Yu Gothic UI";
            else if (charCode >= 0x30A0 && charCode <= 0x30FF) // 日文片假名
                return "Yu Gothic UI";
            else if (charCode >= 0x2600 && charCode <= 0x26FF) // 杂项符号
                return "Segoe UI Symbol";
            else if (charCode >= 0x2700 && charCode <= 0x27BF) // 装饰符号
                return "Segoe UI Symbol";
            else if (charCode >= 0x1F300 && charCode <= 0x1F6FF) // Emoji表情符号
                return "Segoe UI Emoji";
            else if (charCode >= 0x2190 && charCode <= 0x21FF) // 箭头
                return "Segoe UI Symbol";
            else if (charCode >= 0x2200 && charCode <= 0x22FF) // 数学运算符
                return "Cambria Math";
            else
                return "Segoe UI";
        }
    }
}

