using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Threading;

namespace 像素喵笔记
{
    /// <summary>
    /// 高性能语法高亮管理器
    /// 采用可视区域高亮和增量更新策略
    /// </summary>
    public class SyntaxHighlighter
    {
        private readonly RichTextBox _richTextBox;
        private readonly DispatcherTimer _highlightTimer;
        private readonly Dictionary<int, HighlightCache> _lineCache;
        private string _currentLanguage = "Python";
        private bool _isHighlighting = false;
        private int _lastVisibleStartLine = -1;
        private int _lastVisibleEndLine = -1;

        // 语法规则定义
        private readonly Dictionary<string, LanguageRules> _languageRules;

        public SyntaxHighlighter(RichTextBox richTextBox)
        {
            _richTextBox = richTextBox;
            _lineCache = new Dictionary<int, HighlightCache>();
            
            // 初始化计时器
            _highlightTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(300) // 300ms延迟
            };
            _highlightTimer.Tick += OnHighlightTimerTick!;

            // 初始化语法规则
            _languageRules = InitializeLanguageRules();

            // 监听滚动事件（延迟设置，等待控件加载完成）
            _richTextBox.Loaded += (s, e) => SetupScrollListener();
        }

        /// <summary>
        /// 设置当前语言
        /// </summary>
        public void SetLanguage(string language)
        {
            if (_currentLanguage != language)
            {
                _currentLanguage = language;
                ClearCache();
                RequestHighlight();
            }
        }

        /// <summary>
        /// 请求语法高亮
        /// </summary>
        public void RequestHighlight()
        {
            if (_isHighlighting) return;

            _highlightTimer.Stop();
            _highlightTimer.Start();
        }

        /// <summary>
        /// 文本变化时调用
        /// </summary>
        public void OnTextChanged(int startLine, int endLine)
        {
            // 清除受影响行的缓存
            for (int line = startLine; line <= endLine; line++)
            {
                _lineCache.Remove(line);
            }

            RequestHighlight();
        }

        /// <summary>
        /// 清除所有缓存
        /// </summary>
        public void ClearCache()
        {
            _lineCache.Clear();
            _lastVisibleStartLine = -1;
            _lastVisibleEndLine = -1;
        }

        /// <summary>
        /// 设置滚动监听器
        /// </summary>
        private void SetupScrollListener()
        {
            var scrollViewer = GetScrollViewer(_richTextBox);
            if (scrollViewer != null)
            {
                scrollViewer.ScrollChanged += OnScrollChanged;
            }
        }

        /// <summary>
        /// 滚动事件处理
        /// </summary>
        private void OnScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            if (e.VerticalChange != 0)
            {
                RequestHighlight();
            }
        }

        /// <summary>
        /// 高亮计时器触发
        /// </summary>
        private void OnHighlightTimerTick(object sender, EventArgs e)
        {
            _highlightTimer.Stop();
            
            // 在后台线程执行高亮
            Dispatcher.CurrentDispatcher.BeginInvoke(new Action(() =>
            {
                HighlightVisibleArea();
            }), DispatcherPriority.Background);
        }

        /// <summary>
        /// 高亮可视区域
        /// </summary>
        private void HighlightVisibleArea()
        {
            if (_isHighlighting || _richTextBox.Document == null) return;

            try
            {
                _isHighlighting = true;

                // 获取可视区域的行范围
                var (startLine, endLine) = GetVisibleLineRange();
                
                // 如果可视区域没有变化且已经高亮过，则跳过
                if (startLine == _lastVisibleStartLine && endLine == _lastVisibleEndLine)
                {
                    return;
                }

                // 扩展高亮范围（前后各加10行，提供缓冲）
                int bufferLines = 10;
                int totalLines = GetTotalLines();
                startLine = Math.Max(0, startLine - bufferLines);
                endLine = Math.Min(totalLines - 1, endLine + bufferLines);

                // 高亮指定范围
                HighlightLineRange(startLine, endLine);

                _lastVisibleStartLine = startLine;
                _lastVisibleEndLine = endLine;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"语法高亮错误: {ex.Message}");
            }
            finally
            {
                _isHighlighting = false;
            }
        }

        /// <summary>
        /// 获取可视区域的行范围
        /// </summary>
        private (int startLine, int endLine) GetVisibleLineRange()
        {
            try
            {
                var scrollViewer = GetScrollViewer(_richTextBox);
                if (scrollViewer == null) return (0, 0);

                double viewportHeight = scrollViewer.ViewportHeight;
                double verticalOffset = scrollViewer.VerticalOffset;
                double lineHeight = GetEstimatedLineHeight();

                int startLine = Math.Max(0, (int)(verticalOffset / lineHeight));
                int visibleLines = (int)(viewportHeight / lineHeight) + 1;
                int endLine = Math.Min(GetTotalLines() - 1, startLine + visibleLines);

                return (startLine, endLine);
            }
            catch
            {
                return (0, Math.Min(50, GetTotalLines() - 1)); // 默认前50行
            }
        }

        /// <summary>
        /// 获取总行数
        /// </summary>
        private int GetTotalLines()
        {
            try
            {
                var textRange = new TextRange(_richTextBox.Document.ContentStart, _richTextBox.Document.ContentEnd);
                string text = textRange.Text;
                return string.IsNullOrEmpty(text) ? 1 : text.Split('\n').Length;
            }
            catch
            {
                return 1;
            }
        }

        /// <summary>
        /// 估算行高
        /// </summary>
        private double GetEstimatedLineHeight()
        {
            try
            {
                return _richTextBox.FontSize * 1.3; // 估算值
            }
            catch
            {
                return 16.0; // 默认值
            }
        }

        /// <summary>
        /// 获取ScrollViewer
        /// </summary>
        private ScrollViewer? GetScrollViewer(DependencyObject element)
        {
            if (element is ScrollViewer scrollViewer)
                return scrollViewer;

            for (int i = 0; i < System.Windows.Media.VisualTreeHelper.GetChildrenCount(element); i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(element, i);
                var result = GetScrollViewer(child);
                if (result != null)
                    return result;
            }

            return null;
        }

        /// <summary>
        /// 高亮指定行范围
        /// </summary>
        private void HighlightLineRange(int startLine, int endLine)
        {
            try
            {
                var document = _richTextBox.Document;
                var textRange = new TextRange(document.ContentStart, document.ContentEnd);
                string fullText = textRange.Text;
                string[] lines = fullText.Split('\n');

                if (!_languageRules.ContainsKey(_currentLanguage)) return;
                var rules = _languageRules[_currentLanguage];

                // 暂时禁用文本变化事件
                _richTextBox.TextChanged -= OnRichTextBoxTextChanged;

                for (int lineIndex = startLine; lineIndex <= endLine && lineIndex < lines.Length; lineIndex++)
                {
                    // 检查缓存
                    if (_lineCache.ContainsKey(lineIndex) && 
                        _lineCache[lineIndex].Text == lines[lineIndex])
                    {
                        continue; // 该行已经高亮且内容未变化
                    }

                    // 高亮单行
                    HighlightSingleLine(document, lines, lineIndex, rules);

                    // 更新缓存
                    _lineCache[lineIndex] = new HighlightCache
                    {
                        Text = lines[lineIndex],
                        LastHighlighted = DateTime.Now
                    };
                }

                // 重新启用文本变化事件
                _richTextBox.TextChanged += OnRichTextBoxTextChanged;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮行范围错误: {ex.Message}");
                _richTextBox.TextChanged += OnRichTextBoxTextChanged;
            }
        }

        /// <summary>
        /// 高亮单行
        /// </summary>
        private void HighlightSingleLine(FlowDocument document, string[] lines, int lineIndex, LanguageRules rules)
        {
            try
            {
                string line = lines[lineIndex];
                if (string.IsNullOrEmpty(line)) return;

                // 计算行的起始位置
                int charOffset = 0;
                for (int i = 0; i < lineIndex; i++)
                {
                    charOffset += lines[i].Length + 1; // +1 for newline
                }

                // 应用语法高亮规则
                ApplyRulesToLine(document, line, charOffset, rules);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮单行错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用语法规则到单行
        /// </summary>
        private void ApplyRulesToLine(FlowDocument document, string line, int lineStartOffset, LanguageRules rules)
        {
            // 先清除该行的格式
            ClearLineFormatting(document, lineStartOffset, line.Length);

            // 注释优先处理（因为注释内的内容不应该被其他规则处理）
            var commentRanges = GetCommentRanges(line, rules.CommentStart, rules.CommentEnd);
            foreach (var range in commentRanges)
            {
                ApplyFormatToRange(document, lineStartOffset + range.Start, range.Length, rules.CommentBrush, FontWeights.Normal);
            }

            // 应用字符串高亮（避开注释区域）
            var stringRanges = GetStringRanges(line, commentRanges);
            foreach (var range in stringRanges)
            {
                ApplyFormatToRange(document, lineStartOffset + range.Start, range.Length, rules.StringBrush, FontWeights.Normal);
            }

            // 应用关键字高亮（避开注释和字符串区域）
            var excludeRanges = commentRanges.Concat(stringRanges).ToList();
            foreach (var keyword in rules.Keywords)
            {
                var keywordRanges = GetKeywordRanges(line, keyword, excludeRanges);
                foreach (var range in keywordRanges)
                {
                    ApplyFormatToRange(document, lineStartOffset + range.Start, range.Length, rules.KeywordBrush, FontWeights.Bold);
                }
            }
        }

        /// <summary>
        /// 应用格式到指定范围
        /// </summary>
        private void ApplyFormatToRange(FlowDocument document, int absoluteStart, int length, Brush brush, FontWeight weight)
        {
            try
            {
                var start = document.ContentStart.GetPositionAtOffset(absoluteStart);
                var end = document.ContentStart.GetPositionAtOffset(absoluteStart + length);

                if (start != null && end != null)
                {
                    var range = new TextRange(start, end);
                    range.ApplyPropertyValue(TextElement.ForegroundProperty, brush);
                    range.ApplyPropertyValue(TextElement.FontWeightProperty, weight);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用格式错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取注释范围
        /// </summary>
        private List<HighlightRange> GetCommentRanges(string line, string commentStart, string? commentEnd)
        {
            var ranges = new List<HighlightRange>();

            if (string.IsNullOrEmpty(commentStart)) return ranges;

            try
            {
                if (string.IsNullOrEmpty(commentEnd))
                {
                    // 单行注释（如 # 或 //）
                    int index = line.IndexOf(commentStart);
                    if (index != -1)
                    {
                        ranges.Add(new HighlightRange { Start = index, Length = line.Length - index });
                    }
                }
                else
                {
                    // 块注释（如 <!-- -->）
                    int startIndex = 0;
                    while ((startIndex = line.IndexOf(commentStart, startIndex)) != -1)
                    {
                        int endIndex = line.IndexOf(commentEnd, startIndex + commentStart.Length);
                        if (endIndex != -1)
                        {
                            int length = endIndex + commentEnd.Length - startIndex;
                            ranges.Add(new HighlightRange { Start = startIndex, Length = length });
                            startIndex = endIndex + commentEnd.Length;
                        }
                        else
                        {
                            // 注释未结束，高亮到行尾
                            ranges.Add(new HighlightRange { Start = startIndex, Length = line.Length - startIndex });
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取注释范围错误: {ex.Message}");
            }

            return ranges;
        }

        /// <summary>
        /// 获取字符串范围
        /// </summary>
        private List<HighlightRange> GetStringRanges(string line, List<HighlightRange> excludeRanges)
        {
            var ranges = new List<HighlightRange>();

            try
            {
                // 处理双引号字符串
                ranges.AddRange(GetQuoteStringRanges(line, '"', excludeRanges));
                // 处理单引号字符串
                ranges.AddRange(GetQuoteStringRanges(line, '\'', excludeRanges));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取字符串范围错误: {ex.Message}");
            }

            return ranges;
        }

        /// <summary>
        /// 获取特定引号的字符串范围
        /// </summary>
        private List<HighlightRange> GetQuoteStringRanges(string line, char quote, List<HighlightRange> excludeRanges)
        {
            var ranges = new List<HighlightRange>();
            int index = 0;

            while ((index = line.IndexOf(quote, index)) != -1)
            {
                // 检查是否在排除范围内
                if (IsInExcludeRanges(index, excludeRanges))
                {
                    index++;
                    continue;
                }

                int endIndex = line.IndexOf(quote, index + 1);
                if (endIndex != -1)
                {
                    int length = endIndex - index + 1;
                    ranges.Add(new HighlightRange { Start = index, Length = length });
                    index = endIndex + 1;
                }
                else
                {
                    break;
                }
            }

            return ranges;
        }

        /// <summary>
        /// 获取关键字范围
        /// </summary>
        private List<HighlightRange> GetKeywordRanges(string line, string keyword, List<HighlightRange> excludeRanges)
        {
            var ranges = new List<HighlightRange>();
            int index = 0;

            while ((index = line.IndexOf(keyword, index, StringComparison.OrdinalIgnoreCase)) != -1)
            {
                // 检查是否在排除范围内
                if (IsInExcludeRanges(index, excludeRanges))
                {
                    index += keyword.Length;
                    continue;
                }

                // 检查是否是完整单词
                bool isWholeWord = (index == 0 || !char.IsLetterOrDigit(line[index - 1])) &&
                                  (index + keyword.Length >= line.Length || !char.IsLetterOrDigit(line[index + keyword.Length]));

                if (isWholeWord)
                {
                    ranges.Add(new HighlightRange { Start = index, Length = keyword.Length });
                }

                index += keyword.Length;
            }

            return ranges;
        }

        /// <summary>
        /// 检查位置是否在排除范围内
        /// </summary>
        private bool IsInExcludeRanges(int position, List<HighlightRange> excludeRanges)
        {
            foreach (var range in excludeRanges)
            {
                if (position >= range.Start && position < range.Start + range.Length)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 清除行格式
        /// </summary>
        private void ClearLineFormatting(FlowDocument document, int startOffset, int length)
        {
            try
            {
                var start = document.ContentStart.GetPositionAtOffset(startOffset);
                var end = document.ContentStart.GetPositionAtOffset(startOffset + length);

                if (start != null && end != null)
                {
                    var range = new TextRange(start, end);
                    range.ApplyPropertyValue(TextElement.ForegroundProperty, Brushes.Black);
                    range.ApplyPropertyValue(TextElement.FontWeightProperty, FontWeights.Normal);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除行格式错误: {ex.Message}");
            }
        }







        /// <summary>
        /// 初始化语言规则
        /// </summary>
        private Dictionary<string, LanguageRules> InitializeLanguageRules()
        {
            return new Dictionary<string, LanguageRules>
            {
                ["Python"] = new LanguageRules
                {
                    Keywords = new[] { "def", "class", "if", "else", "elif", "for", "while", "try", "except",
                                     "finally", "import", "from", "as", "return", "yield", "break", "continue",
                                     "pass", "and", "or", "not", "in", "is", "True", "False", "None", "lambda",
                                     "with", "global", "nonlocal", "print" },
                    KeywordBrush = GetThemeBrush("KeywordBrush", Brushes.Blue),
                    StringBrush = GetThemeBrush("StringBrush", Brushes.Brown),
                    CommentBrush = GetThemeBrush("CommentBrush", Brushes.Green),
                    CommentStart = "#",
                    CommentEnd = null // Python使用单行注释
                },
                ["HTML"] = new LanguageRules
                {
                    Keywords = new[] { "html", "head", "body", "div", "span", "p", "h1", "h2", "h3", "h4", "h5", "h6",
                                     "a", "img", "ul", "ol", "li", "table", "tr", "td", "th", "form", "input",
                                     "button", "script", "style", "link", "meta", "title" },
                    KeywordBrush = GetThemeBrush("KeywordBrush", Brushes.Blue),
                    StringBrush = GetThemeBrush("StringBrush", Brushes.Brown),
                    CommentBrush = GetThemeBrush("CommentBrush", Brushes.Green),
                    CommentStart = "<!--",
                    CommentEnd = "-->" // HTML使用块注释
                }
            };
        }

        /// <summary>
        /// RichTextBox文本变化事件处理
        /// </summary>
        private void OnRichTextBoxTextChanged(object sender, TextChangedEventArgs e)
        {
            // 这个方法将在MainWindow中设置
        }

        /// <summary>
        /// 获取主题画刷
        /// </summary>
        private static Brush GetThemeBrush(string resourceKey, Brush fallback)
        {
            try
            {
                var app = System.Windows.Application.Current;
                if (app?.Resources[resourceKey] is Brush brush)
                {
                    return brush;
                }
            }
            catch
            {
                // 忽略异常，使用默认值
            }
            return fallback;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _highlightTimer?.Stop();
            _lineCache?.Clear();

            var scrollViewer = GetScrollViewer(_richTextBox);
            if (scrollViewer != null)
            {
                scrollViewer.ScrollChanged -= OnScrollChanged;
            }
        }
    }

    /// <summary>
    /// 语法高亮文本范围结构
    /// </summary>
    public struct HighlightRange
    {
        public int Start { get; set; }
        public int Length { get; set; }
    }

    /// <summary>
    /// 高亮缓存项
    /// </summary>
    public class HighlightCache
    {
        public string Text { get; set; } = "";
        public DateTime LastHighlighted { get; set; }
    }

    /// <summary>
    /// 语言规则定义
    /// </summary>
    public class LanguageRules
    {
        public string[] Keywords { get; set; } = Array.Empty<string>();
        public Brush KeywordBrush { get; set; } = Brushes.Blue;
        public Brush StringBrush { get; set; } = Brushes.Brown;
        public Brush CommentBrush { get; set; } = Brushes.Green;
        public string CommentStart { get; set; } = "#";
        public string? CommentEnd { get; set; } = null; // 用于块注释
    }
}
