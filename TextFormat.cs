using System.Windows;
using System.Windows.Media;

namespace 像素喵笔记
{
    /// <summary>
    /// 文本格式化信息
    /// </summary>
    public class TextFormat
    {
        public bool? IsBold { get; set; }
        public bool? IsItalic { get; set; }
        public bool? IsUnderline { get; set; }
        public bool? IsStrikethrough { get; set; }
        public Color? FontColor { get; set; }
        public Color? BackgroundColor { get; set; }
        public string? FontFamily { get; set; }
        public double? FontSize { get; set; }
        public TextAlignment? TextAlignment { get; set; }
        public string? ListType { get; set; } // "bullet", "number", "none"
        
        public TextFormat()
        {
        }
        
        public TextFormat(bool? isBold = null, bool? isItalic = null, bool? isUnderline = null, 
                         bool? isStrikethrough = null, Color? fontColor = null, Color? backgroundColor = null,
                         string? fontFamily = null, double? fontSize = null, TextAlignment? textAlignment = null,
                         string? listType = null)
        {
            IsBold = isBold;
            IsItalic = isItalic;
            IsUnderline = isUnderline;
            IsStrikethrough = isStrikethrough;
            FontColor = fontColor;
            BackgroundColor = backgroundColor;
            FontFamily = fontFamily;
            FontSize = fontSize;
            TextAlignment = textAlignment;
            ListType = listType;
        }
    }
}
