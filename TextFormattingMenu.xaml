<UserControl x:Class="像素喵笔记.TextFormattingMenu"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="50" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- 格式化按钮样式 - 支持主题切换 -->
        <Style x:Key="DarkFormattingButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="{DynamicResource ButtonForegroundBrush}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="MinWidth" Value="32"/>
            <Setter Property="MinHeight" Value="32"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" TargetName="border" Value="#333333"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" TargetName="border" Value="#555555"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 深色ToggleButton样式 -->
        <Style x:Key="DarkFormattingToggleStyle" TargetType="ToggleButton">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#F0F0F0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="MinWidth" Value="32"/>
            <Setter Property="MinHeight" Value="32"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border x:Name="border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" TargetName="border" Value="#333333"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" TargetName="border" Value="#555555"/>
                            </Trigger>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter Property="Background" TargetName="border" Value="#0099ff"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 浅色ComboBox样式 -->
        <Style x:Key="LightFormattingComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontFamily" Value="Segoe UI"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="MinHeight" Value="32"/>
            <Setter Property="ItemContainerStyle">
                <Setter.Value>
                    <Style TargetType="ComboBoxItem">
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="Foreground" Value="#333333"/>
                        <Setter Property="Padding" Value="8,4"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ComboBoxItem">
                                    <Border x:Name="ItemBorder"
                                            Background="{TemplateBinding Background}"
                                            Padding="{TemplateBinding Padding}"
                                            CornerRadius="4">
                                        <ContentPresenter />
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="ItemBorder" Property="Background" Value="#F0F0F0"/>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter TargetName="ItemBorder" Property="Background" Value="#0099ff"/>
                                            <Setter TargetName="ItemBorder" Property="TextElement.Foreground" Value="White"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </Setter.Value>
            </Setter>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <ToggleButton x:Name="ToggleButton"
                                          BorderBrush="{TemplateBinding BorderBrush}"
                                          Background="{TemplateBinding Background}"
                                          Focusable="False"
                                          IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                          ClickMode="Press">
                                <ToggleButton.Template>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border x:Name="border"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="1"
                                                CornerRadius="4">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Grid.Column="1"
                                                          Text="▼"
                                                          Foreground="#666666"
                                                          FontSize="10"
                                                          Margin="0,0,8,0"
                                                          VerticalAlignment="Center"/>
                                            </Grid>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                                            </Trigger>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter Property="BorderBrush" TargetName="border" Value="#0099ff"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </ToggleButton.Template>
                            </ToggleButton>

                            <!-- 🔧 修复可编辑文本框样式 -->
                            <TextBox x:Name="PART_EditableTextBox"
                                     Margin="8,4,24,4"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     Foreground="#333333"
                                     FontSize="12"
                                     FontFamily="Segoe UI"
                                     VerticalAlignment="Center"
                                     HorizontalAlignment="Left"
                                     Focusable="True"
                                     Visibility="Hidden"
                                     IsReadOnly="{Binding Path=IsReadOnly, RelativeSource={RelativeSource TemplatedParent}}"
                                     Text="{Binding Path=Text, RelativeSource={RelativeSource TemplatedParent}, Mode=TwoWay}"/>

                            <!-- 🔧 修复非编辑模式的内容显示 -->
                            <ContentPresenter x:Name="ContentSite"
                                             Margin="8,4,24,4"
                                             HorizontalAlignment="Left"
                                             VerticalAlignment="Center"
                                             Content="{TemplateBinding SelectionBoxItem}"
                                             ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                             ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                             IsHitTestVisible="False">
                                <ContentPresenter.Resources>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="Foreground" Value="#333333"/>
                                        <Setter Property="FontSize" Value="12"/>
                                        <Setter Property="FontFamily" Value="Segoe UI"/>
                                    </Style>
                                </ContentPresenter.Resources>
                            </ContentPresenter>

                            <Popup x:Name="PART_Popup"
                                   IsOpen="{Binding Path=IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                   Placement="Bottom"
                                   AllowsTransparency="True">
                                <Border Background="White"
                                        BorderBrush="#CCCCCC"
                                        BorderThickness="1"
                                        CornerRadius="4"
                                        MaxHeight="200">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                                    </Border.Effect>
                                    <ScrollViewer>
                                        <ItemsPresenter />
                                    </ScrollViewer>
                                </Border>
                            </Popup>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsEditable" Value="True">
                                <Setter Property="Visibility" TargetName="PART_EditableTextBox" Value="Visible"/>
                                <Setter Property="Visibility" TargetName="ContentSite" Value="Hidden"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <!-- 主容器 -->
    <Border x:Name="mainBorder"
            Background="#1E1E1E"
            BorderBrush="#333333"
            BorderThickness="1"
            CornerRadius="8"
            Padding="8,4"
            Opacity="0">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" Opacity="0.45" BlurRadius="12"/>
        </Border.Effect>

        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">

            <!-- 字体系列选择器 -->
            <ComboBox x:Name="fontFamilyComboBox"
                      Style="{StaticResource LightFormattingComboBoxStyle}"
                      Width="140"
                      SelectedIndex="0"
                      SelectionChanged="FontFamily_SelectionChanged">
                <!-- 字体项目将通过代码动态加载 -->
            </ComboBox>

            <!-- 分隔符 -->
            <Border Width="1" Height="24" Background="#555555" Margin="4,0"/>

            <!-- 字体大小选择器 -->
            <ComboBox x:Name="fontSizeComboBox"
                      Style="{StaticResource LightFormattingComboBoxStyle}"
                      Width="80"
                      MinWidth="80"
                      IsEditable="True"
                      Text="14"
                      SelectionChanged="FontSize_SelectionChanged">
                <ComboBoxItem Content="8"/>
                <ComboBoxItem Content="9"/>
                <ComboBoxItem Content="10"/>
                <ComboBoxItem Content="11"/>
                <ComboBoxItem Content="12"/>
                <ComboBoxItem Content="14"/>
                <ComboBoxItem Content="16"/>
                <ComboBoxItem Content="18"/>
                <ComboBoxItem Content="20"/>
                <ComboBoxItem Content="24"/>
                <ComboBoxItem Content="28"/>
                <ComboBoxItem Content="32"/>
                <ComboBoxItem Content="36"/>
                <ComboBoxItem Content="48"/>
                <ComboBoxItem Content="72"/>
            </ComboBox>

            <!-- 分隔符 -->
            <Border Width="1" Height="24" Background="{DynamicResource AppBorderBrush}" Margin="4,0"/>

            <!-- 字体颜色按钮 -->
            <Button x:Name="fontColorButton"
                    Style="{StaticResource DarkFormattingButtonStyle}"
                    Width="32" Height="32"
                    Click="FontColor_Click"
                    ToolTip="字体颜色">
                <Grid>
                    <TextBlock Text="A"
                               FontSize="16"
                               FontWeight="Bold"
                               Foreground="{DynamicResource ButtonForegroundBrush}"
                               HorizontalAlignment="Center"
                               VerticalAlignment="Center"/>
                    <Rectangle x:Name="colorIndicator"
                               Width="16" Height="3"
                               Fill="{DynamicResource AppForegroundBrush}"
                               VerticalAlignment="Bottom"
                               Margin="0,0,0,2"/>
                </Grid>
            </Button>

            <!-- 分隔符 -->
            <Border Width="1" Height="24" Background="#555555" Margin="4,0"/>

            <!-- 粗体按钮 -->
            <ToggleButton x:Name="boldToggle"
                          Style="{StaticResource DarkFormattingToggleStyle}"
                          Width="32" Height="32"
                          Click="Bold_Click"
                          ToolTip="粗体">
                <TextBlock Text="B"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="#F0F0F0"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </ToggleButton>

            <!-- 斜体按钮 -->
            <ToggleButton x:Name="italicToggle"
                          Style="{StaticResource DarkFormattingToggleStyle}"
                          Width="32" Height="32"
                          Click="Italic_Click"
                          ToolTip="斜体">
                <TextBlock Text="I"
                           FontSize="16"
                           FontStyle="Italic"
                           FontWeight="Bold"
                           Foreground="#F0F0F0"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </ToggleButton>

            <!-- 下划线按钮 -->
            <ToggleButton x:Name="underlineToggle"
                          Style="{StaticResource DarkFormattingToggleStyle}"
                          Width="32" Height="32"
                          Click="Underline_Click"
                          ToolTip="下划线">
                <TextBlock Text="U"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="#F0F0F0"
                           TextDecorations="Underline"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </ToggleButton>

            <!-- 删除线按钮 -->
            <ToggleButton x:Name="strikethroughToggle"
                          Style="{StaticResource DarkFormattingToggleStyle}"
                          Width="32" Height="32"
                          Click="Strikethrough_Click"
                          ToolTip="删除线">
                <TextBlock Text="S"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="#F0F0F0"
                           TextDecorations="Strikethrough"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </ToggleButton>

            <!-- 分隔符 -->
            <Border Width="1" Height="24" Background="#555555" Margin="4,0"/>

            <!-- 左对齐按钮 -->
            <Button x:Name="alignLeftButton"
                    Style="{StaticResource DarkFormattingButtonStyle}"
                    Width="32" Height="32"
                    Click="AlignLeft_Click"
                    ToolTip="左对齐">
                <TextBlock Text="≡"
                           FontSize="14"
                           FontWeight="Bold"
                           Foreground="#F0F0F0"
                           HorizontalAlignment="Left"
                           VerticalAlignment="Center"
                           Margin="4,0,0,0"/>
            </Button>

            <!-- 居中对齐按钮 -->
            <Button x:Name="alignCenterButton"
                    Style="{StaticResource DarkFormattingButtonStyle}"
                    Width="32" Height="32"
                    Click="AlignCenter_Click"
                    ToolTip="居中对齐">
                <TextBlock Text="≣"
                           FontSize="14"
                           FontWeight="Bold"
                           Foreground="#F0F0F0"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Button>

            <!-- 右对齐按钮 -->
            <Button x:Name="alignRightButton"
                    Style="{StaticResource DarkFormattingButtonStyle}"
                    Width="32" Height="32"
                    Click="AlignRight_Click"
                    ToolTip="右对齐">
                <TextBlock Text="≡"
                           FontSize="14"
                           FontWeight="Bold"
                           Foreground="#F0F0F0"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Margin="0,0,4,0"/>
            </Button>

            <!-- 分隔符 -->
            <Border Width="1" Height="24" Background="#555555" Margin="8,0"/>

            <!-- 取消按钮 -->
            <Button x:Name="cancelButton"
                    Style="{StaticResource DarkFormattingButtonStyle}"
                    Width="32" Height="32"
                    Background="#DC3545"
                    Click="Cancel_Click"
                    ToolTip="取消">
                <TextBlock Text="×"
                           FontSize="18"
                           FontWeight="Bold"
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Button>

            <!-- 确认按钮 -->
            <Button x:Name="confirmButton"
                    Style="{StaticResource DarkFormattingButtonStyle}"
                    Width="32" Height="32"
                    Background="#0099ff"
                    Click="Confirm_Click"
                    ToolTip="确认">
                <TextBlock Text="✓"
                           FontSize="16"
                           FontWeight="Bold"
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
            </Button>

        </StackPanel>
    </Border>
</UserControl>
