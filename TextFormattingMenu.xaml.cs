using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;

namespace 像素喵笔记
{
    /// <summary>
    /// TextFormattingMenu.xaml 的交互逻辑
    /// 动态格式化菜单控件
    /// </summary>
    public partial class TextFormattingMenu : UserControl
    {
        public event Action<TextFormat>? OnFormatApplied;
        public event Action? OnMenuClosed;

        private Color _currentFontColor = Colors.Black;

        public TextFormattingMenu()
        {
            InitializeComponent();
            InitializeMenu();
        }

        private void InitializeMenu()
        {
            // 设置默认字体颜色指示器
            colorIndicator.Fill = new SolidColorBrush(_currentFontColor);

            // 动态加载系统字体
            LoadSystemFonts();

            // 监听键盘事件
            this.KeyDown += TextFormattingMenu_KeyDown;
            this.Focusable = true;
        }

        private void LoadSystemFonts()
        {
            // 清空现有项目
            fontFamilyComboBox.Items.Clear();

            // 获取系统安装的所有字体
            var systemFonts = Fonts.SystemFontFamilies
                .Select(f => f.Source)
                .OrderBy(name => name)
                .ToList();

            // 添加常用中文字体到顶部
            var commonChineseFonts = new[]
            {
                "微软雅黑", "宋体", "黑体", "楷体", "仿宋", "隶书", "幼圆"
            };

            foreach (var font in commonChineseFonts)
            {
                if (systemFonts.Contains(font))
                {
                    var item = new ComboBoxItem
                    {
                        Content = font,
                        FontFamily = new FontFamily(font)
                    };
                    fontFamilyComboBox.Items.Add(item);
                    systemFonts.Remove(font); // 避免重复
                }
            }

            // 添加分隔符
            if (fontFamilyComboBox.Items.Count > 0)
            {
                fontFamilyComboBox.Items.Add(new Separator());
            }

            // 添加其他系统字体
            foreach (var fontName in systemFonts)
            {
                var item = new ComboBoxItem
                {
                    Content = fontName,
                    FontFamily = new FontFamily(fontName)
                };
                fontFamilyComboBox.Items.Add(item);
            }

            // 设置默认选择
            if (fontFamilyComboBox.Items.Count > 0)
            {
                fontFamilyComboBox.SelectedIndex = 0;
            }
        }

        private void TextFormattingMenu_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            switch (e.Key)
            {
                case System.Windows.Input.Key.Enter:
                    Confirm_Click(this, new RoutedEventArgs());
                    e.Handled = true;
                    break;
                case System.Windows.Input.Key.Escape:
                    Cancel_Click(this, new RoutedEventArgs());
                    e.Handled = true;
                    break;
            }
        }

        /// <summary>
        /// 显示菜单动画
        /// </summary>
        public void ShowWithAnimation()
        {
            // 从上往下滑入并淡入
            var translateTransform = new TranslateTransform(0, -20);
            this.RenderTransform = translateTransform;

            var storyboard = new Storyboard();

            // 透明度动画
            var opacityAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(200),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
            };
            Storyboard.SetTarget(opacityAnimation, mainBorder);
            Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));

            // 位移动画
            var translateAnimation = new DoubleAnimation
            {
                From = -20,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(200),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
            };
            Storyboard.SetTarget(translateAnimation, translateTransform);
            Storyboard.SetTargetProperty(translateAnimation, new PropertyPath("Y"));

            storyboard.Children.Add(opacityAnimation);
            storyboard.Children.Add(translateAnimation);
            storyboard.Begin();

            // 设置焦点以接收键盘事件
            this.Focus();
        }

        /// <summary>
        /// 隐藏菜单动画
        /// </summary>
        public void HideWithAnimation(Action? onCompleted = null)
        {
            var translateTransform = this.RenderTransform as TranslateTransform ?? new TranslateTransform();
            this.RenderTransform = translateTransform;

            var storyboard = new Storyboard();

            // 透明度动画
            var opacityAnimation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(150),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTarget(opacityAnimation, mainBorder);
            Storyboard.SetTargetProperty(opacityAnimation, new PropertyPath("Opacity"));

            // 位移动画
            var translateAnimation = new DoubleAnimation
            {
                From = 0,
                To = -20,
                Duration = TimeSpan.FromMilliseconds(150),
                EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
            };
            Storyboard.SetTarget(translateAnimation, translateTransform);
            Storyboard.SetTargetProperty(translateAnimation, new PropertyPath("Y"));

            storyboard.Children.Add(opacityAnimation);
            storyboard.Children.Add(translateAnimation);

            if (onCompleted != null)
            {
                storyboard.Completed += (s, e) => onCompleted();
            }

            storyboard.Begin();
        }

        #region 事件处理

        private void FontFamily_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (fontFamilyComboBox.SelectedItem is ComboBoxItem item && item.Content != null)
            {
                var fontName = item.Content.ToString();
                if (!string.IsNullOrEmpty(fontName))
                {
                    var format = new TextFormat { FontFamily = fontName };
                    OnFormatApplied?.Invoke(format);
                }
            }
            else if (fontFamilyComboBox.SelectedItem is Separator)
            {
                // 如果选中了分隔符，跳过到下一个有效项目
                var currentIndex = fontFamilyComboBox.SelectedIndex;
                if (currentIndex + 1 < fontFamilyComboBox.Items.Count)
                {
                    fontFamilyComboBox.SelectedIndex = currentIndex + 1;
                }
            }
        }

        private void FontSize_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (double.TryParse(fontSizeComboBox.Text, out double size))
            {
                var format = new TextFormat { FontSize = size };
                OnFormatApplied?.Invoke(format);
            }
        }

        private void FontColor_Click(object sender, RoutedEventArgs e)
        {
            var colorPickerDialog = new ColorPickerDialog();
            if (colorPickerDialog.ShowDialog() == true)
            {
                _currentFontColor = colorPickerDialog.SelectedColor;
                colorIndicator.Fill = new SolidColorBrush(_currentFontColor);

                var format = new TextFormat { FontColor = _currentFontColor };
                OnFormatApplied?.Invoke(format);
            }
        }

        private void Bold_Click(object sender, RoutedEventArgs e)
        {
            var format = new TextFormat { IsBold = boldToggle.IsChecked };
            OnFormatApplied?.Invoke(format);
        }

        private void Italic_Click(object sender, RoutedEventArgs e)
        {
            var format = new TextFormat { IsItalic = italicToggle.IsChecked };
            OnFormatApplied?.Invoke(format);
        }

        private void Underline_Click(object sender, RoutedEventArgs e)
        {
            var format = new TextFormat { IsUnderline = underlineToggle.IsChecked };
            OnFormatApplied?.Invoke(format);
        }

        private void Strikethrough_Click(object sender, RoutedEventArgs e)
        {
            var format = new TextFormat { IsStrikethrough = strikethroughToggle.IsChecked };
            OnFormatApplied?.Invoke(format);
        }

        private void AlignLeft_Click(object sender, RoutedEventArgs e)
        {
            var format = new TextFormat { TextAlignment = TextAlignment.Left };
            OnFormatApplied?.Invoke(format);
        }

        private void AlignCenter_Click(object sender, RoutedEventArgs e)
        {
            var format = new TextFormat { TextAlignment = TextAlignment.Center };
            OnFormatApplied?.Invoke(format);
        }

        private void AlignRight_Click(object sender, RoutedEventArgs e)
        {
            var format = new TextFormat { TextAlignment = TextAlignment.Right };
            OnFormatApplied?.Invoke(format);
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            OnMenuClosed?.Invoke();
        }

        private void Confirm_Click(object sender, RoutedEventArgs e)
        {
            OnMenuClosed?.Invoke();
        }

        #endregion
    }
}
