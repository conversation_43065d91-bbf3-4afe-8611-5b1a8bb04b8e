using System;
using System.Linq;
using System.Windows;
using System.Windows.Media;

namespace 像素喵笔记
{
    /// <summary>
    /// 主题管理器
    /// 负责应用程序主题的切换和管理
    /// </summary>
    public static class ThemeManager
    {
        #region 公共事件

        /// <summary>
        /// 主题改变事件
        /// </summary>
        public static event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

        #endregion

        #region 公共属性

        /// <summary>
        /// 当前主题
        /// </summary>
        public static AppTheme CurrentTheme { get; private set; } = AppTheme.Light;

        #endregion

        #region 静态构造函数

        static ThemeManager()
        {
            // 初始化默认浅色主题
            InitializeDefaultTheme();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 应用主题
        /// </summary>
        public static void ApplyTheme(AppTheme theme)
        {
            try
            {
                var oldTheme = CurrentTheme;
                CurrentTheme = theme;

                // 获取应用程序资源
                var app = Application.Current;
                if (app?.Resources == null) return;

                // 清除现有主题资源
                ClearThemeResources(app.Resources);

                // 应用新主题
                switch (theme)
                {
                    case AppTheme.Light:
                        ApplyLightTheme(app.Resources);
                        break;
                    case AppTheme.Dark:
                        ApplyDarkTheme(app.Resources);
                        break;
                }

                // 触发主题改变事件
                ThemeChanged?.Invoke(null, new ThemeChangedEventArgs(oldTheme, theme));

                System.Diagnostics.Debug.WriteLine($"主题已切换: {oldTheme} -> {theme}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换主题
        /// </summary>
        public static void ToggleTheme()
        {
            var newTheme = CurrentTheme == AppTheme.Light ? AppTheme.Dark : AppTheme.Light;
            ApplyTheme(newTheme);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化默认主题
        /// </summary>
        private static void InitializeDefaultTheme()
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources != null)
                {
                    ApplyLightTheme(app.Resources);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化默认主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除主题资源
        /// </summary>
        private static void ClearThemeResources(ResourceDictionary resources)
        {
            try
            {
                // 移除主题相关的动态资源
                var keysToRemove = new[]
                {
                    "AppBackgroundBrush",
                    "AppForegroundBrush",
                    "AppCardBackgroundBrush",
                    "AppBorderBrush",
                    "AppSecondaryTextBrush",
                    "DefaultContextMenuStyle",
                    "DefaultMenuItemStyle",
                    "DefaultTextBoxStyle",
                    "DefaultRichTextBoxStyle"
                };

                foreach (var key in keysToRemove)
                {
                    if (resources.Contains(key))
                    {
                        resources.Remove(key);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除主题资源失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用浅色主题
        /// </summary>
        private static void ApplyLightTheme(ResourceDictionary resources)
        {
            try
            {
                // 基础颜色
                resources["AppBackgroundBrush"] = new SolidColorBrush(Colors.White);
                resources["AppForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0x1F, 0x29, 0x37));
                resources["AppCardBackgroundBrush"] = new SolidColorBrush(Colors.White);
                resources["AppBorderBrush"] = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED));
                resources["AppSecondaryTextBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x72, 0x80));

                // TreeView浅色主题颜色
                resources["TreeViewBackgroundBrush"] = new SolidColorBrush(Colors.White);
                resources["TreeViewForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0x1F, 0x29, 0x37));

                // 按钮浅色主题颜色
                resources["ButtonBackgroundBrush"] = new SolidColorBrush(Colors.White);
                resources["ButtonForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0x1F, 0x29, 0x37));
                resources["ButtonBorderBrush"] = new SolidColorBrush(Color.FromRgb(0xDA, 0xDC, 0xE0));

                // 滚动条浅色主题颜色
                resources["ScrollBarThumbBrush"] = new SolidColorBrush(Color.FromRgb(0xDA, 0xDC, 0xE0));
                resources["ScrollBarTrackBrush"] = new SolidColorBrush(Colors.Transparent);

                // 菜单项浅色主题颜色
                resources["MenuItemHoverBrush"] = new SolidColorBrush(Color.FromRgb(0xF8, 0xF9, 0xFA));
                resources["MenuItemPressedBrush"] = new SolidColorBrush(Color.FromRgb(0xE8, 0xF0, 0xFE));

                // 危险操作浅色主题颜色
                resources["DangerBrush"] = new SolidColorBrush(Color.FromRgb(239, 68, 68));

                // 清除深色主题的控件样式
                ClearDarkControlStyles(resources);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用浅色主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除深色主题的控件样式
        /// </summary>
        private static void ClearDarkControlStyles(ResourceDictionary resources)
        {
            try
            {
                // 移除深色主题的控件样式
                var stylesToRemove = new[]
                {
                    "DefaultContextMenuStyle",
                    "DefaultMenuItemStyle",
                    "DefaultTextBoxStyle",
                    "DefaultRichTextBoxStyle"
                };

                foreach (var styleKey in stylesToRemove)
                {
                    if (resources.Contains(styleKey))
                    {
                        resources.Remove(styleKey);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除深色控件样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用深色主题
        /// </summary>
        private static void ApplyDarkTheme(ResourceDictionary resources)
        {
            try
            {
                // 基础颜色
                resources["AppBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x1A, 0x20, 0x2C));
                resources["AppForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF7, 0xFA, 0xFC));
                resources["AppCardBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x2D, 0x37, 0x48));
                resources["AppBorderBrush"] = new SolidColorBrush(Color.FromRgb(0x4A, 0x55, 0x68));
                resources["AppSecondaryTextBrush"] = new SolidColorBrush(Color.FromRgb(0xA0, 0xAE, 0xC0));

                // TreeView深色主题颜色
                resources["TreeViewBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x2D, 0x37, 0x48));
                resources["TreeViewForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF7, 0xFA, 0xFC));

                // 按钮深色主题颜色
                resources["ButtonBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x4A, 0x55, 0x68));
                resources["ButtonForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF7, 0xFA, 0xFC));
                resources["ButtonBorderBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x7C, 0x93));

                // 滚动条深色主题颜色
                resources["ScrollBarThumbBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x7C, 0x93));
                resources["ScrollBarTrackBrush"] = new SolidColorBrush(Color.FromRgb(0x2D, 0x37, 0x48));

                // 菜单项深色主题颜色
                resources["MenuItemHoverBrush"] = new SolidColorBrush(Color.FromRgb(0x4A, 0x55, 0x68));
                resources["MenuItemPressedBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x7C, 0x93));

                // 危险操作深色主题颜色
                resources["DangerBrush"] = new SolidColorBrush(Color.FromRgb(248, 113, 113));

                // 应用深色主题的控件样式
                ApplyDarkControlStyles(resources);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用深色主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用深色主题的控件样式
        /// </summary>
        private static void ApplyDarkControlStyles(ResourceDictionary resources)
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources == null) return;

                // 查找并应用深色主题样式
                var figmaStyles = app.Resources.MergedDictionaries
                    .FirstOrDefault(d => d.Source?.OriginalString?.Contains("FigmaStyles.xaml") == true);

                if (figmaStyles != null)
                {
                    // 应用深色主题的右键菜单样式
                    if (figmaStyles.Contains("DarkFigmaContextMenuStyle"))
                    {
                        resources["DefaultContextMenuStyle"] = figmaStyles["DarkFigmaContextMenuStyle"];
                    }

                    // 应用深色主题的菜单项样式
                    if (figmaStyles.Contains("DarkFigmaMenuItemStyle"))
                    {
                        resources["DefaultMenuItemStyle"] = figmaStyles["DarkFigmaMenuItemStyle"];
                    }

                    // 应用深色主题的文本框样式
                    if (figmaStyles.Contains("DarkFigmaTextBoxStyle"))
                    {
                        resources["DefaultTextBoxStyle"] = figmaStyles["DarkFigmaTextBoxStyle"];
                    }

                    // 应用深色主题的富文本框样式
                    if (figmaStyles.Contains("DarkFigmaRichTextBoxStyle"))
                    {
                        resources["DefaultRichTextBoxStyle"] = figmaStyles["DarkFigmaRichTextBoxStyle"];
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用深色控件样式失败: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 应用程序主题枚举
    /// </summary>
    public enum AppTheme
    {
        Light,
        Dark
    }

    /// <summary>
    /// 主题改变事件参数
    /// </summary>
    public class ThemeChangedEventArgs : EventArgs
    {
        public AppTheme OldTheme { get; }
        public AppTheme NewTheme { get; }

        public ThemeChangedEventArgs(AppTheme oldTheme, AppTheme newTheme)
        {
            OldTheme = oldTheme;
            NewTheme = newTheme;
        }
    }
}
