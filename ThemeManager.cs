using System;
using System.Linq;
using System.Windows;
using System.Windows.Media;

namespace 像素喵笔记
{
    /// <summary>
    /// 主题管理器
    /// 负责应用程序主题的切换和管理
    /// </summary>
    public static class ThemeManager
    {
        #region 公共事件

        /// <summary>
        /// 主题改变事件
        /// </summary>
        public static event EventHandler<ThemeChangedEventArgs>? ThemeChanged;

        #endregion

        #region 公共属性

        /// <summary>
        /// 当前主题
        /// </summary>
        public static AppTheme CurrentTheme { get; private set; } = AppTheme.Light;

        #endregion

        #region 静态构造函数

        static ThemeManager()
        {
            // 初始化默认浅色主题
            InitializeDefaultTheme();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 应用主题 - 重构版本，使用FigmaStyles.xaml中的主题资源
        /// </summary>
        public static void ApplyTheme(AppTheme theme)
        {
            try
            {
                var oldTheme = CurrentTheme;
                CurrentTheme = theme;

                // 获取应用程序资源
                var app = Application.Current;
                if (app?.Resources == null) return;

                // 获取FigmaStyles资源字典
                var figmaStyles = GetFigmaStylesResourceDictionary();
                if (figmaStyles == null)
                {
                    System.Diagnostics.Debug.WriteLine("无法获取FigmaStyles资源字典，回退到传统方式");
                    ApplyThemeLegacy(theme, app.Resources);
                    return;
                }

                // 应用新主题资源
                ApplyThemeFromFigmaStyles(theme, figmaStyles, app.Resources);

                // 触发主题改变事件
                ThemeChanged?.Invoke(null, new ThemeChangedEventArgs(oldTheme, theme));

                // 更新现有表格的主题
                UpdateExistingTablesTheme();

                // 刷新节点界面
                RefreshNodeInterface();

                System.Diagnostics.Debug.WriteLine($"主题已切换: {oldTheme} -> {theme}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 切换主题
        /// </summary>
        public static void ToggleTheme()
        {
            var newTheme = CurrentTheme == AppTheme.Light ? AppTheme.Dark : AppTheme.Light;
            ApplyTheme(newTheme);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化默认主题 - 重构版本
        /// </summary>
        private static void InitializeDefaultTheme()
        {
            try
            {
                ApplyTheme(AppTheme.Light);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化默认主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取FigmaStyles资源字典
        /// </summary>
        private static ResourceDictionary? GetFigmaStylesResourceDictionary()
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources == null) return null;

                // 查找FigmaStyles资源字典
                foreach (var mergedDict in app.Resources.MergedDictionaries)
                {
                    if (mergedDict.Source?.OriginalString?.Contains("FigmaStyles.xaml") == true)
                    {
                        return mergedDict;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取FigmaStyles资源字典失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从FigmaStyles应用主题资源
        /// </summary>
        private static void ApplyThemeFromFigmaStyles(AppTheme theme, ResourceDictionary figmaStyles, ResourceDictionary appResources)
        {
            try
            {
                // 清除现有主题资源
                ClearThemeResources(appResources);

                // 获取对应的主题资源字典
                string themeKey = theme == AppTheme.Light ? "LightThemeResources" : "DarkThemeResources";

                if (figmaStyles.Contains(themeKey) && figmaStyles[themeKey] is ResourceDictionary themeResources)
                {
                    // 将主题资源复制到应用程序资源中
                    foreach (var key in themeResources.Keys)
                    {
                        appResources[key] = themeResources[key];
                    }

                    System.Diagnostics.Debug.WriteLine($"已从FigmaStyles应用{theme}主题，资源数量: {themeResources.Count}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"在FigmaStyles中未找到{themeKey}，回退到传统方式");
                    ApplyThemeLegacy(theme, appResources);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从FigmaStyles应用主题失败: {ex.Message}");
                ApplyThemeLegacy(theme, appResources);
            }
        }

        /// <summary>
        /// 传统方式应用主题（回退方案）
        /// </summary>
        private static void ApplyThemeLegacy(AppTheme theme, ResourceDictionary resources)
        {
            switch (theme)
            {
                case AppTheme.Light:
                    ApplyLightTheme(resources);
                    break;
                case AppTheme.Dark:
                    ApplyDarkTheme(resources);
                    break;
            }
        }

        /// <summary>
        /// 更新现有表格的主题颜色
        /// </summary>
        private static void UpdateExistingTablesTheme()
        {
            try
            {
                var app = Application.Current;
                if (app?.MainWindow is MainWindow mainWindow)
                {
                    // 调用MainWindow的表格主题更新方法
                    mainWindow.UpdateAllTablesTheme();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新表格主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新节点界面以应用新主题
        /// </summary>
        private static void RefreshNodeInterface()
        {
            try
            {
                var app = Application.Current;
                if (app?.MainWindow is MainWindow mainWindow)
                {
                    // 强制刷新TreeView以应用新的主题颜色
                    mainWindow.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        mainWindow.RefreshTreeViewForTheme();
                    }), System.Windows.Threading.DispatcherPriority.Render);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新节点界面失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除主题资源 - 扩展版本，包含所有主题相关资源
        /// </summary>
        private static void ClearThemeResources(ResourceDictionary resources)
        {
            try
            {
                // 移除主题相关的动态资源
                var keysToRemove = new[]
                {
                    // 基础颜色
                    "AppBackgroundBrush",
                    "AppForegroundBrush",
                    "AppCardBackgroundBrush",
                    "AppSecondaryBackgroundBrush",
                    "AppBorderBrush",
                    "AppSecondaryTextBrush",

                    // 按钮颜色
                    "ButtonBackgroundBrush",
                    "ButtonForegroundBrush",
                    "ButtonBorderBrush",

                    // 滚动条颜色
                    "ScrollBarBackgroundBrush",
                    "ScrollBarThumbBrush",
                    "ScrollBarThumbHoverBrush",

                    // 菜单项颜色
                    "MenuItemHoverBrush",
                    "MenuItemPressedBrush",

                    // 背景色
                    "HoverBackgroundBrush",
                    "PressedBackgroundBrush",
                    "SelectedBackgroundBrush",
                    "DisabledBackgroundBrush",
                    "DisabledForegroundBrush",

                    // 工具提示
                    "AppTooltipBackgroundBrush",
                    "AppTooltipForegroundBrush",

                    // 主题色
                    "PrimaryBrush",
                    "PrimaryHoverBrush",
                    "PrimaryPressedBrush",
                    "ErrorBrush",
                    "SuccessBrush",
                    "WarningBrush",
                    "DangerBrush",

                    // 语法高亮
                    "KeywordBrush",
                    "StringBrush",
                    "CommentBrush",

                    // 控件样式
                    "DefaultContextMenuStyle",
                    "DefaultMenuItemStyle",
                    "DefaultTextBoxStyle",
                    "DefaultRichTextBoxStyle"
                };

                foreach (var key in keysToRemove)
                {
                    if (resources.Contains(key))
                    {
                        resources.Remove(key);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"已清除 {keysToRemove.Length} 个主题资源");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除主题资源失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用浅色主题
        /// </summary>
        private static void ApplyLightTheme(ResourceDictionary resources)
        {
            try
            {
                // 基础颜色
                resources["AppBackgroundBrush"] = new SolidColorBrush(Colors.White);
                resources["AppForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0x1F, 0x29, 0x37));
                resources["AppCardBackgroundBrush"] = new SolidColorBrush(Colors.White);
                resources["AppSecondaryBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF8, 0xF9, 0xFA));
                resources["AppBorderBrush"] = new SolidColorBrush(Color.FromRgb(0xE8, 0xEA, 0xED));
                resources["AppSecondaryTextBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x72, 0x80));

                // TreeView浅色主题颜色
                resources["TreeViewBackgroundBrush"] = new SolidColorBrush(Colors.White);
                resources["TreeViewForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0x1F, 0x29, 0x37));

                // 按钮浅色主题颜色
                resources["ButtonBackgroundBrush"] = new SolidColorBrush(Colors.White);
                resources["ButtonForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0x1F, 0x29, 0x37));
                resources["ButtonBorderBrush"] = new SolidColorBrush(Color.FromRgb(0xDA, 0xDC, 0xE0));

                // 滚动条浅色主题颜色
                resources["ScrollBarThumbBrush"] = new SolidColorBrush(Color.FromRgb(0xDA, 0xDC, 0xE0));
                resources["ScrollBarTrackBrush"] = new SolidColorBrush(Colors.Transparent);

                // 菜单项浅色主题颜色
                resources["MenuItemHoverBrush"] = new SolidColorBrush(Color.FromRgb(0xF8, 0xF9, 0xFA));
                resources["MenuItemPressedBrush"] = new SolidColorBrush(Color.FromRgb(0xE8, 0xF0, 0xFE));

                // 危险操作浅色主题颜色
                resources["DangerBrush"] = new SolidColorBrush(Color.FromRgb(239, 68, 68));

                // 语法高亮浅色主题颜色
                resources["KeywordBrush"] = new SolidColorBrush(Color.FromRgb(0, 0, 255));      // 蓝色关键字
                resources["StringBrush"] = new SolidColorBrush(Color.FromRgb(163, 21, 21));     // 棕色字符串
                resources["CommentBrush"] = new SolidColorBrush(Color.FromRgb(0, 128, 0));      // 绿色注释

                // 主题色浅色版本
                resources["PrimaryBrush"] = new SolidColorBrush(Color.FromRgb(0, 153, 255));    // #0099ff
                resources["PrimaryHoverBrush"] = new SolidColorBrush(Color.FromRgb(21, 87, 176)); // #1557B0
                resources["PrimaryPressedBrush"] = new SolidColorBrush(Color.FromRgb(15, 76, 140)); // #0F4C8C
                resources["ErrorBrush"] = new SolidColorBrush(Color.FromRgb(255, 35, 35));      // #FF2323
                resources["SuccessBrush"] = new SolidColorBrush(Color.FromRgb(52, 168, 83));    // #34A853
                resources["WarningBrush"] = new SolidColorBrush(Color.FromRgb(251, 188, 4));    // #FBBC04

                // 背景色浅色版本
                resources["HoverBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF8, 0xF9, 0xFA));
                resources["PressedBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF1, 0xF3, 0xF4));
                resources["SelectedBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0xE8, 0xF0, 0xFE));
                resources["DisabledBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF1, 0xF3, 0xF4));
                resources["DisabledForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0x9A, 0xA0, 0xA6));

                // 工具提示浅色版本
                resources["AppTooltipBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x2D, 0x2D, 0x30));
                resources["AppTooltipForegroundBrush"] = new SolidColorBrush(Colors.White);

                // 清除深色主题的控件样式
                ClearDarkControlStyles(resources);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用浅色主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除深色主题的控件样式
        /// </summary>
        private static void ClearDarkControlStyles(ResourceDictionary resources)
        {
            try
            {
                // 移除深色主题的控件样式
                var stylesToRemove = new[]
                {
                    "DefaultContextMenuStyle",
                    "DefaultMenuItemStyle",
                    "DefaultTextBoxStyle",
                    "DefaultRichTextBoxStyle"
                };

                foreach (var styleKey in stylesToRemove)
                {
                    if (resources.Contains(styleKey))
                    {
                        resources.Remove(styleKey);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"清除深色控件样式失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用深色主题
        /// </summary>
        private static void ApplyDarkTheme(ResourceDictionary resources)
        {
            try
            {
                // 基础颜色
                resources["AppBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x1A, 0x20, 0x2C));
                resources["AppForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF7, 0xFA, 0xFC));
                resources["AppCardBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x2D, 0x37, 0x48));
                resources["AppSecondaryBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x24, 0x2D, 0x3A));
                resources["AppBorderBrush"] = new SolidColorBrush(Color.FromRgb(0x4A, 0x55, 0x68));
                resources["AppSecondaryTextBrush"] = new SolidColorBrush(Color.FromRgb(0xA0, 0xAE, 0xC0));

                // TreeView深色主题颜色
                resources["TreeViewBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x2D, 0x37, 0x48));
                resources["TreeViewForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF7, 0xFA, 0xFC));

                // 按钮深色主题颜色
                resources["ButtonBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x4A, 0x55, 0x68));
                resources["ButtonForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF7, 0xFA, 0xFC));
                resources["ButtonBorderBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x7C, 0x93));

                // 滚动条深色主题颜色
                resources["ScrollBarThumbBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x7C, 0x93));
                resources["ScrollBarTrackBrush"] = new SolidColorBrush(Color.FromRgb(0x2D, 0x37, 0x48));

                // 菜单项深色主题颜色
                resources["MenuItemHoverBrush"] = new SolidColorBrush(Color.FromRgb(0x4A, 0x55, 0x68));
                resources["MenuItemPressedBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x7C, 0x93));

                // 危险操作深色主题颜色
                resources["DangerBrush"] = new SolidColorBrush(Color.FromRgb(248, 113, 113));

                // 语法高亮深色主题颜色
                resources["KeywordBrush"] = new SolidColorBrush(Color.FromRgb(86, 156, 214));   // 浅蓝色关键字
                resources["StringBrush"] = new SolidColorBrush(Color.FromRgb(206, 145, 120));   // 浅棕色字符串
                resources["CommentBrush"] = new SolidColorBrush(Color.FromRgb(106, 153, 85));   // 浅绿色注释

                // 主题色深色版本
                resources["PrimaryBrush"] = new SolidColorBrush(Color.FromRgb(66, 153, 225));   // #4299E1
                resources["PrimaryHoverBrush"] = new SolidColorBrush(Color.FromRgb(44, 82, 130)); // #2C5282
                resources["PrimaryPressedBrush"] = new SolidColorBrush(Color.FromRgb(42, 67, 101)); // #2A4365
                resources["ErrorBrush"] = new SolidColorBrush(Color.FromRgb(248, 113, 113));    // #F87171
                resources["SuccessBrush"] = new SolidColorBrush(Color.FromRgb(72, 187, 120));   // #48BB78
                resources["WarningBrush"] = new SolidColorBrush(Color.FromRgb(237, 137, 54));   // #ED8936

                // 背景色深色版本
                resources["HoverBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x4A, 0x55, 0x68));
                resources["PressedBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x7C, 0x93));
                resources["SelectedBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x4A, 0x55, 0x68));
                resources["DisabledBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x2D, 0x37, 0x48));
                resources["DisabledForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0x6B, 0x7C, 0x93));

                // 工具提示深色版本
                resources["AppTooltipBackgroundBrush"] = new SolidColorBrush(Color.FromRgb(0x1A, 0x1A, 0x1A));
                resources["AppTooltipForegroundBrush"] = new SolidColorBrush(Color.FromRgb(0xF7, 0xFA, 0xFC));

                // 应用深色主题的控件样式
                ApplyDarkControlStyles(resources);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用深色主题失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 应用深色主题的控件样式
        /// </summary>
        private static void ApplyDarkControlStyles(ResourceDictionary resources)
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources == null) return;

                // 查找并应用深色主题样式
                var figmaStyles = app.Resources.MergedDictionaries
                    .FirstOrDefault(d => d.Source?.OriginalString?.Contains("FigmaStyles.xaml") == true);

                if (figmaStyles != null)
                {
                    // 应用深色主题的右键菜单样式
                    if (figmaStyles.Contains("DarkFigmaContextMenuStyle"))
                    {
                        resources["DefaultContextMenuStyle"] = figmaStyles["DarkFigmaContextMenuStyle"];
                    }

                    // 应用深色主题的菜单项样式
                    if (figmaStyles.Contains("DarkFigmaMenuItemStyle"))
                    {
                        resources["DefaultMenuItemStyle"] = figmaStyles["DarkFigmaMenuItemStyle"];
                    }

                    // 应用深色主题的文本框样式
                    if (figmaStyles.Contains("DarkFigmaTextBoxStyle"))
                    {
                        resources["DefaultTextBoxStyle"] = figmaStyles["DarkFigmaTextBoxStyle"];
                    }

                    // 应用深色主题的富文本框样式
                    if (figmaStyles.Contains("DarkFigmaRichTextBoxStyle"))
                    {
                        resources["DefaultRichTextBoxStyle"] = figmaStyles["DarkFigmaRichTextBoxStyle"];
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"应用深色控件样式失败: {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// 应用程序主题枚举
    /// </summary>
    public enum AppTheme
    {
        Light,
        Dark
    }

    /// <summary>
    /// 主题改变事件参数
    /// </summary>
    public class ThemeChangedEventArgs : EventArgs
    {
        public AppTheme OldTheme { get; }
        public AppTheme NewTheme { get; }

        public ThemeChangedEventArgs(AppTheme oldTheme, AppTheme newTheme)
        {
            OldTheme = oldTheme;
            NewTheme = newTheme;
        }
    }
}
