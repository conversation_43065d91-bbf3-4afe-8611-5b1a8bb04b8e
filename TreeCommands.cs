using System;

namespace 像素喵笔记
{
    /// <summary>
    /// 命令接口
    /// </summary>
    public interface ICommand
    {
        void Execute();
        void Undo();
    }

    /// <summary>
    /// 创建节点命令
    /// </summary>
    public class CreateNodeCommand : ICommand
    {
        private readonly TreeNodeManager _manager;
        private readonly BaseNode _node;
        private readonly BaseNode? _parent;

        public CreateNodeCommand(TreeNodeManager manager, BaseNode node, BaseNode? parent)
        {
            _manager = manager;
            _node = node;
            _parent = parent;
        }

        public void Execute()
        {
            _manager.AddNodeInternal(_node, _parent);
        }

        public void Undo()
        {
            _manager.RemoveNodeInternal(_node);
        }
    }

    /// <summary>
    /// 删除节点命令
    /// </summary>
    public class DeleteNodeCommand : ICommand
    {
        private readonly TreeNodeManager _manager;
        private readonly BaseNode _node;
        private readonly BaseNode? _parent;
        private readonly int _index;

        public DeleteNodeCommand(TreeNodeManager manager, BaseNode node)
        {
            _manager = manager;
            _node = node;
            _parent = node.Parent;
            
            // 记录节点在父节点中的位置
            if (_parent != null)
            {
                _index = _parent.Children.IndexOf(node);
            }
            else
            {
                _index = _manager.RootNodes.IndexOf(node);
            }
        }

        public void Execute()
        {
            _manager.RemoveNodeInternal(_node);
        }

        public void Undo()
        {
            // 恢复到原来的位置
            if (_parent == null)
            {
                if (_index >= 0 && _index <= _manager.RootNodes.Count)
                {
                    _manager.RootNodes.Insert(_index, _node);
                }
                else
                {
                    _manager.RootNodes.Add(_node);
                }
            }
            else
            {
                _node.Parent = _parent;
                if (_index >= 0 && _index <= _parent.Children.Count)
                {
                    _parent.Children.Insert(_index, _node);
                }
                else
                {
                    _parent.Children.Add(_node);
                }
            }
        }
    }

    /// <summary>
    /// 重命名节点命令
    /// </summary>
    public class RenameNodeCommand : ICommand
    {
        private readonly TreeNodeManager _manager;
        private readonly BaseNode _node;
        private readonly string _oldName;
        private readonly string _newName;

        public RenameNodeCommand(TreeNodeManager manager, BaseNode node, string oldName, string newName)
        {
            _manager = manager;
            _node = node;
            _oldName = oldName;
            _newName = newName;
        }

        public void Execute()
        {
            _manager.RenameNodeInternal(_node, _newName);
        }

        public void Undo()
        {
            _manager.RenameNodeInternal(_node, _oldName);
        }
    }

    /// <summary>
    /// 移动节点命令
    /// </summary>
    public class MoveNodeCommand : ICommand
    {
        private readonly TreeNodeManager _manager;
        private readonly BaseNode _node;
        private readonly BaseNode? _oldParent;
        private readonly BaseNode? _newParent;
        private readonly int _oldIndex;
        private readonly int _newIndex;

        public MoveNodeCommand(TreeNodeManager manager, BaseNode node, BaseNode? oldParent, BaseNode? newParent)
        {
            _manager = manager;
            _node = node;
            _oldParent = oldParent;
            _newParent = newParent;

            // 记录原位置
            if (_oldParent != null)
            {
                _oldIndex = _oldParent.Children.IndexOf(node);
            }
            else
            {
                _oldIndex = _manager.RootNodes.IndexOf(node);
            }

            // 计算新位置（在目标父节点的末尾）
            if (_newParent != null)
            {
                _newIndex = _newParent.Children.Count;
            }
            else
            {
                _newIndex = _manager.RootNodes.Count;
            }
        }

        public void Execute()
        {
            _manager.MoveNodeInternal(_node, _oldParent, _newParent);
        }

        public void Undo()
        {
            _manager.MoveNodeInternal(_node, _newParent, _oldParent);
            
            // 恢复到原来的位置
            if (_oldParent == null)
            {
                _manager.RootNodes.Remove(_node);
                if (_oldIndex >= 0 && _oldIndex <= _manager.RootNodes.Count)
                {
                    _manager.RootNodes.Insert(_oldIndex, _node);
                }
                else
                {
                    _manager.RootNodes.Add(_node);
                }
            }
            else
            {
                _oldParent.Children.Remove(_node);
                if (_oldIndex >= 0 && _oldIndex <= _oldParent.Children.Count)
                {
                    _oldParent.Children.Insert(_oldIndex, _node);
                }
                else
                {
                    _oldParent.Children.Add(_node);
                }
                _node.Parent = _oldParent;
            }
        }
    }

    /// <summary>
    /// 批量操作命令
    /// </summary>
    public class BatchCommand : ICommand
    {
        private readonly ICommand[] _commands;

        public BatchCommand(params ICommand[] commands)
        {
            _commands = commands;
        }

        public void Execute()
        {
            foreach (var command in _commands)
            {
                command.Execute();
            }
        }

        public void Undo()
        {
            // 逆序撤销
            for (int i = _commands.Length - 1; i >= 0; i--)
            {
                _commands[i].Undo();
            }
        }
    }
}
