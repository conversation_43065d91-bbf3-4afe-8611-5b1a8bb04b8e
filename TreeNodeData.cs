using System;
using System.Collections.Generic;

namespace 像素喵笔记
{
    /// <summary>
    /// 树节点数据类 - 用于保存和加载
    /// </summary>
    public class TreeNodeData
    {
        /// <summary>
        /// 节点唯一标识符
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 节点名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 父节点引用
        /// </summary>
        public TreeNodeData? Parent { get; set; }

        /// <summary>
        /// 子节点列表
        /// </summary>
        public List<TreeNodeData> Children { get; set; } = new List<TreeNodeData>();

        /// <summary>
        /// 节点创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; } = DateTime.Now;

        /// <summary>
        /// 节点类型（用于确定显示哪种编辑器）
        /// </summary>
        public string NodeType { get; set; } = "Document";

        /// <summary>
        /// 扩展属性（用于存储额外信息）
        /// </summary>
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();

        /// <summary>
        /// 构造函数
        /// </summary>
        public TreeNodeData()
        {
            Id = Guid.NewGuid().ToString();
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">节点名称</param>
        public TreeNodeData(string name) : this()
        {
            Name = name;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="name">节点名称</param>
        /// <param name="parent">父节点</param>
        public TreeNodeData(string name, TreeNodeData? parent) : this(name)
        {
            Parent = parent;
            if (parent != null)
            {
                parent.Children.Add(this);
            }
        }

        /// <summary>
        /// 添加子节点
        /// </summary>
        /// <param name="child">子节点</param>
        public void AddChild(TreeNodeData child)
        {
            if (child != null && !Children.Contains(child))
            {
                child.Parent = this;
                Children.Add(child);
            }
        }

        /// <summary>
        /// 移除子节点
        /// </summary>
        /// <param name="child">子节点</param>
        public void RemoveChild(TreeNodeData child)
        {
            if (child != null && Children.Contains(child))
            {
                child.Parent = null;
                Children.Remove(child);
            }
        }

        /// <summary>
        /// 获取节点路径
        /// </summary>
        /// <returns>从根节点到当前节点的路径</returns>
        public string GetPath()
        {
            var path = new List<string>();
            var current = this;
            
            while (current != null)
            {
                path.Insert(0, current.Name);
                current = current.Parent;
            }
            
            return string.Join("/", path);
        }

        /// <summary>
        /// 获取节点深度
        /// </summary>
        /// <returns>节点深度（根节点为0）</returns>
        public int GetDepth()
        {
            int depth = 0;
            var current = Parent;
            
            while (current != null)
            {
                depth++;
                current = current.Parent;
            }
            
            return depth;
        }

        /// <summary>
        /// 判断是否为根节点
        /// </summary>
        /// <returns>是否为根节点</returns>
        public bool IsRoot => Parent == null;

        /// <summary>
        /// 判断是否为叶子节点
        /// </summary>
        /// <returns>是否为叶子节点</returns>
        public bool IsLeaf => Children.Count == 0;

        /// <summary>
        /// 获取所有后代节点
        /// </summary>
        /// <returns>所有后代节点列表</returns>
        public List<TreeNodeData> GetAllDescendants()
        {
            var descendants = new List<TreeNodeData>();
            
            foreach (var child in Children)
            {
                descendants.Add(child);
                descendants.AddRange(child.GetAllDescendants());
            }
            
            return descendants;
        }

        /// <summary>
        /// 查找子节点
        /// </summary>
        /// <param name="name">节点名称</param>
        /// <returns>找到的子节点，如果没找到返回null</returns>
        public TreeNodeData? FindChild(string name)
        {
            return Children.Find(child => child.Name == name);
        }

        /// <summary>
        /// 递归查找后代节点
        /// </summary>
        /// <param name="name">节点名称</param>
        /// <returns>找到的节点，如果没找到返回null</returns>
        public TreeNodeData? FindDescendant(string name)
        {
            var child = FindChild(name);
            if (child != null)
                return child;
                
            foreach (var childNode in Children)
            {
                var descendant = childNode.FindDescendant(name);
                if (descendant != null)
                    return descendant;
            }
            
            return null;
        }

        /// <summary>
        /// 克隆节点（不包含父子关系）
        /// </summary>
        /// <returns>克隆的节点</returns>
        public TreeNodeData Clone()
        {
            return new TreeNodeData
            {
                Id = Id,
                Name = Name,
                NodeType = NodeType,
                CreatedTime = CreatedTime,
                LastModified = LastModified,
                Properties = new Dictionary<string, object>(Properties)
            };
        }

        /// <summary>
        /// 更新最后修改时间
        /// </summary>
        public void UpdateLastModified()
        {
            LastModified = DateTime.Now;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>节点的字符串表示</returns>
        public override string ToString()
        {
            return $"{Name} ({NodeType}) - {Children.Count} children";
        }
    }
}
