using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;

namespace 像素喵笔记
{
    /// <summary>
    /// 树状节点管理器
    /// </summary>
    public class TreeNodeManager
    {
        private readonly ObservableCollection<BaseNode> _rootNodes;
        private readonly Stack<ICommand> _undoStack;
        private readonly Stack<ICommand> _redoStack;
        private BaseNode? _clipboardNode;
        private bool _isCutOperation;

        public ObservableCollection<BaseNode> RootNodes => _rootNodes;

        private BaseNode? _selectedNode;
        public BaseNode? SelectedNode
        {
            get => _selectedNode;
            set
            {
                if (_selectedNode != value)
                {
                    var oldNode = _selectedNode;
                    _selectedNode = value;

                    // 只触发选中事件，不重复设置IsSelected状态
                    // IsSelected状态由UI层直接管理，避免循环调用
                    if (_selectedNode != null)
                    {
                        NodeSelected?.Invoke(this, new NodeEventArgs(_selectedNode));
                    }
                }
            }
        }

        public event EventHandler<NodeEventArgs>? NodeSelected;
        public event EventHandler<NodeEventArgs>? NodeCreated;
        public event EventHandler<NodeEventArgs>? NodeDeleted;
        public event EventHandler<NodeRenamedEventArgs>? NodeRenamed;
        public event EventHandler<NodeEventArgs>? NodeMoved;

        public TreeNodeManager()
        {
            _rootNodes = new ObservableCollection<BaseNode>();
            _undoStack = new Stack<ICommand>();
            _redoStack = new Stack<ICommand>();
        }

        /// <summary>
        /// 创建新笔记本
        /// </summary>
        public NotebookNode CreateNotebook(string? name = null)
        {
            var notebook = new NotebookNode(name ?? GenerateUniqueName("新笔记本", null));
            var command = new CreateNodeCommand(this, notebook, null);
            ExecuteCommand(command);
            return notebook;
        }

        /// <summary>
        /// 创建新集合
        /// </summary>
        public CollectionNode? CreateCollection(NotebookNode? parent = null, string? name = null)
        {
            parent ??= SelectedNode as NotebookNode;
            if (parent == null || parent.NodeType != NodeType.Notebook)
            {
                MessageBox.Show("请先选择一个笔记本", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return null;
            }

            var collection = new CollectionNode(name ?? GenerateUniqueName("新集合", parent));
            var command = new CreateNodeCommand(this, collection, parent);
            ExecuteCommand(command);
            return collection;
        }

        /// <summary>
        /// 创建新页面
        /// </summary>
        public PageNode? CreatePage(CollectionNode? parent = null, string? name = null)
        {
            parent ??= SelectedNode as CollectionNode;
            if (parent == null || parent.NodeType != NodeType.Collection)
            {
                MessageBox.Show("请先选择一个集合", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return null;
            }

            var page = new PageNode(name ?? GenerateUniqueNameWithSuffix("新页面", parent));
            var command = new CreateNodeCommand(this, page, parent);
            ExecuteCommand(command);
            return page;
        }

        // 🔧 删除CreateDrawingBoard方法

        /// <summary>
        /// 创建新代码页
        /// </summary>
        public CodePageNode? CreateCodePage(CollectionNode? parent = null, string? name = null)
        {
            parent ??= SelectedNode as CollectionNode;
            if (parent == null || parent.NodeType != NodeType.Collection)
            {
                MessageBox.Show("请先选择一个集合", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return null;
            }

            var codePage = new CodePageNode(name ?? GenerateUniqueNameWithSuffix("新代码页", parent));
            var command = new CreateNodeCommand(this, codePage, parent);
            ExecuteCommand(command);

            return codePage;
        }

        /// <summary>
        /// 删除节点
        /// </summary>
        public bool DeleteNode(BaseNode node)
        {
            if (node == null) return false;

            // 🔧 修复：删除所有节点实行静默删除，不弹出提示框
            var command = new DeleteNodeCommand(this, node);
            ExecuteCommand(command);
            return true;
        }

        /// <summary>
        /// 重命名节点
        /// </summary>
        public bool RenameNode(BaseNode node, string newName)
        {
            if (node == null || string.IsNullOrWhiteSpace(newName)) return false;

            // 检查名称合法性
            if (ContainsInvalidChars(newName))
            {
                MessageBox.Show("名称不能包含以下字符: \\ / : * ? \" < > |", "无效名称", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // 检查同级是否存在重名
            if (IsNameConflict(node, newName))
            {
                MessageBox.Show("同级目录下已存在相同名称的项目", "名称冲突", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            var command = new RenameNodeCommand(this, node, node.Name, newName);
            ExecuteCommand(command);
            return true;
        }

        /// <summary>
        /// 移动节点
        /// </summary>
        public bool MoveNode(BaseNode node, BaseNode? newParent)
        {
            if (node == null || !node.CanMoveTo(newParent)) return false;

            // 检查是否会造成循环引用
            if (IsCircularReference(node, newParent)) return false;

            var command = new MoveNodeCommand(this, node, node.Parent, newParent);
            ExecuteCommand(command);
            return true;
        }

        /// <summary>
        /// 剪切节点
        /// </summary>
        public void CutNode(BaseNode node)
        {
            if (node == null) return;

            _clipboardNode = node;
            _isCutOperation = true;
            node.State = NodeState.Cut;
        }

        /// <summary>
        /// 复制节点
        /// </summary>
        public void CopyNode(BaseNode node)
        {
            if (node == null) return;

            _clipboardNode = node.CreateDeepCopy();
            _isCutOperation = false;
        }

        /// <summary>
        /// 粘贴节点
        /// </summary>
        public bool PasteNode(BaseNode? targetParent = null)
        {
            if (_clipboardNode == null) return false;

            targetParent ??= SelectedNode;

            // 检查目标位置是否合法
            if (targetParent != null && !targetParent.CanAddChild(_clipboardNode.NodeType))
            {
                MessageBox.Show("无法在此位置粘贴该类型的节点", "粘贴失败", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (_isCutOperation)
            {
                // 剪切操作：移动原节点
                var success = MoveNode(_clipboardNode, targetParent);
                if (success)
                {
                    _clipboardNode.State = NodeState.Normal;
                    _clipboardNode = null;
                    _isCutOperation = false;
                }
                return success;
            }
            else
            {
                // 复制操作：创建新节点
                var newNode = _clipboardNode.CreateDeepCopy();
                newNode.Name = GenerateUniqueName(newNode.Name, targetParent);

                var command = new CreateNodeCommand(this, newNode, targetParent);
                ExecuteCommand(command);
                return true;
            }
        }

        /// <summary>
        /// 撤销操作
        /// </summary>
        public void Undo()
        {
            if (_undoStack.Count > 0)
            {
                var command = _undoStack.Pop();
                command.Undo();
                _redoStack.Push(command);
            }
        }

        /// <summary>
        /// 重做操作
        /// </summary>
        public void Redo()
        {
            if (_redoStack.Count > 0)
            {
                var command = _redoStack.Pop();
                command.Execute();
                _undoStack.Push(command);
            }
        }

        /// <summary>
        /// 展开所有节点
        /// </summary>
        public void ExpandAll()
        {
            foreach (var node in _rootNodes)
            {
                ExpandNodeRecursive(node);
            }
        }

        /// <summary>
        /// 折叠所有节点
        /// </summary>
        public void CollapseAll()
        {
            foreach (var node in _rootNodes)
            {
                CollapseNodeRecursive(node);
            }
        }

        /// <summary>
        /// 查找节点
        /// </summary>
        public List<BaseNode> FindNodes(string searchText, bool caseSensitive = false)
        {
            var results = new List<BaseNode>();
            var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;

            foreach (var node in _rootNodes)
            {
                FindNodesRecursive(node, searchText, comparison, results);
            }

            return results;
        }

        // 内部方法
        internal void AddNodeInternal(BaseNode node, BaseNode? parent)
        {
            if (parent == null)
            {
                _rootNodes.Add(node);
            }
            else
            {
                node.Parent = parent;
                parent.Children.Add(node);
                parent.IsExpanded = true;
            }

            NodeCreated?.Invoke(this, new NodeEventArgs(node));
        }

        internal void RemoveNodeInternal(BaseNode node)
        {
            if (node.Parent == null)
            {
                _rootNodes.Remove(node);
            }
            else
            {
                node.Parent.Children.Remove(node);
                node.Parent = null;
            }

            NodeDeleted?.Invoke(this, new NodeEventArgs(node));
        }

        internal void RenameNodeInternal(BaseNode node, string newName)
        {
            string oldName = node.Name;
            node.Name = newName;
            NodeRenamed?.Invoke(this, new NodeRenamedEventArgs(node, oldName, newName));
        }

        internal void MoveNodeInternal(BaseNode node, BaseNode? oldParent, BaseNode? newParent)
        {
            // 从旧位置移除
            if (oldParent == null)
            {
                _rootNodes.Remove(node);
            }
            else
            {
                oldParent.Children.Remove(node);
            }

            // 添加到新位置
            if (newParent == null)
            {
                _rootNodes.Add(node);
                node.Parent = null;
            }
            else
            {
                newParent.Children.Add(node);
                node.Parent = newParent;
                newParent.IsExpanded = true;
            }

            NodeMoved?.Invoke(this, new NodeEventArgs(node));
        }

        private void ExecuteCommand(ICommand command)
        {
            command.Execute();
            _undoStack.Push(command);
            _redoStack.Clear(); // 执行新命令后清空重做栈
        }

        private string GenerateUniqueName(string baseName, BaseNode? parent)
        {
            var siblings = parent?.Children ?? _rootNodes.Cast<BaseNode>();
            var existingNames = siblings.Select(n => n.Name).ToHashSet();

            if (!existingNames.Contains(baseName))
                return baseName;

            int counter = 1;
            string newName;
            do
            {
                newName = $"{baseName}({counter})";
                counter++;
            } while (existingNames.Contains(newName));

            return newName;
        }

        /// <summary>
        /// 为孙节点生成带4位随机数后缀的唯一名称
        /// </summary>
        private string GenerateUniqueNameWithSuffix(string baseName, BaseNode? parent)
        {
            var siblings = parent?.Children ?? _rootNodes.Cast<BaseNode>();
            var existingNames = siblings.Select(n => n.Name).ToHashSet();

            // 生成4位随机数
            var random = new Random();
            string newName;
            int attempts = 0;

            do
            {
                var suffix = random.Next(1000, 9999); // 生成1000-9999的4位数
                newName = $"{baseName}{suffix}";
                attempts++;

                // 防止无限循环，如果尝试100次还没找到唯一名称，则使用时间戳
                if (attempts > 100)
                {
                    var timestamp = DateTime.Now.ToString("HHmmss");
                    newName = $"{baseName}{timestamp}";
                    break;
                }
            } while (existingNames.Contains(newName));

            return newName;
        }

        private bool ContainsInvalidChars(string name)
        {
            char[] invalidChars = { '\\', '/', ':', '*', '?', '"', '<', '>', '|' };
            return name.IndexOfAny(invalidChars) >= 0;
        }

        private bool IsNameConflict(BaseNode node, string newName)
        {
            var siblings = node.Parent?.Children ?? _rootNodes.Cast<BaseNode>();
            return siblings.Any(n => n != node && n.Name.Equals(newName, StringComparison.OrdinalIgnoreCase));
        }

        private bool IsCircularReference(BaseNode node, BaseNode? newParent)
        {
            var current = newParent;
            while (current != null)
            {
                if (current == node) return true;
                current = current.Parent;
            }
            return false;
        }

        private void ExpandNodeRecursive(BaseNode node)
        {
            node.IsExpanded = true;
            foreach (var child in node.Children)
            {
                ExpandNodeRecursive(child);
            }
        }

        private void CollapseNodeRecursive(BaseNode node)
        {
            node.IsExpanded = false;
            foreach (var child in node.Children)
            {
                CollapseNodeRecursive(child);
            }
        }

        private void FindNodesRecursive(BaseNode node, string searchText, StringComparison comparison, List<BaseNode> results)
        {
            if (node.Name.Contains(searchText, comparison))
            {
                results.Add(node);
            }

            foreach (var child in node.Children)
            {
                FindNodesRecursive(child, searchText, comparison, results);
            }
        }

        /// <summary>
        /// 添加根节点
        /// </summary>
        public void AddRootNode(BaseNode node)
        {
            _rootNodes.Add(node);
            node.Parent = null;
            NodeCreated?.Invoke(this, new NodeEventArgs(node));
        }

        /// <summary>
        /// 添加子节点
        /// </summary>
        public void AddChildNode(BaseNode parent, BaseNode child)
        {
            parent.Children.Add(child);
            child.Parent = parent;
            parent.IsExpanded = true;
            NodeCreated?.Invoke(this, new NodeEventArgs(child));
        }

        /// <summary>
        /// 获取父节点
        /// </summary>
        public BaseNode? GetParentNode(BaseNode node)
        {
            return node.Parent;
        }

        /// <summary>
        /// 清空所有节点
        /// </summary>
        public void ClearAllNodes()
        {
            _rootNodes.Clear();
            _selectedNode = null;
        }
    }

    /// <summary>
    /// 节点事件参数
    /// </summary>
    public class NodeEventArgs : EventArgs
    {
        public BaseNode Node { get; }

        public NodeEventArgs(BaseNode node)
        {
            Node = node;
        }
    }

    /// <summary>
    /// 节点重命名事件参数
    /// </summary>
    public class NodeRenamedEventArgs : EventArgs
    {
        public BaseNode Node { get; }
        public string OldName { get; }
        public string NewName { get; }

        public NodeRenamedEventArgs(BaseNode node, string oldName, string newName)
        {
            Node = node;
            OldName = oldName;
            NewName = newName;
        }
    }
}
