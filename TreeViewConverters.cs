using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace 像素喵笔记
{
    /// <summary>
    /// 节点类型到Figma风格图标的转换器（支持展开状态）
    /// </summary>
    public class NodeTypeToIconConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 2 && values[0] is NodeType nodeType)
            {
                bool isExpanded = values[1] is bool expanded && expanded;

                // 使用Figma风格的emoji图标
                string iconText = nodeType switch
                {
                    NodeType.Notebook => "📓",    // 笔记本图标
                    NodeType.Collection => isExpanded ? "📂" : "📁",  // 展开时显示打开的文件夹
                    NodeType.Page => "📄",        // 页面图标
                    NodeType.CodePage => "💻",    // 代码页图标
                    _ => "📄"
                };

                return iconText;
            }

            return "📄";
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 简单的节点类型到图标转换器（不考虑展开状态）
    /// </summary>
    public class SimpleNodeTypeToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is NodeType nodeType)
            {
                // 使用Figma风格的emoji图标
                string iconText = nodeType switch
                {
                    NodeType.Notebook => "📓",    // 笔记本图标
                    NodeType.Collection => "📁",  // 文件夹图标
                    NodeType.Page => "📄",        // 页面图标
                    _ => "📄"
                };

                return iconText;
            }

            return "📄";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 节点状态到样式的转换器
    /// </summary>
    public class NodeStateToStyleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is NodeState state)
            {
                return state switch
                {
                    NodeState.Normal => Brushes.Black,
                    NodeState.Selected => Brushes.Black, // 选中状态也使用黑色字体，背景由TreeViewItem样式控制
                    NodeState.Cut => Brushes.Gray,
                    NodeState.Dragging => Brushes.LightGray,
                    NodeState.Editing => Brushes.DarkBlue,
                    _ => Brushes.Black
                };
            }

            return Brushes.Black;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 布尔值到可见性的转换器
    /// </summary>
    public class BooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? Visibility.Visible : Visibility.Collapsed;
            }

            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Visibility visibility)
            {
                return visibility == Visibility.Visible;
            }

            return false;
        }
    }

    /// <summary>
    /// 节点统计信息到字符串的转换器
    /// </summary>
    public class NodeStatisticsToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is BaseNode node)
            {
                var stats = node.GetStatistics();

                return node.NodeType switch
                {
                    NodeType.Notebook => $"集合: {stats.CollectionCount}, 页面: {stats.PageCount}, 字数: {stats.WordCount}",
                    NodeType.Collection => $"页面: {stats.PageCount}, 字数: {stats.WordCount}",
                    NodeType.Page => $"字数: {stats.WordCount}, 图片: {stats.ImageCount}",
                    _ => ""
                };
            }

            return "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 多值转换器：根据节点类型和父节点确定是否可以执行操作
    /// </summary>
    public class NodeOperationEnabledConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length >= 2 && values[0] is NodeType nodeType && parameter is string operation)
            {
                return operation switch
                {
                    "CreateCollection" => nodeType == NodeType.Notebook,
                    "CreatePage" => nodeType == NodeType.Collection,
                    "Delete" => true, // 所有节点都可以删除
                    "Rename" => true, // 所有节点都可以重命名
                    "Cut" => true,    // 所有节点都可以剪切
                    "Copy" => true,   // 所有节点都可以复制
                    _ => false
                };
            }

            return false;
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 节点深度到缩进的转换器
    /// </summary>
    public class NodeDepthToIndentConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is BaseNode node)
            {
                int depth = GetNodeDepth(node);
                return new Thickness(depth * 20, 0, 0, 0); // 每级缩进20像素
            }

            return new Thickness(0);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private int GetNodeDepth(BaseNode node)
        {
            int depth = 0;
            var current = node.Parent;
            while (current != null)
            {
                depth++;
                current = current.Parent;
            }
            return depth;
        }
    }

    /// <summary>
    /// 时间到友好字符串的转换器
    /// </summary>
    public class DateTimeToFriendlyStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DateTime dateTime)
            {
                var now = DateTime.Now;
                var timeSpan = now - dateTime;

                if (timeSpan.TotalMinutes < 1)
                    return "刚刚";
                else if (timeSpan.TotalMinutes < 60)
                    return $"{(int)timeSpan.TotalMinutes}分钟前";
                else if (timeSpan.TotalHours < 24)
                    return $"{(int)timeSpan.TotalHours}小时前";
                else if (timeSpan.TotalDays < 7)
                    return $"{(int)timeSpan.TotalDays}天前";
                else if (dateTime.Year == now.Year)
                    return dateTime.ToString("MM月dd日");
                else
                    return dateTime.ToString("yyyy年MM月dd日");
            }

            return "";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 文件大小到友好字符串的转换器
    /// </summary>
    public class FileSizeToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is long size)
            {
                string[] units = { "B", "KB", "MB", "GB", "TB" };
                double fileSize = size;
                int unitIndex = 0;

                while (fileSize >= 1024 && unitIndex < units.Length - 1)
                {
                    fileSize /= 1024;
                    unitIndex++;
                }

                return $"{fileSize:F1} {units[unitIndex]}";
            }

            return "0 B";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
