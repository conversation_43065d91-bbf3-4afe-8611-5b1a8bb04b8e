﻿#pragma checksum "..\..\..\..\AdvancedSmoothingDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C478279F83D4EDE1A17D8F1696C055139FEEDA12"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// AdvancedSmoothingDialog
    /// </summary>
    public partial class AdvancedSmoothingDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 63 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider antiShakeRadiusSlider;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock antiShakeRadiusText;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider stabilizerDelaySlider;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock stabilizerDelayText;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider flowRateSlider;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock flowRateText;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider bufferSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock bufferSizeText;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider minDistanceSlider;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock minDistanceText;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\AdvancedSmoothingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox enablePredictionCheckBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/advancedsmoothingdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.antiShakeRadiusSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 68 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            this.antiShakeRadiusSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.AntiShakeRadiusSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.antiShakeRadiusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.stabilizerDelaySlider = ((System.Windows.Controls.Slider)(target));
            
            #line 101 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            this.stabilizerDelaySlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.StabilizerDelaySlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.stabilizerDelayText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.flowRateSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 134 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            this.flowRateSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.FlowRateSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.flowRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.bufferSizeSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 168 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            this.bufferSizeSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.BufferSizeSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.bufferSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.minDistanceSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 191 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            this.minDistanceSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.MinDistanceSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.minDistanceText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.enablePredictionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 205 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            this.enablePredictionCheckBox.Checked += new System.Windows.RoutedEventHandler(this.EnablePredictionCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 206 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            this.enablePredictionCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.EnablePredictionCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 224 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PresetLight_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 230 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PresetMedium_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 236 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PresetHeavy_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 241 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PresetProfessional_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 257 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetDefaults_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 263 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 269 "..\..\..\..\AdvancedSmoothingDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OK_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

