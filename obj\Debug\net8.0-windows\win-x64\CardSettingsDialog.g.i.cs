﻿#pragma checksum "..\..\..\..\CardSettingsDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BAFDCF076291548FD7F4A6A0C67FB9717692D321"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// CardSettingsDialog
    /// </summary>
    public partial class CardSettingsDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 40 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle backgroundColorPreview;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBorderColor;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle borderColorPreview;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbBorderThickness;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtWidth;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtHeight;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbFontFamily;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbFontSize;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTextColor;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle textColorPreview;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\CardSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnApply;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/cardsettingsdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\CardSettingsDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\..\CardSettingsDialog.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\CardSettingsDialog.xaml"
            this.btnBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.BtnBackgroundColor_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.backgroundColorPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 4:
            this.btnBorderColor = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\CardSettingsDialog.xaml"
            this.btnBorderColor.Click += new System.Windows.RoutedEventHandler(this.BtnBorderColor_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.borderColorPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 6:
            this.cmbBorderThickness = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.txtWidth = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.txtHeight = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.cmbFontFamily = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.cmbFontSize = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.btnTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 150 "..\..\..\..\CardSettingsDialog.xaml"
            this.btnTextColor.Click += new System.Windows.RoutedEventHandler(this.BtnTextColor_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.textColorPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 13:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\CardSettingsDialog.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.btnApply = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\..\CardSettingsDialog.xaml"
            this.btnApply.Click += new System.Windows.RoutedEventHandler(this.BtnApply_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

