﻿#pragma checksum "..\..\..\..\ColorPickerDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7EAF5C7A5B011FDAC31798636BB05D74535FEEFC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using 像素喵笔记;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// ColorPickerDialog
    /// </summary>
    public partial class ColorPickerDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 48 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas mainColorCanvas;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse colorSelector;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas hueCanvas;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle hueSelector;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtR;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtG;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtB;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtHex;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle colorPreview;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\ColorPickerDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOK;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/colorpickerdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\ColorPickerDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\ColorPickerDialog.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.btnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 58 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 59 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 60 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 61 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 62 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 63 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 64 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 65 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 68 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 69 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 70 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 71 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 72 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 73 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 74 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 75 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 78 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 79 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 80 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 81 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 82 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 83 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 84 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 85 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 88 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            
            #line 89 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            
            #line 90 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 91 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 92 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 93 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            
            #line 94 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            
            #line 95 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            
            #line 98 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            
            #line 99 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            
            #line 100 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            
            #line 101 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            
            #line 102 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 39:
            
            #line 103 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            
            #line 104 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            
            #line 105 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            
            #line 108 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            
            #line 109 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            
            #line 110 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            
            #line 111 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            
            #line 112 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            
            #line 113 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            
            #line 114 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 49:
            
            #line 115 "..\..\..\..\ColorPickerDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 50:
            this.mainColorCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 132 "..\..\..\..\ColorPickerDialog.xaml"
            this.mainColorCanvas.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.MainColorCanvas_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 133 "..\..\..\..\ColorPickerDialog.xaml"
            this.mainColorCanvas.MouseMove += new System.Windows.Input.MouseEventHandler(this.MainColorCanvas_MouseMove);
            
            #line default
            #line hidden
            
            #line 134 "..\..\..\..\ColorPickerDialog.xaml"
            this.mainColorCanvas.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.MainColorCanvas_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 51:
            this.colorSelector = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 52:
            this.hueCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 143 "..\..\..\..\ColorPickerDialog.xaml"
            this.hueCanvas.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.HueCanvas_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 144 "..\..\..\..\ColorPickerDialog.xaml"
            this.hueCanvas.MouseMove += new System.Windows.Input.MouseEventHandler(this.HueCanvas_MouseMove);
            
            #line default
            #line hidden
            
            #line 145 "..\..\..\..\ColorPickerDialog.xaml"
            this.hueCanvas.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.HueCanvas_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 53:
            this.hueSelector = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 54:
            this.txtR = ((System.Windows.Controls.TextBox)(target));
            
            #line 171 "..\..\..\..\ColorPickerDialog.xaml"
            this.txtR.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.NumberValidation);
            
            #line default
            #line hidden
            
            #line 171 "..\..\..\..\ColorPickerDialog.xaml"
            this.txtR.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.RgbTextChanged);
            
            #line default
            #line hidden
            return;
            case 55:
            this.txtG = ((System.Windows.Controls.TextBox)(target));
            
            #line 173 "..\..\..\..\ColorPickerDialog.xaml"
            this.txtG.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.NumberValidation);
            
            #line default
            #line hidden
            
            #line 173 "..\..\..\..\ColorPickerDialog.xaml"
            this.txtG.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.RgbTextChanged);
            
            #line default
            #line hidden
            return;
            case 56:
            this.txtB = ((System.Windows.Controls.TextBox)(target));
            
            #line 175 "..\..\..\..\ColorPickerDialog.xaml"
            this.txtB.PreviewTextInput += new System.Windows.Input.TextCompositionEventHandler(this.NumberValidation);
            
            #line default
            #line hidden
            
            #line 175 "..\..\..\..\ColorPickerDialog.xaml"
            this.txtB.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.RgbTextChanged);
            
            #line default
            #line hidden
            return;
            case 57:
            this.txtHex = ((System.Windows.Controls.TextBox)(target));
            
            #line 177 "..\..\..\..\ColorPickerDialog.xaml"
            this.txtHex.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.HexTextChanged);
            
            #line default
            #line hidden
            return;
            case 58:
            this.colorPreview = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 59:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 193 "..\..\..\..\ColorPickerDialog.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.btnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 60:
            this.btnOK = ((System.Windows.Controls.Button)(target));
            
            #line 194 "..\..\..\..\ColorPickerDialog.xaml"
            this.btnOK.Click += new System.Windows.RoutedEventHandler(this.btnOK_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

