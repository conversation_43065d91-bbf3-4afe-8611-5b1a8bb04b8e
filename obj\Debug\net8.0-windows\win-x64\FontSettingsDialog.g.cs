﻿#pragma checksum "..\..\..\..\FontSettingsDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B6268861E0AE6B42632C546DB6BD218763C90A55"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using 像素喵笔记;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// FontSettingsDialog
    /// </summary>
    public partial class FontSettingsDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 51 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbFontFamily;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbFontSize;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkBold;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkItalic;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkUnderline;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkStrikethrough;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbLineSpacing;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbParagraphBefore;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbParagraphAfter;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton rbLeft;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton rbCenter;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton rbRight;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton rbJustify;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle rectTextColor;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle rectBackColor;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtOriginalPreview;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPreview;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        
        #line 312 "..\..\..\..\FontSettingsDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnApply;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/fontsettingsdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\FontSettingsDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\..\FontSettingsDialog.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.btnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.cmbFontFamily = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.cmbFontSize = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.chkBold = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 5:
            this.chkItalic = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.chkUnderline = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 7:
            this.chkStrikethrough = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 8:
            this.cmbLineSpacing = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.cmbParagraphBefore = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.cmbParagraphAfter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.rbLeft = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 12:
            this.rbCenter = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 13:
            this.rbRight = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 14:
            this.rbJustify = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 15:
            
            #line 208 "..\..\..\..\FontSettingsDialog.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.rectTextColor_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.rectTextColor = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 17:
            
            #line 234 "..\..\..\..\FontSettingsDialog.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.rectBackColor_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.rectBackColor = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 19:
            this.txtOriginalPreview = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.txtPreview = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 311 "..\..\..\..\FontSettingsDialog.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.btnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.btnApply = ((System.Windows.Controls.Button)(target));
            
            #line 312 "..\..\..\..\FontSettingsDialog.xaml"
            this.btnApply.Click += new System.Windows.RoutedEventHandler(this.btnApply_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

