﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E783171D322C0AC8620A67E9093B29F41CE4BFE1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using 像素喵笔记;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 106 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainGrid;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition treeColumn;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition contentColumn;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border treeContainer;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView notesTreeView;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid mainContentGrid;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border topToolbar;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSettings;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnToggleTree;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnFavorites;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock favoritesIcon;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnUndo;
        
        #line default
        #line hidden
        
        
        #line 289 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRedo;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnFontSettings;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnInsertSymbol;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnInsertNumbering;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnImportFiles;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnInsertTable;
        
        #line default
        #line hidden
        
        
        #line 402 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnMinimize;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnMaximize;
        
        #line default
        #line hidden
        
        
        #line 459 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        
        #line 467 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid mainContentArea;
        
        #line default
        #line hidden
        
        
        #line 475 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border favoritesPopup;
        
        #line default
        #line hidden
        
        
        #line 496 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCloseFavorites;
        
        #line default
        #line hidden
        
        
        #line 506 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox favoritesList;
        
        #line default
        #line hidden
        
        
        #line 560 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid editorContainer;
        
        #line default
        #line hidden
        
        
        #line 562 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RichTextBox richTextEditor;
        
        #line default
        #line hidden
        
        
        #line 584 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContextMenu richTextContextMenu;
        
        #line default
        #line hidden
        
        
        #line 608 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas floatingCardCanvas;
        
        #line default
        #line hidden
        
        
        #line 612 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer mediaScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 613 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid mediaContainer;
        
        #line default
        #line hidden
        
        
        #line 621 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock mediaTitleText;
        
        #line default
        #line hidden
        
        
        #line 623 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock mediaSubtitleText;
        
        #line default
        #line hidden
        
        
        #line 628 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel mediaWrapPanel;
        
        #line default
        #line hidden
        
        
        #line 637 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid codeEditorContainer;
        
        #line default
        #line hidden
        
        
        #line 649 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox languageComboBox;
        
        #line default
        #line hidden
        
        
        #line 656 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRunPython;
        
        #line default
        #line hidden
        
        
        #line 660 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPreviewHtml;
        
        #line default
        #line hidden
        
        
        #line 680 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer lineNumberScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 684 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lineNumbersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 695 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RichTextBox codeRichTextBox;
        
        #line default
        #line hidden
        
        
        #line 719 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock statusLineCount;
        
        #line default
        #line hidden
        
        
        #line 720 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock statusCharCount;
        
        #line default
        #line hidden
        
        
        #line 721 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock statusLanguage;
        
        #line default
        #line hidden
        
        
        #line 727 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid welcomeContainer;
        
        #line default
        #line hidden
        
        
        #line 753 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.StatusBarItem statusBarItem;
        
        #line default
        #line hidden
        
        
        #line 757 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.StatusBarItem wordCountItem;
        
        #line default
        #line hidden
        
        
        #line 761 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.StatusBarItem imageCountItem;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 13 "..\..\..\..\MainWindow.xaml"
            ((像素喵笔记.MainWindow)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.Window_Closing);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MainGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.treeColumn = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 4:
            this.contentColumn = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 5:
            this.treeContainer = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            
            #line 136 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleArea_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 137 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseMove += new System.Windows.Input.MouseEventHandler(this.TitleArea_MouseMove);
            
            #line default
            #line hidden
            
            #line 138 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.TitleArea_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 7:
            this.notesTreeView = ((System.Windows.Controls.TreeView)(target));
            
            #line 176 "..\..\..\..\MainWindow.xaml"
            this.notesTreeView.MouseRightButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.notesTreeView_MouseRightButtonUp);
            
            #line default
            #line hidden
            
            #line 177 "..\..\..\..\MainWindow.xaml"
            this.notesTreeView.SelectedItemChanged += new System.Windows.RoutedPropertyChangedEventHandler<object>(this.notesTreeView_SelectedItemChanged);
            
            #line default
            #line hidden
            
            #line 179 "..\..\..\..\MainWindow.xaml"
            this.notesTreeView.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.notesTreeView_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 180 "..\..\..\..\MainWindow.xaml"
            this.notesTreeView.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.notesTreeView_MouseLeftButtonUp);
            
            #line default
            #line hidden
            
            #line 181 "..\..\..\..\MainWindow.xaml"
            this.notesTreeView.PreviewMouseMove += new System.Windows.Input.MouseEventHandler(this.notesTreeView_PreviewMouseMove);
            
            #line default
            #line hidden
            
            #line 182 "..\..\..\..\MainWindow.xaml"
            this.notesTreeView.PreviewDragOver += new System.Windows.DragEventHandler(this.notesTreeView_PreviewDragOver);
            
            #line default
            #line hidden
            
            #line 183 "..\..\..\..\MainWindow.xaml"
            this.notesTreeView.Drop += new System.Windows.DragEventHandler(this.notesTreeView_Drop);
            
            #line default
            #line hidden
            return;
            case 8:
            this.mainContentGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 9:
            this.topToolbar = ((System.Windows.Controls.Border)(target));
            
            #line 198 "..\..\..\..\MainWindow.xaml"
            this.topToolbar.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnSettings = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\..\MainWindow.xaml"
            this.btnSettings.Click += new System.Windows.RoutedEventHandler(this.BtnSettings_Click);
            
            #line default
            #line hidden
            
            #line 207 "..\..\..\..\MainWindow.xaml"
            this.btnSettings.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnToggleTree = ((System.Windows.Controls.Button)(target));
            
            #line 222 "..\..\..\..\MainWindow.xaml"
            this.btnToggleTree.Click += new System.Windows.RoutedEventHandler(this.BtnToggleTree_Click);
            
            #line default
            #line hidden
            
            #line 223 "..\..\..\..\MainWindow.xaml"
            this.btnToggleTree.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 12:
            this.btnFavorites = ((System.Windows.Controls.Button)(target));
            
            #line 238 "..\..\..\..\MainWindow.xaml"
            this.btnFavorites.Click += new System.Windows.RoutedEventHandler(this.BtnFavorites_Click);
            
            #line default
            #line hidden
            
            #line 239 "..\..\..\..\MainWindow.xaml"
            this.btnFavorites.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 13:
            this.favoritesIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.btnUndo = ((System.Windows.Controls.Button)(target));
            
            #line 274 "..\..\..\..\MainWindow.xaml"
            this.btnUndo.Click += new System.Windows.RoutedEventHandler(this.BtnUndo_Click);
            
            #line default
            #line hidden
            
            #line 275 "..\..\..\..\MainWindow.xaml"
            this.btnUndo.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 15:
            this.btnRedo = ((System.Windows.Controls.Button)(target));
            
            #line 289 "..\..\..\..\MainWindow.xaml"
            this.btnRedo.Click += new System.Windows.RoutedEventHandler(this.BtnRedo_Click);
            
            #line default
            #line hidden
            
            #line 290 "..\..\..\..\MainWindow.xaml"
            this.btnRedo.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 16:
            this.btnFontSettings = ((System.Windows.Controls.Button)(target));
            
            #line 307 "..\..\..\..\MainWindow.xaml"
            this.btnFontSettings.Click += new System.Windows.RoutedEventHandler(this.btnFontSettings_Click);
            
            #line default
            #line hidden
            
            #line 308 "..\..\..\..\MainWindow.xaml"
            this.btnFontSettings.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 17:
            this.btnInsertSymbol = ((System.Windows.Controls.Button)(target));
            
            #line 326 "..\..\..\..\MainWindow.xaml"
            this.btnInsertSymbol.Click += new System.Windows.RoutedEventHandler(this.BtnInsertSymbol_Click);
            
            #line default
            #line hidden
            
            #line 327 "..\..\..\..\MainWindow.xaml"
            this.btnInsertSymbol.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 18:
            this.btnInsertNumbering = ((System.Windows.Controls.Button)(target));
            
            #line 341 "..\..\..\..\MainWindow.xaml"
            this.btnInsertNumbering.Click += new System.Windows.RoutedEventHandler(this.BtnInsertNumbering_Click);
            
            #line default
            #line hidden
            
            #line 342 "..\..\..\..\MainWindow.xaml"
            this.btnInsertNumbering.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 19:
            this.btnImportFiles = ((System.Windows.Controls.Button)(target));
            
            #line 360 "..\..\..\..\MainWindow.xaml"
            this.btnImportFiles.Click += new System.Windows.RoutedEventHandler(this.BtnImportFiles_Click);
            
            #line default
            #line hidden
            
            #line 361 "..\..\..\..\MainWindow.xaml"
            this.btnImportFiles.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 20:
            this.btnInsertTable = ((System.Windows.Controls.Button)(target));
            
            #line 378 "..\..\..\..\MainWindow.xaml"
            this.btnInsertTable.Click += new System.Windows.RoutedEventHandler(this.BtnInsertTable_Click);
            
            #line default
            #line hidden
            
            #line 379 "..\..\..\..\MainWindow.xaml"
            this.btnInsertTable.PreviewMouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Button_PreviewMouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 21:
            this.btnMinimize = ((System.Windows.Controls.Button)(target));
            
            #line 402 "..\..\..\..\MainWindow.xaml"
            this.btnMinimize.Click += new System.Windows.RoutedEventHandler(this.BtnMinimize_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.btnMaximize = ((System.Windows.Controls.Button)(target));
            
            #line 430 "..\..\..\..\MainWindow.xaml"
            this.btnMaximize.Click += new System.Windows.RoutedEventHandler(this.BtnMaximize_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 459 "..\..\..\..\MainWindow.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.mainContentArea = ((System.Windows.Controls.Grid)(target));
            return;
            case 25:
            this.favoritesPopup = ((System.Windows.Controls.Border)(target));
            return;
            case 26:
            this.btnCloseFavorites = ((System.Windows.Controls.Button)(target));
            
            #line 496 "..\..\..\..\MainWindow.xaml"
            this.btnCloseFavorites.Click += new System.Windows.RoutedEventHandler(this.BtnCloseFavorites_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.favoritesList = ((System.Windows.Controls.ListBox)(target));
            
            #line 508 "..\..\..\..\MainWindow.xaml"
            this.favoritesList.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FavoritesList_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 29:
            this.editorContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 30:
            this.richTextEditor = ((System.Windows.Controls.RichTextBox)(target));
            
            #line 574 "..\..\..\..\MainWindow.xaml"
            this.richTextEditor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.RichTextEditor_TextChanged);
            
            #line default
            #line hidden
            
            #line 575 "..\..\..\..\MainWindow.xaml"
            this.richTextEditor.SelectionChanged += new System.Windows.RoutedEventHandler(this.RichTextEditor_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 576 "..\..\..\..\MainWindow.xaml"
            this.richTextEditor.PreviewDragOver += new System.Windows.DragEventHandler(this.RichTextEditor_PreviewDragOver);
            
            #line default
            #line hidden
            
            #line 577 "..\..\..\..\MainWindow.xaml"
            this.richTextEditor.Drop += new System.Windows.DragEventHandler(this.RichTextEditor_Drop);
            
            #line default
            #line hidden
            
            #line 578 "..\..\..\..\MainWindow.xaml"
            this.richTextEditor.PreviewKeyDown += new System.Windows.Input.KeyEventHandler(this.RichTextEditor_PreviewKeyDown);
            
            #line default
            #line hidden
            return;
            case 31:
            this.richTextContextMenu = ((System.Windows.Controls.ContextMenu)(target));
            return;
            case 32:
            
            #line 598 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ContextMenu_InsertImage_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            
            #line 599 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ContextMenu_InsertTable_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            
            #line 600 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ContextMenu_InsertSymbol_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            
            #line 602 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ContextMenu_FontSettings_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.floatingCardCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 37:
            this.mediaScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 38:
            this.mediaContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 39:
            this.mediaTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.mediaSubtitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.mediaWrapPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 42:
            this.codeEditorContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 43:
            this.languageComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 649 "..\..\..\..\MainWindow.xaml"
            this.languageComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LanguageComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 44:
            this.btnRunPython = ((System.Windows.Controls.Button)(target));
            
            #line 658 "..\..\..\..\MainWindow.xaml"
            this.btnRunPython.Click += new System.Windows.RoutedEventHandler(this.BtnRunPython_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.btnPreviewHtml = ((System.Windows.Controls.Button)(target));
            
            #line 662 "..\..\..\..\MainWindow.xaml"
            this.btnPreviewHtml.Click += new System.Windows.RoutedEventHandler(this.BtnPreviewHtml_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            this.lineNumberScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 47:
            this.lineNumbersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 48:
            this.codeRichTextBox = ((System.Windows.Controls.RichTextBox)(target));
            
            #line 705 "..\..\..\..\MainWindow.xaml"
            this.codeRichTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CodeRichTextBox_TextChanged);
            
            #line default
            #line hidden
            
            #line 706 "..\..\..\..\MainWindow.xaml"
            this.codeRichTextBox.AddHandler(System.Windows.Controls.ScrollViewer.ScrollChangedEvent, new System.Windows.Controls.ScrollChangedEventHandler(this.CodeRichTextBox_ScrollChanged));
            
            #line default
            #line hidden
            return;
            case 49:
            this.statusLineCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.statusCharCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 51:
            this.statusLanguage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.welcomeContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 53:
            this.statusBarItem = ((System.Windows.Controls.Primitives.StatusBarItem)(target));
            return;
            case 54:
            this.wordCountItem = ((System.Windows.Controls.Primitives.StatusBarItem)(target));
            return;
            case 55:
            this.imageCountItem = ((System.Windows.Controls.Primitives.StatusBarItem)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 28:
            
            #line 527 "..\..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveFavorite_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

