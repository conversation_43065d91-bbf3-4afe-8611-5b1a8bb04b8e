﻿#pragma checksum "..\..\..\..\NodePropertiesDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C82DDB8B48029BC075C2ECB81651EC4FC924FB52"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using 像素喵笔记;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// NodePropertiesDialog
    /// </summary>
    public partial class NodePropertiesDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 193 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock titleText;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock nameValue;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock typeValue;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock pathValue;
        
        #line default
        #line hidden
        
        
        #line 258 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock parentLabel;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock parentValue;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock wordCountValue;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock imageCountValue;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock videoCountValue;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock tableCountValue;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock createdTimeValue;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock modifiedTimeValue;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock childrenLabel;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock childrenCountValue;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock depthLabel;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock depthValue;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\..\NodePropertiesDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOK;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/nodepropertiesdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\NodePropertiesDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 190 "..\..\..\..\NodePropertiesDialog.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.titleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.nameValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.typeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.pathValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.parentLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.parentValue = ((System.Windows.Controls.TextBlock)(target));
            
            #line 259 "..\..\..\..\NodePropertiesDialog.xaml"
            this.parentValue.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.ParentValue_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.wordCountValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.imageCountValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.videoCountValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.tableCountValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.createdTimeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.modifiedTimeValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.childrenLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.childrenCountValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.depthLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.depthValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.btnOK = ((System.Windows.Controls.Button)(target));
            
            #line 314 "..\..\..\..\NodePropertiesDialog.xaml"
            this.btnOK.Click += new System.Windows.RoutedEventHandler(this.BtnOK_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

