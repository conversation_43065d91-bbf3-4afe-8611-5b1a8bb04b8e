﻿#pragma checksum "..\..\..\..\NumberingDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D5056CB5FCD52C10CA3F5B9A869FE584A2B3D2F0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// NumberingDialog
    /// </summary>
    public partial class NumberingDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnNumberingTab;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBulletsTab;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnMultiLevelTab;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid contentGrid;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel numberingPanel;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel bulletsPanel;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel multiLevelPanel;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\NumberingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOK;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/numberingdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\NumberingDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 46 "..\..\..\..\NumberingDialog.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnNumberingTab = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\NumberingDialog.xaml"
            this.btnNumberingTab.Click += new System.Windows.RoutedEventHandler(this.BtnNumberingTab_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnBulletsTab = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\NumberingDialog.xaml"
            this.btnBulletsTab.Click += new System.Windows.RoutedEventHandler(this.BtnBulletsTab_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.btnMultiLevelTab = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\NumberingDialog.xaml"
            this.btnMultiLevelTab.Click += new System.Windows.RoutedEventHandler(this.BtnMultiLevelTab_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.contentGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.numberingPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 7:
            this.bulletsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 8:
            this.multiLevelPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 9:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 79 "..\..\..\..\NumberingDialog.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnOK = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\..\NumberingDialog.xaml"
            this.btnOK.Click += new System.Windows.RoutedEventHandler(this.BtnOK_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

