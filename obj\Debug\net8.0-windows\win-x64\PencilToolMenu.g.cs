﻿#pragma checksum "..\..\..\..\PencilToolMenu.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CFB65B5EF92B58F809305D4404095AB8BC34118B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using 像素喵笔记;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// PencilToolMenu
    /// </summary>
    public partial class PencilToolMenu : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 32 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPencil;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnMarker;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnEraser;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider brushSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock brushSizeText;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox smoothingModeCombo;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider smoothingSlider;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock smoothingText;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAdvancedSmoothing;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\PencilToolMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnColorPicker;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/penciltoolmenu.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\PencilToolMenu.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnPencil = ((System.Windows.Controls.Button)(target));
            
            #line 36 "..\..\..\..\PencilToolMenu.xaml"
            this.btnPencil.Click += new System.Windows.RoutedEventHandler(this.BtnPencil_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnMarker = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\PencilToolMenu.xaml"
            this.btnMarker.Click += new System.Windows.RoutedEventHandler(this.BtnMarker_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnEraser = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\PencilToolMenu.xaml"
            this.btnEraser.Click += new System.Windows.RoutedEventHandler(this.BtnEraser_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.brushSizeSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 97 "..\..\..\..\PencilToolMenu.xaml"
            this.brushSizeSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.BrushSizeSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.brushSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.smoothingModeCombo = ((System.Windows.Controls.ComboBox)(target));
            
            #line 118 "..\..\..\..\PencilToolMenu.xaml"
            this.smoothingModeCombo.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SmoothingModeCombo_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.smoothingSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 140 "..\..\..\..\PencilToolMenu.xaml"
            this.smoothingSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.SmoothingSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.smoothingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.btnAdvancedSmoothing = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\..\..\PencilToolMenu.xaml"
            this.btnAdvancedSmoothing.Click += new System.Windows.RoutedEventHandler(this.BtnAdvancedSmoothing_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnColorPicker = ((System.Windows.Controls.Button)(target));
            
            #line 182 "..\..\..\..\PencilToolMenu.xaml"
            this.btnColorPicker.Click += new System.Windows.RoutedEventHandler(this.BtnColorPicker_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 196 "..\..\..\..\PencilToolMenu.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 207 "..\..\..\..\PencilToolMenu.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 218 "..\..\..\..\PencilToolMenu.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 229 "..\..\..\..\PencilToolMenu.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 239 "..\..\..\..\PencilToolMenu.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 252 "..\..\..\..\PencilToolMenu.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CustomColor_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

