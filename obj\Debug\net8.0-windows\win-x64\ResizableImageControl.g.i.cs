﻿#pragma checksum "..\..\..\..\ResizableImageControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "129B8B43265146B54498B1BE8676ACEDFE30C855"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// ResizableImageControl
    /// </summary>
    public partial class ResizableImageControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 17 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid mainGrid;
        
        #line default
        #line hidden
        
        
        #line 19 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border imageBorder;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image imageControl;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid resizeHandles;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle bottomRightHandle;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle rightHandle;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle bottomHandle;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel editModePanel;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnConfirm;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\ResizableImageControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/resizableimagecontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\ResizableImageControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.mainGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 2:
            this.imageBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.imageControl = ((System.Windows.Controls.Image)(target));
            
            #line 27 "..\..\..\..\ResizableImageControl.xaml"
            this.imageControl.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Image_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 4:
            this.resizeHandles = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.bottomRightHandle = ((System.Windows.Shapes.Rectangle)(target));
            
            #line 42 "..\..\..\..\ResizableImageControl.xaml"
            this.bottomRightHandle.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.BottomRightHandle_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 43 "..\..\..\..\ResizableImageControl.xaml"
            this.bottomRightHandle.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.Handle_MouseLeftButtonUp);
            
            #line default
            #line hidden
            
            #line 44 "..\..\..\..\ResizableImageControl.xaml"
            this.bottomRightHandle.MouseMove += new System.Windows.Input.MouseEventHandler(this.BottomRightHandle_MouseMove);
            
            #line default
            #line hidden
            return;
            case 6:
            this.rightHandle = ((System.Windows.Shapes.Rectangle)(target));
            
            #line 60 "..\..\..\..\ResizableImageControl.xaml"
            this.rightHandle.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.RightHandle_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 61 "..\..\..\..\ResizableImageControl.xaml"
            this.rightHandle.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.Handle_MouseLeftButtonUp);
            
            #line default
            #line hidden
            
            #line 62 "..\..\..\..\ResizableImageControl.xaml"
            this.rightHandle.MouseMove += new System.Windows.Input.MouseEventHandler(this.RightHandle_MouseMove);
            
            #line default
            #line hidden
            return;
            case 7:
            this.bottomHandle = ((System.Windows.Shapes.Rectangle)(target));
            
            #line 78 "..\..\..\..\ResizableImageControl.xaml"
            this.bottomHandle.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.BottomHandle_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 79 "..\..\..\..\ResizableImageControl.xaml"
            this.bottomHandle.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.Handle_MouseLeftButtonUp);
            
            #line default
            #line hidden
            
            #line 80 "..\..\..\..\ResizableImageControl.xaml"
            this.bottomHandle.MouseMove += new System.Windows.Input.MouseEventHandler(this.BottomHandle_MouseMove);
            
            #line default
            #line hidden
            return;
            case 8:
            this.editModePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.btnConfirm = ((System.Windows.Controls.Button)(target));
            
            #line 101 "..\..\..\..\ResizableImageControl.xaml"
            this.btnConfirm.Click += new System.Windows.RoutedEventHandler(this.BtnConfirm_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 124 "..\..\..\..\ResizableImageControl.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

