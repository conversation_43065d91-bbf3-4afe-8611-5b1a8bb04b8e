﻿#pragma checksum "..\..\..\..\ShapesSidebar.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "299828B7E6C0DEA45627F4475956501CE497BE77"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using 像素喵笔记;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// ShapesSidebar
    /// </summary>
    public partial class ShapesSidebar : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 68 "..\..\..\..\ShapesSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox searchTextBox;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\ShapesSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Expander recentsExpander;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\ShapesSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Expander connectionsExpander;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\ShapesSidebar.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Expander basicExpander;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/shapessidebar.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\ShapesSidebar.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.searchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 74 "..\..\..\..\ShapesSidebar.xaml"
            this.searchTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 75 "..\..\..\..\ShapesSidebar.xaml"
            this.searchTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.SearchTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 2:
            this.recentsExpander = ((System.Windows.Controls.Expander)(target));
            return;
            case 3:
            
            #line 92 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.connectionsExpander = ((System.Windows.Controls.Expander)(target));
            return;
            case 5:
            
            #line 108 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 118 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.basicExpander = ((System.Windows.Controls.Expander)(target));
            return;
            case 8:
            
            #line 136 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 143 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 150 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 160 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 170 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 180 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 187 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 197 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 207 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 216 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 225 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 234 "..\..\..\..\ShapesSidebar.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShapeButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

