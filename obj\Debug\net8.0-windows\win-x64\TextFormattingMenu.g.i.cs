﻿#pragma checksum "..\..\..\..\TextFormattingMenu.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5298F8595DDF434281EDBBEE0850189D6FDEA093"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace 像素喵笔记 {
    
    
    /// <summary>
    /// TextFormattingMenu
    /// </summary>
    public partial class TextFormattingMenu : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 234 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border mainBorder;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox fontFamilyComboBox;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox fontSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button fontColorButton;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle colorIndicator;
        
        #line default
        #line hidden
        
        
        #line 312 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton boldToggle;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton italicToggle;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton underlineToggle;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton strikethroughToggle;
        
        #line default
        #line hidden
        
        
        #line 374 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button alignLeftButton;
        
        #line default
        #line hidden
        
        
        #line 389 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button alignCenterButton;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button alignRightButton;
        
        #line default
        #line hidden
        
        
        #line 421 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button cancelButton;
        
        #line default
        #line hidden
        
        
        #line 436 "..\..\..\..\TextFormattingMenu.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button confirmButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/像素喵笔记;V1.0.0.0;component/textformattingmenu.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\TextFormattingMenu.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.mainBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 2:
            this.fontFamilyComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 252 "..\..\..\..\TextFormattingMenu.xaml"
            this.fontFamilyComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FontFamily_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.fontSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 266 "..\..\..\..\TextFormattingMenu.xaml"
            this.fontSizeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FontSize_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.fontColorButton = ((System.Windows.Controls.Button)(target));
            
            #line 291 "..\..\..\..\TextFormattingMenu.xaml"
            this.fontColorButton.Click += new System.Windows.RoutedEventHandler(this.FontColor_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.colorIndicator = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 6:
            this.boldToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 315 "..\..\..\..\TextFormattingMenu.xaml"
            this.boldToggle.Click += new System.Windows.RoutedEventHandler(this.Bold_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.italicToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 329 "..\..\..\..\TextFormattingMenu.xaml"
            this.italicToggle.Click += new System.Windows.RoutedEventHandler(this.Italic_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.underlineToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 344 "..\..\..\..\TextFormattingMenu.xaml"
            this.underlineToggle.Click += new System.Windows.RoutedEventHandler(this.Underline_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.strikethroughToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 359 "..\..\..\..\TextFormattingMenu.xaml"
            this.strikethroughToggle.Click += new System.Windows.RoutedEventHandler(this.Strikethrough_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.alignLeftButton = ((System.Windows.Controls.Button)(target));
            
            #line 377 "..\..\..\..\TextFormattingMenu.xaml"
            this.alignLeftButton.Click += new System.Windows.RoutedEventHandler(this.AlignLeft_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.alignCenterButton = ((System.Windows.Controls.Button)(target));
            
            #line 392 "..\..\..\..\TextFormattingMenu.xaml"
            this.alignCenterButton.Click += new System.Windows.RoutedEventHandler(this.AlignCenter_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.alignRightButton = ((System.Windows.Controls.Button)(target));
            
            #line 406 "..\..\..\..\TextFormattingMenu.xaml"
            this.alignRightButton.Click += new System.Windows.RoutedEventHandler(this.AlignRight_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.cancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 425 "..\..\..\..\TextFormattingMenu.xaml"
            this.cancelButton.Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.confirmButton = ((System.Windows.Controls.Button)(target));
            
            #line 440 "..\..\..\..\TextFormattingMenu.xaml"
            this.confirmButton.Click += new System.Windows.RoutedEventHandler(this.Confirm_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

