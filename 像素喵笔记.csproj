﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>Resources\F2.ico</ApplicationIcon>

    <!-- 优化的单文件发布配置 -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>false</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>

    <!-- WPF应用不支持代码裁剪，移除相关配置 -->
    <!-- <PublishTrimmed>true</PublishTrimmed> -->
    <!-- <TrimMode>link</TrimMode> -->

    <!-- 启用ReadyToRun以提高启动性能 -->
    <PublishReadyToRun>true</PublishReadyToRun>

    <!-- 优化设置 -->
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <Optimize>true</Optimize>

    <!-- 移除未使用的代码 -->
    <EnableUnsafeBinaryFormatterSerialization>false</EnableUnsafeBinaryFormatterSerialization>
    <!-- 移除InvariantGlobalization以支持本地化功能 -->

    <!-- 应用程序信息 -->
    <AssemblyTitle>像素喵笔记</AssemblyTitle>
    <AssemblyDescription>一个功能丰富的笔记应用程序</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <!-- 只包含必要的资源文件 -->
    <None Remove="Resources\*.png" />
    <None Remove="Resources\*.ico" />
  </ItemGroup>

  <ItemGroup>
    <!-- 只包含实际使用的资源文件 -->
    <Resource Include="Resources\F1.png" />
    <Resource Include="Resources\F2.ico" />
  </ItemGroup>

  <!-- WebView2依赖 - 轻量HTML编辑器所需 -->
  <ItemGroup>
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.2210.55" />
  </ItemGroup>



</Project>
